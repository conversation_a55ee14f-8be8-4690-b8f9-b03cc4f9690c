import { Module } from '@nestjs/common';
import { MisconductSetsService } from './misconduct-sets.service';
import { MisconductSetsController } from './misconduct-sets.controller';
import { PrismaService } from '../prisma.service';

@Module({
  controllers: [MisconductSetsController],
  providers: [MisconductSetsService, PrismaService],
  exports: [MisconductSetsService],
})
export class MisconductSetsModule {}