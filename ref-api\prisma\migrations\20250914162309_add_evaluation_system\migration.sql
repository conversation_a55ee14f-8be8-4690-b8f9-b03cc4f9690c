-- CreateTable
CREATE TABLE "evaluation_questions" (
    "id" INTEGER NOT NULL,
    "user_id" TEXT NOT NULL,
    "question_text" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "evaluation_questions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "match_evaluations" (
    "id" TEXT NOT NULL,
    "match_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "completed_at" TIMESTAMP(3) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "match_evaluations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "evaluation_answers" (
    "id" TEXT NOT NULL,
    "evaluation_id" TEXT NOT NULL,
    "question_id" INTEGER NOT NULL,
    "question_text" TEXT NOT NULL,
    "rating" SMALLINT NOT NULL,
    "label" VARCHAR(50) NOT NULL,
    "emoji" SMALLINT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "evaluation_answers_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "evaluation_answers" ADD CONSTRAINT "evaluation_answers_evaluation_id_fkey" FOREIGN KEY ("evaluation_id") REFERENCES "match_evaluations"("id") ON DELETE CASCADE ON UPDATE CASCADE;
