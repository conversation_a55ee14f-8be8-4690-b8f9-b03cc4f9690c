import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  TextInput,
  Alert,
  ActivityIndicator,
  DeviceEventEmitter,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useUser } from '../contexts/UserContext';
import { useAuth } from '../contexts/AuthContext';
import BroadcastService, { MatchParticipant, SyncInvitation } from '../api/broadcastService';
import MatchService from '../api/matchService';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

interface SyncManagementModalProps {
  visible: boolean;
  matchId: string;
  onClose: () => void;
  onSyncUpdate?: () => void;
}

const ROLE_OPTIONS = [
  { value: 'AR1', label: 'Assistant Referee 1' },
  { value: 'AR2', label: 'Assistant Referee 2' },
  { value: '4th', label: '4th Official' },
];

const SyncManagementModal: React.FC<SyncManagementModalProps> = ({
  visible,
  matchId,
  onClose,
  onSyncUpdate,
}) => {
  const { theme, isDarkMode } = useTheme();
  const { userProfile } = useUser();
  const { user } = useAuth();
  const styles = getStyles(theme, isDarkMode);

  const [participants, setParticipants] = useState<MatchParticipant[]>([]);
  const [matchData, setMatchData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteUsername, setInviteUsername] = useState('');
  const [selectedRole, setSelectedRole] = useState('AR1');
  const [inviteMode, setInviteMode] = useState<'email' | 'username'>('email');
  const [inviting, setInviting] = useState(false);

  // Load participants and match data when modal opens
  useEffect(() => {
    if (visible) {
      console.log('🔄 Modal opened, loading data for match:', matchId);
      loadParticipants();
      loadMatchData();

      // Listen for sync updates
      const handleSyncUpdate = () => {
        console.log('🔄 Sync update received, refreshing participants');
        loadParticipants();
      };

      // Add listener for sync updates
      const unsubscribe = DeviceEventEmitter.addListener('syncUpdate', handleSyncUpdate);

      return () => {
        unsubscribe.remove();
      };
    }
  }, [visible, matchId]);

  // Update selected role when available roles change
  useEffect(() => {
    const availableRoles = getAvailableRoles();
    if (availableRoles.length > 0 && !availableRoles.find(r => r.value === selectedRole)) {
      setSelectedRole(availableRoles[0].value);
    }
  }, [participants]);

  const loadParticipants = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading participants for match:', matchId);
      console.log('🔄 Using BroadcastService.getMatchParticipants...');
      const response = await BroadcastService.getMatchParticipants(matchId);
      console.log('🔍 Raw participants response:', response);

      if (response.success && response.data) {
        console.log('✅ Participants loaded successfully:', response.data);
        console.log('✅ Number of participants:', response.data.length);
        setParticipants(response.data);
      } else {
        console.log('❌ Failed to load participants - response not successful:', response);
        setParticipants([]); // Ensure empty array on failure
      }
    } catch (error) {
      console.error('❌ Error loading participants:', error);
      setParticipants([]); // Ensure empty array on error
      Alert.alert('Error', 'Failed to load match participants');
    } finally {
      setLoading(false);
    }
  };

  const loadMatchData = async () => {
    try {
      console.log('🔄 Loading match data for:', matchId);
      const response = await MatchService.getMatchById(matchId);
      console.log('🔍 Raw match data response:', response);
      if (response.success && response.data) {
        console.log('✅ Match data loaded:', response.data);
        console.log('🔍 Referee info:', {
          referee: response.data.referee
        });
        setMatchData(response.data);
      } else {
        console.log('❌ Failed to load match data:', response);
      }
    } catch (error) {
      console.error('❌ Error loading match data:', error);
    }
  };

  // Helper function to get current user's role
  const getCurrentUserRole = () => {
    if (matchData?.referee?.id === user?.uid) {
      return 'referee';
    }

    const currentUserParticipant = participants.find(p => p.userId === user?.uid);
    return currentUserParticipant?.role || null;
  };

  // Helper function to check if current user is the referee
  const isCurrentUserReferee = () => {
    const isReferee = matchData?.referee?.id === user?.uid;
    console.log('🔍 Checking if current user is referee:', {
      currentUserId: user?.uid,
      refereeId: matchData?.referee?.id,
      isReferee
    });
    return isReferee;
  };

  const handleInviteUser = async () => {
    if (!inviteEmail.trim() && !inviteUsername.trim()) {
      Alert.alert('Error', 'Please enter an email or username');
      return;
    }

    try {
      setInviting(true);
      
      const invitation: SyncInvitation = {
        matchId,
        role: selectedRole,
        ...(inviteMode === 'email' 
          ? { invitedUserEmail: inviteEmail.trim() }
          : { invitedUserUsername: inviteUsername.trim() }
        ),
      };

      const response = await BroadcastService.inviteUser(invitation);
      
      if (response.success) {
        Alert.alert('Success', 'User invited successfully');
        setInviteEmail('');
        setInviteUsername('');
        loadParticipants(); // Refresh participants list
        onSyncUpdate?.();

        // Emit sync update event for real-time updates
        DeviceEventEmitter.emit('syncUpdate');
      } else {
        Alert.alert('Error', response.error || 'Failed to invite user');
      }
    } catch (error: any) {
      console.error('Failed to invite user:', error);
      Alert.alert('Error', error.message || 'Failed to invite user');
    } finally {
      setInviting(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'joined': return '#4CAF50';
      case 'invited': return '#FF9800';
      default: return theme.textSecondary;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'joined': return 'Active';
      case 'invited': return 'Pending';
      default: return status;
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role.toLowerCase()) {
      case 'referee': return 'sports-soccer';
      case 'ar1':
      case 'ar2': return 'flag';
      case '4th': return 'timer';
      default: return 'person';
    }
  };

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'AR1': return 'Assistant Referee 1';
      case 'AR2': return 'Assistant Referee 2';
      case '4th': return '4th Official';
      case 'Referee': return 'Referee';
      default: return role;
    }
  };

  // Get available roles (exclude already assigned roles)
  const getAvailableRoles = () => {
    const assignedRoles = participants
      .filter(p => p.status === 'joined' || p.status === 'invited')
      .map(p => p.role);

    return ROLE_OPTIONS.filter(option => !assignedRoles.includes(option.value));
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={false}
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Match Sync</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <MaterialIcons name="close" size={24} color={theme.text} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Current Participants */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Match Officials</Text>

            {loading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color={theme.primary} />
                <Text style={styles.loadingText}>Loading participants...</Text>
              </View>
            ) : (
              <>
                {/* Show sync button only if current user is the referee */}
                {isCurrentUserReferee() && (
                  <View style={{ padding: 16, borderBottomWidth: 1, borderBottomColor: theme.border }}>
                    <TouchableOpacity style={styles.syncButton} onPress={() => {}}>
                      <Text style={styles.syncButtonText}>Sync with Officials</Text>
                    </TouchableOpacity>
                  </View>
                )}

                {/* Show synced participants */}
                {(() => {
                  console.log('🔍 Rendering participants:', participants);
                  console.log('🔍 Current user ID:', user?.uid);
                  console.log('🔍 Referee ID:', matchData?.referee?.id);
                  return null;
                })()}
                {participants.length > 0 ? (
                  participants.map((participant) => (
                    <View key={participant.id} style={styles.participantCard}>
                      <View style={styles.participantIcon}>
                        <MaterialIcons
                          name={getRoleIcon(participant.role)}
                          size={24}
                          color={theme.primary}
                        />
                      </View>
                      <View style={styles.participantInfo}>
                        <Text style={styles.participantName}>
                          {participant.userId === user?.uid
                            ? `You (${getRoleDisplayName(participant.role)})`
                            : participant.user.displayName || participant.user.name
                          }
                        </Text>
                        <Text style={styles.participantEmail}>{participant.user.email}</Text>
                        <Text style={styles.participantRole}>
                          {getRoleDisplayName(participant.role)}
                        </Text>
                      </View>
                      <View style={styles.participantStatus}>
                        <View
                          style={[
                            styles.statusBadge,
                            { backgroundColor: getStatusColor(participant.status) }
                          ]}
                        >
                          <Text style={styles.statusText}>
                            {getStatusText(participant.status)}
                          </Text>
                        </View>
                      </View>
                    </View>
                  ))
                ) : (
                  <View style={styles.emptyState}>
                    <MaterialIcons name="group-add" size={48} color={theme.textSecondary} />
                    <Text style={styles.emptyStateText}>No assistant officials synced</Text>
                    <Text style={styles.emptyStateSubtext}>
                      Invite other officials to sync with this match
                    </Text>
                  </View>
                )}
              </>
            )}
          </View>

          {/* Invite Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Invite Official</Text>
            
            {/* Invite Mode Toggle */}
            <View style={styles.toggleContainer}>
              <TouchableOpacity
                style={[
                  styles.toggleButton,
                  inviteMode === 'email' && styles.toggleButtonActive
                ]}
                onPress={() => setInviteMode('email')}
              >
                <Text style={[
                  styles.toggleButtonText,
                  inviteMode === 'email' && styles.toggleButtonTextActive
                ]}>
                  Email
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.toggleButton,
                  inviteMode === 'username' && styles.toggleButtonActive
                ]}
                onPress={() => setInviteMode('username')}
              >
                <Text style={[
                  styles.toggleButtonText,
                  inviteMode === 'username' && styles.toggleButtonTextActive
                ]}>
                  Username
                </Text>
              </TouchableOpacity>
            </View>

            {/* Input Field */}
            <TextInput
              style={styles.input}
              placeholder={inviteMode === 'email' ? 'Enter email address' : 'Enter username'}
              placeholderTextColor={theme.textSecondary}
              value={inviteMode === 'email' ? inviteEmail : inviteUsername}
              onChangeText={inviteMode === 'email' ? setInviteEmail : setInviteUsername}
              keyboardType={inviteMode === 'email' ? 'email-address' : 'default'}
              autoCapitalize="none"
              autoCorrect={false}
            />

            {/* Role Selection */}
            <Text style={styles.inputLabel}>Available Roles</Text>
            {getAvailableRoles().length > 0 ? (
              <View style={styles.roleContainer}>
                {getAvailableRoles().map((role) => (
                  <TouchableOpacity
                    key={role.value}
                    style={[
                      styles.roleButton,
                      selectedRole === role.value && styles.roleButtonActive
                    ]}
                    onPress={() => setSelectedRole(role.value)}
                  >
                    <Text style={[
                      styles.roleButtonText,
                      selectedRole === role.value && styles.roleButtonTextActive
                    ]}>
                      {role.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            ) : (
              <View style={styles.noRolesContainer}>
                <MaterialIcons name="info" size={24} color={theme.textSecondary} />
                <Text style={styles.noRolesText}>All official roles are already assigned</Text>
                <Text style={styles.noRolesSubtext}>
                  You can only have one official per role (AR1, AR2, 4th Official)
                </Text>
              </View>
            )}

            {/* Invite Button */}
            <TouchableOpacity
              style={[
                styles.inviteButton,
                (inviting || getAvailableRoles().length === 0) && styles.inviteButtonDisabled
              ]}
              onPress={handleInviteUser}
              disabled={inviting || getAvailableRoles().length === 0}
            >
              {inviting ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <>
                  <MaterialIcons name="send" size={20} color="#fff" />
                  <Text style={styles.inviteButtonText}>Send Invitation</Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.border,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.text,
  },
  closeButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginVertical: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.text,
    marginBottom: 16,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    marginLeft: 10,
    color: theme.textSecondary,
  },
  participantCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  participantIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${theme.primary}20`,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  participantInfo: {
    flex: 1,
  },
  participantName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.text,
  },
  participantEmail: {
    fontSize: 14,
    color: theme.textSecondary,
    marginTop: 2,
  },
  participantRole: {
    fontSize: 12,
    color: theme.primary,
    fontWeight: '500',
    marginTop: 4,
  },
  participantStatus: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#fff',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.text,
    marginTop: 12,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: theme.textSecondary,
    textAlign: 'center',
    marginTop: 4,
  },
  toggleContainer: {
    flexDirection: 'row',
    backgroundColor: theme.card,
    borderRadius: 8,
    padding: 4,
    marginBottom: 16,
  },
  toggleButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 6,
  },
  toggleButtonActive: {
    backgroundColor: theme.primary,
  },
  toggleButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.textSecondary,
  },
  toggleButtonTextActive: {
    color: '#fff',
  },
  input: {
    backgroundColor: theme.card,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: theme.text,
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.text,
    marginBottom: 8,
  },
  roleContainer: {
    marginBottom: 20,
  },
  roleButton: {
    backgroundColor: theme.card,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: theme.border,
  },
  roleButtonActive: {
    backgroundColor: `${theme.primary}20`,
    borderColor: theme.primary,
  },
  roleButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.text,
  },
  roleButtonTextActive: {
    color: theme.primary,
  },
  inviteButton: {
    backgroundColor: theme.primary,
    borderRadius: 8,
    paddingVertical: 14,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  inviteButtonDisabled: {
    opacity: 0.6,
  },
  inviteButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
    marginLeft: 8,
  },
  noRolesContainer: {
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 16,
    backgroundColor: theme.card,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.border,
  },
  noRolesText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.text,
    marginTop: 8,
    textAlign: 'center',
  },
  noRolesSubtext: {
    fontSize: 14,
    color: theme.textSecondary,
    textAlign: 'center',
    marginTop: 4,
  },
  syncButton: {
    backgroundColor: theme.primary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    marginLeft: 8,
  },
  syncButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
});

export default SyncManagementModal;
