-- CreateEnum
CREATE TYPE "UserRole" AS ENUM ('REFEREE', 'AR1', 'AR2', 'FOURTH_OFFICIAL', 'ASSESSOR', 'ADMIN', 'MANAGER');

-- CreateEnum
CREATE TYPE "MatchStatus" AS ENUM ('SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "TeamType" AS ENUM ('HOME', 'AWAY');

-- CreateEnum
CREATE TYPE "PlayerPosition" AS ENUM ('STARTER', 'SUBSTITUTE');

-- CreateTable
CREATE TABLE "users" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "displayName" TEXT,
    "photoURL" TEXT,
    "firebaseUid" TEXT,
    "emailVerified" BOOLEAN NOT NULL DEFAULT false,
    "provider" TEXT NOT NULL DEFAULT 'email',
    "lastLoginAt" TIMESTAMP(3),
    "role" "UserRole" NOT NULL DEFAULT 'REFEREE',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "matches" (
    "id" TEXT NOT NULL,
    "competition" TEXT NOT NULL,
    "venue" TEXT NOT NULL,
    "matchDate" TIMESTAMP(3) NOT NULL,
    "officialRole" TEXT NOT NULL DEFAULT 'Referee',
    "homeTeamName" TEXT NOT NULL,
    "homeTeamShortName" TEXT,
    "homeKitBase" TEXT NOT NULL DEFAULT '#FF0000',
    "homeKitStripe" TEXT NOT NULL DEFAULT '#00FFFF',
    "awayTeamName" TEXT NOT NULL,
    "awayTeamShortName" TEXT,
    "awayKitBase" TEXT NOT NULL DEFAULT '#FFFFFF',
    "awayKitStripe" TEXT NOT NULL DEFAULT '#0000FF',
    "teamSize" INTEGER NOT NULL DEFAULT 11,
    "benchSize" INTEGER NOT NULL DEFAULT 5,
    "numberOfPeriods" INTEGER NOT NULL DEFAULT 2,
    "periodLengths" JSONB NOT NULL,
    "halfTime" INTEGER NOT NULL DEFAULT 15,
    "extraTime" BOOLEAN NOT NULL DEFAULT false,
    "extraTimeLengths" JSONB,
    "subOpportunities" BOOLEAN NOT NULL DEFAULT false,
    "subOpportunitiesAllowance" INTEGER NOT NULL DEFAULT 1,
    "extraTimeSubOpportunities" BOOLEAN NOT NULL DEFAULT false,
    "extraTimeSubOpportunitiesAllowance" INTEGER NOT NULL DEFAULT 1,
    "penalties" BOOLEAN NOT NULL DEFAULT false,
    "misconductCode" TEXT NOT NULL DEFAULT 'England',
    "temporaryDismissals" BOOLEAN NOT NULL DEFAULT false,
    "temporaryDismissalsTime" INTEGER NOT NULL DEFAULT 10,
    "injuryTimeAllowance" BOOLEAN NOT NULL DEFAULT false,
    "injuryTimeSubs" INTEGER NOT NULL DEFAULT 0,
    "injuryTimeSanctions" INTEGER NOT NULL DEFAULT 0,
    "injuryTimeGoals" INTEGER NOT NULL DEFAULT 0,
    "matchOfficials" JSONB NOT NULL,
    "fees" TEXT,
    "expenses" TEXT,
    "mileage" TEXT,
    "travelTime" TEXT,
    "notes" TEXT,
    "templateName" TEXT,
    "status" "MatchStatus" NOT NULL DEFAULT 'SCHEDULED',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "refereeId" TEXT NOT NULL,

    CONSTRAINT "matches_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "assessments" (
    "id" TEXT NOT NULL,
    "score" INTEGER NOT NULL,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "matchId" TEXT NOT NULL,
    "assessorId" TEXT NOT NULL,

    CONSTRAINT "assessments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "squads" (
    "id" TEXT NOT NULL,
    "teamType" "TeamType" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "matchId" TEXT NOT NULL,

    CONSTRAINT "squads_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "squad_players" (
    "id" TEXT NOT NULL,
    "number" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "position" "PlayerPosition" NOT NULL,
    "isCaptain" BOOLEAN NOT NULL DEFAULT false,
    "isGoalkeeper" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "squadId" TEXT NOT NULL,

    CONSTRAINT "squad_players_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "squad_officials" (
    "id" TEXT NOT NULL,
    "role" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "squadId" TEXT NOT NULL,

    CONSTRAINT "squad_officials_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "users_firebaseUid_key" ON "users"("firebaseUid");

-- CreateIndex
CREATE UNIQUE INDEX "squads_matchId_teamType_key" ON "squads"("matchId", "teamType");

-- AddForeignKey
ALTER TABLE "matches" ADD CONSTRAINT "matches_refereeId_fkey" FOREIGN KEY ("refereeId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assessments" ADD CONSTRAINT "assessments_matchId_fkey" FOREIGN KEY ("matchId") REFERENCES "matches"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "assessments" ADD CONSTRAINT "assessments_assessorId_fkey" FOREIGN KEY ("assessorId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "squads" ADD CONSTRAINT "squads_matchId_fkey" FOREIGN KEY ("matchId") REFERENCES "matches"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "squad_players" ADD CONSTRAINT "squad_players_squadId_fkey" FOREIGN KEY ("squadId") REFERENCES "squads"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "squad_officials" ADD CONSTRAINT "squad_officials_squadId_fkey" FOREIGN KEY ("squadId") REFERENCES "squads"("id") ON DELETE CASCADE ON UPDATE CASCADE;
