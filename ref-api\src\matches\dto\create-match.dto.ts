import { 
  IsString, 
  IsOptional, 
  IsBoolean, 
  IsInt, 
  IsArray, 
  IsDateString, 
  Min, 
  Max,
  Length,
  Matches,
  ValidateNested,
  ArrayMaxSize
} from 'class-validator';
import { Type, Transform } from 'class-transformer';

export class CreateMatchDto {
  // Basic match info
  @IsString()
  @Length(1, 200, { message: 'Competition name must be between 1 and 200 characters' })
  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  competition: string;

  @IsString()
  @Length(1, 200, { message: 'Venue must be between 1 and 200 characters' })
  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  venue: string;

  @IsDateString({}, { message: 'Match date must be a valid ISO date string' })
  matchDate: string;

  @IsOptional()
  @IsString()
  @Length(1, 50, { message: 'Official role must be between 1 and 50 characters' })
  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  officialRole?: string = 'Referee';

  // Home team
  @IsString()
  @Length(1, 100, { message: 'Home team name must be between 1 and 100 characters' })
  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  homeTeamName: string;

  @IsOptional()
  @IsString()
  @Length(1, 10, { message: 'Home team short name must be between 1 and 10 characters' })
  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  homeTeamShortName?: string;

  @IsOptional()
  @IsString()
  @Matches(/^#[0-9A-Fa-f]{6}$/, { message: 'Home kit base must be a valid hex color' })
  homeKitBase?: string = '#FF0000';

  @IsOptional()
  @IsString()
  @Matches(/^#[0-9A-Fa-f]{6}$/, { message: 'Home kit stripe must be a valid hex color' })
  homeKitStripe?: string = '#00FFFF';

  // Away team
  @IsString()
  @Length(1, 100, { message: 'Away team name must be between 1 and 100 characters' })
  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  awayTeamName: string;

  @IsOptional()
  @IsString()
  @Length(1, 10, { message: 'Away team short name must be between 1 and 10 characters' })
  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  awayTeamShortName?: string;

  @IsOptional()
  @IsString()
  @Matches(/^#[0-9A-Fa-f]{6}$/, { message: 'Away kit base must be a valid hex color' })
  awayKitBase?: string = '#FFFFFF';

  @IsOptional()
  @IsString()
  @Matches(/^#[0-9A-Fa-f]{6}$/, { message: 'Away kit stripe must be a valid hex color' })
  awayKitStripe?: string = '#0000FF';

  // Match format
  @IsOptional()
  @IsInt({ message: 'Team size must be an integer' })
  @Min(1, { message: 'Team size must be at least 1' })
  @Max(22, { message: 'Team size must be at most 22' })
  teamSize?: number = 11;


  @IsOptional()
  @IsBoolean()
  rollOnRollOff?: boolean = false;

  @IsOptional()
  @IsInt({ message: 'Max substitute must be an integer' })
  @Min(0, { message: 'Max substitute must be at least 0' })
  @Max(15, { message: 'Max substitute must be at most 15' })
  maxSubstitute?: number = 5;

  @IsOptional()
  @IsInt({ message: 'Number of periods must be an integer' })
  @Min(1, { message: 'Number of periods must be at least 1' })
  @Max(4, { message: 'Number of periods must be at most 4' })
  numberOfPeriods?: number = 2;

  @IsOptional()
  @IsArray({ message: 'Period lengths must be an array' })
  @ArrayMaxSize(4, { message: 'Period lengths array can have at most 4 elements' })
  periodLengths?: number[] = [45, 45];

  @IsOptional()
  @IsInt({ message: 'Half time must be an integer' })
  @Min(0, { message: 'Half time must be at least 0 minutes' })
  @Max(60, { message: 'Half time must be at most 60 minutes' })
  halfTime?: number = 15;

  // Extra time settings
  @IsOptional()
  @IsBoolean()
  extraTime?: boolean = false;

  @IsOptional()
  @IsArray()
  extraTimeLengths?: [number, number];

  // Substitution rules
  @IsOptional()
  @IsBoolean()
  substituteOpportunities?: boolean = false;

  @IsOptional()
  @IsInt()
  @Min(0)
  substituteOpportunitiesAllowance?: number = 1;

  @IsOptional()
  @IsBoolean()
  extraTimeSubOpportunities?: boolean = false;

  @IsOptional()
  @IsInt()
  @Min(0)
  extraTimeSubOpportunitiesAllowance?: number = 1;

  @IsOptional()
  @IsBoolean()
  extraTimeAdditionalSub?: boolean = false;

  @IsOptional()
  @IsInt()
  @Min(0)
  extraTimeAdditionalSubAllowance?: number = 1;

  

  // Other rules
  @IsOptional()
  @IsBoolean()
  penalties?: boolean = false;

  @IsOptional()
  @IsString()
  misconductCode?: string = 'England';

  @IsOptional()
  @IsBoolean()
  temporaryDismissals?: boolean = false;

  @IsOptional()
  @IsInt()
  @Min(0)
  temporaryDismissalsTime?: number = 10;

  // Injury time
  @IsOptional()
  @IsBoolean()
  injuryTimeAllowance?: boolean = false;

  @IsOptional()
  @IsInt()
  @Min(0)
  injuryTimeSubs?: number = 0;

  @IsOptional()
  @IsInt()
  @Min(0)
  injuryTimeSanctions?: number = 0;

  @IsOptional()
  @IsInt()
  @Min(0)
  injuryTimeGoals?: number = 0;

  // Match officials
  @IsOptional()
  @IsArray()
  matchOfficials?: Array<{ role: string; name: string }>;

  // Earnings
  @IsOptional()
  @IsString()
  fees?: string;

  @IsOptional()
  @IsString()
  expenses?: string;

  @IsOptional()
  @IsString()
  mileage?: string;

  @IsOptional()
  @IsString()
  travelTime?: string;

  // Notes
  @IsOptional()
  @IsString()
  notes?: string;

  // Template info
  @IsOptional()
  @IsString()
  templateName?: string;
}
