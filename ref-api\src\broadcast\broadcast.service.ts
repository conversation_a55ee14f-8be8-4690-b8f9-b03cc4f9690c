import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRedis } from '@nestjs-modules/ioredis';
import Redis from 'ioredis';

export interface MatchAction {
  id: string;
  type: 'card' | 'goal' | 'substitution' | 'period_start' | 'period_end' | 'match_start' | 'match_end';
  matchId: string;
  userId: string; // User who performed the action
  timestamp: number;
  realTime: Date;
  data: any; // Action-specific data
}

export interface SyncNotification {
  type: 'match_start' | 'role_assigned' | 'action_broadcast' | 'sync_invite' | 'sync_update';
  matchId: string;
  fromUserId: string;
  data: any;
}

@Injectable()
export class BroadcastService {
  private readonly logger = new Logger(BroadcastService.name);
  private broadcastGateway: any;

  constructor(@InjectRedis() private readonly redis: Redis) {}

  // Setter for gateway (to avoid circular dependency)
  setBroadcastGateway(gateway: any) {
    this.broadcastGateway = gateway;
  }

  /**
   * Broadcast match action to all synced participants
   */
  async broadcastMatchAction(action: MatchAction): Promise<void> {
    try {
      const streamKey = `match:${action.matchId}:actions`;
      
      // Add to Redis Stream
      await this.redis.xadd(
        streamKey,
        '*', // Auto-generate ID
        'type', action.type,
        'userId', action.userId,
        'timestamp', action.timestamp.toString(),
        'realTime', action.realTime.toISOString(),
        'data', JSON.stringify(action.data),
        'actionId', action.id
      );

      this.logger.log(`📡 Broadcasted ${action.type} action for match ${action.matchId}`);
    } catch (error) {
      this.logger.error(`❌ Failed to broadcast action: ${error.message}`);
      throw error;
    }
  }

  /**
   * Send notification to specific user
   */
  async sendUserNotification(userId: string, notification: SyncNotification): Promise<void> {
    try {
      // Try to send via WebSocket first
      let sentViaWebSocket = false;
      if (this.broadcastGateway) {
        sentViaWebSocket = this.broadcastGateway.sendDirectNotification(userId, notification);
      }

      // Always save to Redis Stream as backup
      const streamKey = `user:${userId}:notifications`;

      await this.redis.xadd(
        streamKey,
        '*',
        'type', notification.type,
        'matchId', notification.matchId,
        'fromUserId', notification.fromUserId,
        'data', JSON.stringify(notification.data),
        'timestamp', Date.now().toString()
      );

      this.logger.log(`🔔 Sent ${notification.type} notification to user ${userId} (WebSocket: ${sentViaWebSocket ? 'Yes' : 'No'})`);
    } catch (error) {
      this.logger.error(`❌ Failed to send notification: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get match actions from stream (for catching up)
   */
  async getMatchActions(matchId: string, fromId: string = '0'): Promise<any[]> {
    try {
      const streamKey = `match:${matchId}:actions`;
      
      const result = await this.redis.xread('STREAMS', streamKey, fromId);
      
      if (!result || result.length === 0) {
        return [];
      }

      const [, messages] = result[0];
      return messages.map(([id, fields]) => {
        const fieldObj: any = {};
        for (let i = 0; i < fields.length; i += 2) {
          fieldObj[fields[i]] = fields[i + 1];
        }
        
        return {
          id,
          type: fieldObj.type,
          userId: fieldObj.userId,
          timestamp: parseInt(fieldObj.timestamp),
          realTime: new Date(fieldObj.realTime),
          data: JSON.parse(fieldObj.data || '{}'),
          actionId: fieldObj.actionId
        };
      });
    } catch (error) {
      this.logger.error(`❌ Failed to get match actions: ${error.message}`);
      return [];
    }
  }

  /**
   * Get user notifications from stream
   */
  async getUserNotifications(userId: string, fromId: string = '0'): Promise<any[]> {
    try {
      const streamKey = `user:${userId}:notifications`;
      
      const result = await this.redis.xread('STREAMS', streamKey, fromId);
      
      if (!result || result.length === 0) {
        return [];
      }

      const [, messages] = result[0];
      return messages.map(([id, fields]) => {
        const fieldObj: any = {};
        for (let i = 0; i < fields.length; i += 2) {
          fieldObj[fields[i]] = fields[i + 1];
        }
        
        return {
          id,
          type: fieldObj.type,
          matchId: fieldObj.matchId,
          fromUserId: fieldObj.fromUserId,
          data: JSON.parse(fieldObj.data || '{}'),
          timestamp: parseInt(fieldObj.timestamp)
        };
      });
    } catch (error) {
      this.logger.error(`❌ Failed to get user notifications: ${error.message}`);
      return [];
    }
  }

  /**
   * Clean up old stream entries (optional - for memory management)
   */
  async cleanupOldEntries(streamKey: string, maxLength: number = 1000): Promise<void> {
    try {
      await this.redis.xtrim(streamKey, 'MAXLEN', '~', maxLength);
      this.logger.log(`🧹 Cleaned up stream ${streamKey}`);
    } catch (error) {
      this.logger.error(`❌ Failed to cleanup stream: ${error.message}`);
    }
  }

  /**
   * Create consumer group for match actions (for reliable processing)
   */
  async createConsumerGroup(matchId: string, groupName: string = 'processors'): Promise<void> {
    try {
      const streamKey = `match:${matchId}:actions`;
      await this.redis.xgroup('CREATE', streamKey, groupName, '0', 'MKSTREAM');
      this.logger.log(`👥 Created consumer group ${groupName} for match ${matchId}`);
    } catch (error) {
      // Group might already exist
      if (!error.message.includes('BUSYGROUP')) {
        this.logger.error(`❌ Failed to create consumer group: ${error.message}`);
      }
    }
  }
}
