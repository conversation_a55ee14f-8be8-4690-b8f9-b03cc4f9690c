import { Injectable } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { RedisService } from './redis/redis.service';
import { S3Service } from './s3/s3.service';

@Injectable()
export class AppService {
  constructor(
    private prisma: PrismaService,
    private redis: RedisService,
    private s3: S3Service,
  ) {}

  getHello(): string {
    return 'Hello World!';
  }

  // Example method to test database connection
  async testDatabase(): Promise<string> {
    try {
      // Test database connection by checking if we can connect
      await this.prisma.$connect();
      return 'Database connection successful!';
    } catch (error) {
      return `Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }

  // Example method to test Redis connection
  async testRedis(): Promise<string> {
    try {
      await this.redis.set('test', 'Hello Redis!', 60);
      const value = await this.redis.get('test');
      return `Redis connection successful! Value: ${value}`;
    } catch (error) {
      return `Redis connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }

  // Example method to test S3/MinIO connection
  testS3(): string {
    try {
      // This will test the S3 client initialization
      this.s3.getClient();
      return 'S3/MinIO client initialized successfully!';
    } catch (error) {
      return `S3/MinIO connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
}
console.log("Volume mount test");
