import type { Auth } from 'firebase/auth';

// Import and properly type the auth instance
const auth = require('../firebaseConfig').auth as Auth;

const API_BASE_URL = __DEV__
  ? 'http://192.168.1.101:3000'  // Development - your local API
  : 'https://your-production-api.com'; // Production URL when you deploy

export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  status: number;
}

class ApiService {
  private baseURL: string;

  constructor() {
    this.baseURL = API_BASE_URL;
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    try {
      const user = auth.currentUser;
      if (user) {
        const token = await user.getIdToken();
        return {
          'Authorization': `Bearer ${token}`,
        };
      }
    } catch (error) {
      console.error('Error getting auth token:', error);
    }
    return {};
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const authHeaders = await this.getAuthHeaders();
      const url = `${this.baseURL}${endpoint}`;

      console.log(`Making ${options.method || 'GET'} request to:`, url);

      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...authHeaders,
          ...options.headers,
        },
        ...options,
      });

      console.log(`Response status: ${response.status}`);

      let data;
      try {
        data = await response.json();
      } catch (parseError) {
        console.error('Error parsing response JSON:', parseError);
        data = null;
      }

      console.log('Response data:', data);

      return {
        data,
        status: response.status,
      };
    } catch (error) {
      console.error('API request error:', error);
      return {
        error: error instanceof Error ? error.message : 'Unknown error',
        status: 0,
      };
    }
  }

  // Health check
  async healthCheck(): Promise<ApiResponse> {
    return this.request('/health');
  }

  // Test endpoints
  async testDatabase(): Promise<ApiResponse> {
    return this.request('/test/database');
  }

  async testRedis(): Promise<ApiResponse> {
    return this.request('/test/redis');
  }

  async testS3(): Promise<ApiResponse> {
    return this.request('/test/s3');
  }

  // GET request
  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  // POST request
  async post<T>(endpoint: string, data: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // PUT request
  async put<T>(endpoint: string, data: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // DELETE request
  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

export const apiService = new ApiService();
export default apiService;
