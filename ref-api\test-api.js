const axios = require('axios');

async function testAPI() {
  try {
    console.log('🧪 Testing API endpoints...');
    
    // Test health check (no auth required)
    try {
      const healthResponse = await axios.get('http://localhost:3000/health');
      console.log('✅ Health check:', healthResponse.status);
    } catch (error) {
      console.log('❌ Health check failed:', error.message);
    }
    
    // Test users profile endpoint (requires auth)
    try {
      const profileResponse = await axios.get('http://localhost:3000/users/profile');
      console.log('❌ Profile endpoint should require auth but didn\'t');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Profile endpoint correctly requires authentication');
      } else if (error.response?.status === 404) {
        console.log('❌ Profile endpoint not found (404) - this is the issue!');
      } else {
        console.log('❌ Profile endpoint error:', error.response?.status, error.message);
      }
    }
    
    // Test matches endpoints (requires auth)
    try {
      const matchesResponse = await axios.get('http://localhost:3000/matches');
      console.log('❌ Matches endpoint should require auth but didn\'t');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Matches endpoint correctly requires authentication');
      } else {
        console.log('❌ Matches endpoint error:', error.response?.status, error.message);
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testAPI();