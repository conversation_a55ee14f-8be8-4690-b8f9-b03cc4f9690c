-- CreateTable
CREATE TABLE "match_logs" (
    "id" TEXT NOT NULL,
    "type" VARCHAR(50) NOT NULL,
    "timestamp" INTEGER NOT NULL,
    "realTime" TIMESTAMP(3) NOT NULL,
    "data" JSONB,
    "matchId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "match_logs_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "match_logs" ADD CONSTRAINT "match_logs_matchId_fkey" FOREIGN KEY ("matchId") REFERENCES "matches"("id") ON DELETE CASCADE ON UPDATE CASCADE;