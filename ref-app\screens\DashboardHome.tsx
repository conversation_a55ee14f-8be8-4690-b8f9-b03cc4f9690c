import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation, useFocusEffect, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Header from '../components/Header';
import Svg, { Defs, ClipPath, Path, G, Rect } from 'react-native-svg';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import MatchService from '../api/matchService';
import { apiClient } from '../api/client';

const { width } = Dimensions.get('window');

// --- INTERFACES AND TYPES ---
type Team = {
  name: string;
  kitColor: string;
  kitStripe?: string;
};

type Match = {
  id?: string;
  division: string;
  role: string;
  homeTeam: Team;
  awayTeam: Team;
  location: string;
  date: string;
  time: string;
  homeScore?: number;
  awayScore?: number;
  status?: string;
};

interface StripedTshirtProps {
  baseColor: string;
  stripeColor?: string;
  size?: number;
}

interface Filters {
  competitions?: string[];
  venues?: string[];
  teams?: string[];
  matchOfficials?: string[];
  roles?: string[];
  minYellowCards?: number;
  minRedCards?: number;
  minGoals?: number;
  hadExtraTime?: boolean;
  wasAbandoned?: boolean;
  applyStatsToAll?: boolean;
}

type RootStackParamList = {
  Dashboard: { screen?: string; params?: { filters?: Filters } } | undefined;
  FilterMatches: undefined;
  CreateMatch: undefined;
  MatchResults: { matchId: string };
  MatchDetail: { matchId: string };
};

type TabParamList = {
  DashboardHome: { filters?: Filters } | undefined;
  Calendar: undefined;
  Evaluate: undefined;
  Manage: undefined;
};

type DashboardHomeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Dashboard'>;
type DashboardHomeScreenRouteProp = RouteProp<TabParamList, 'DashboardHome'>;

// --- DATA ---
const fallbackUpcomingMatches: Match[] = [
  { division: "East Gwent Division", role: "Referee", homeTeam: { name: "Town MAN Reserves", kitColor: "#0066cc", kitStripe: "#ffffff" }, awayTeam: { name: "Chepstow Town PSG 3rd Team Dot dave playeing", kitColor: "#cc0000", kitStripe: "#000000" }, location: "Manhattan City", date: "03/15/25", time: "15:30" },
  { division: "East Gwent Division", role: "Ar2", homeTeam: { name: "Monmouth Town FC Reserves", kitColor: "#9933cc", kitStripe: "#ffff00" }, awayTeam: { name: "Manchester United", kitColor: "#66cc00", kitStripe: "#ffffff" }, location: "New York City", date: "03/20/25", time: "14:00" },
];

const fallbackPreviousMatches: Match[] = [
  { division: "West Gwent Division", role: "Referee", homeTeam: { name: "Caldicot Town", kitColor: "#ff9900", kitStripe: "#000000" }, awayTeam: { name: "Chepstow Town PSG 3rd Team", kitColor: "#003366", kitStripe: "#ffffff" }, location: "Jubilee Way", date: "04.15.25", time: "14:00", homeScore: 2, awayScore: 4 },
  { division: "Gwent County League", role: "Ar1", homeTeam: { name: "Abercarn United", kitColor: "#000000", kitStripe: "#ffffff" }, awayTeam: { name: "Blaenavon Blues", kitColor: "#3399ff", kitStripe: "#ffff00" }, location: "The Cwmcarn", date: "04.08.25", time: "18:30", homeScore: 3, awayScore: 1 },
];

// --- SVG T-SHIRT COMPONENT ---
const StripedTshirt: React.FC<StripedTshirtProps> = ({ baseColor, stripeColor = '#d60a2e', size = 36 }) => (
  <Svg width={size} height={size} viewBox="-1 0 19 19">
    <Defs><ClipPath id="tshirtClip"><Path d="m15.867 7.593-1.534.967a.544.544 0 0 1-.698-.118l-.762-.957v7.256a.476.476 0 0 1-.475.475h-7.79a.476.476 0 0 1-.475-.475V7.477l-.769.965a.544.544 0 0 1-.697.118l-1.535-.967a.387.387 0 0 1-.083-.607l2.245-2.492a2.814 2.814 0 0 1 2.092-.932h.935a2.374 2.374 0 0 0 4.364 0h.934a2.816 2.816 0 0 1 2.093.933l2.24 2.49a.388.388 0 0 1-.085.608z" /></ClipPath></Defs>
    <Path d="m15.867 7.593-1.534.967a.544.544 0 0 1-.698-.118l-.762-.957v7.256a.476.476 0 0 1-.475.475h-7.79a.476.476 0 0 1-.475-.475V7.477l-.769.965a.544.544 0 0 1-.697.118l-1.535-.967a.387.387 0 0 1-.083-.607l2.245-2.492a2.814 2.814 0 0 1 2.092-.932h.935a2.374 2.374 0 0 0 4.364 0h.934a2.816 2.816 0 0 1 2.093.933l2.24 2.49a.388.388 0 0 1-.085.608z" fill={baseColor} />
    <G clipPath="url(#tshirtClip)">
      {[3.5, 6, 8.5, 11, 13.5].map(x => (
        <Rect key={x} x={x} y="3" width="1.2" height="12" fill={stripeColor} />
      ))}
    </G>
  </Svg>
);

// --- MAIN DASHBOARD COMPONENT ---
const UserDashboard = () => {
  const navigation = useNavigation<DashboardHomeScreenNavigationProp>();
  const route = useRoute<DashboardHomeScreenRouteProp>();
  const { theme, isDarkMode } = useTheme();
  const { user, loading: authLoading } = useAuth();
  const styles = getStyles(theme, isDarkMode);
  const [activeTab, setActiveTab] = useState('upcoming');

  // State for API data
  const [upcomingMatches, setUpcomingMatches] = useState<Match[]>([]);
  const [previousMatches, setPreviousMatches] = useState<Match[]>([]);
  const [filteredUpcomingMatches, setFilteredUpcomingMatches] = useState<Match[]>([]);
  const [filteredPreviousMatches, setFilteredPreviousMatches] = useState<Match[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isFiltering, setIsFiltering] = useState(false);
  const [matchScores, setMatchScores] = useState<Record<string, { home: number, away: number }>>({});

  // State for countdown timer
  const [countdown, setCountdown] = useState({ days: 0, hours: 0, minutes: 0 });
  const [nextMatch, setNextMatch] = useState<Match | null>(null);

  const allMatches = useMemo(() => [...upcomingMatches, ...previousMatches], [upcomingMatches, previousMatches]);
  const allCompetitions = useMemo(() => [...new Set(allMatches.map(m => m.division).filter(Boolean))], [allMatches]);
  const allVenues = useMemo(() => [...new Set(allMatches.map(m => m.location).filter(Boolean))], [allMatches]);
  const allTeams = useMemo(() => [...new Set(allMatches.flatMap(m => [m.homeTeam.name, m.awayTeam.name]).filter(Boolean))], [allMatches]);
  const allRoles = useMemo(() => [...new Set(allMatches.map(m => m.role).filter(Boolean))], [allMatches]);

  useEffect(() => {
    const filters = route.params?.filters;

    if (filters && Object.keys(filters).some(key => filters[key as keyof Filters] !== undefined)) {
      // Apply filters using API
      applyFilters(filters);
    } else {
      // No filters, use original matches
      setFilteredUpcomingMatches(upcomingMatches);
      setFilteredPreviousMatches(previousMatches);
    }

  }, [upcomingMatches, previousMatches, route.params?.filters]);

  const applyFilters = async (filters: Filters) => {
    try {
      setIsFiltering(true);
      console.log('🎯 Applying filters:', filters);
      
      // Create filters for upcoming matches (without match statistics)
      const upcomingFilters = {
        competitions: filters.competitions,
        venues: filters.venues,
        teams: filters.teams,
        matchOfficials: filters.matchOfficials,
        roles: filters.roles,
        // No match statistics filters for upcoming matches
      };
      
      // Create filters for previous matches (with all filters including statistics)
      const previousFilters = { ...filters };
      
      // Get filtered upcoming matches (basic filters only)
      let filteredUpcoming = upcomingMatches;
      if (Object.values(upcomingFilters).some(val => val !== undefined)) {
        const upcomingResponse = await MatchService.getFilteredMatches(upcomingFilters);
        if (upcomingResponse.success && upcomingResponse.data) {
          const converted = MatchService.convertApiResponseToDashboardFormat(upcomingResponse.data);
          filteredUpcoming = converted.filter(match => 
            match.status === 'SCHEDULED' || match.status === 'IN_PROGRESS'
          );
        }
      }
      
      // Get filtered previous matches (all filters including statistics)
      let filteredPrevious = previousMatches;
      if (Object.values(filters).some(val => val !== undefined)) {
        const previousResponse = await MatchService.getFilteredMatches(previousFilters);
        if (previousResponse.success && previousResponse.data) {
          const converted = MatchService.convertApiResponseToDashboardFormat(previousResponse.data);
          filteredPrevious = converted.filter(match => 
            match.status === 'COMPLETED' || match.status === 'CANCELLED'
          );
        }
      }
      
      setFilteredUpcomingMatches(filteredUpcoming);
      setFilteredPreviousMatches(filteredPrevious);
      
      console.log('✅ Filter results:', {
        upcoming: filteredUpcoming.length,
        previous: filteredPrevious.length
      });
      
    } catch (error) {
      console.error('💥 Error applying filters:', error);
      // Fallback to original matches
      setFilteredUpcomingMatches(upcomingMatches);
      setFilteredPreviousMatches(previousMatches);
    } finally {
      setIsFiltering(false);
    }
  };

  // Calculate countdown to next match
  const calculateCountdown = (matches: Match[]) => {
    if (matches.length === 0) {
      setCountdown({ days: 0, hours: 0, minutes: 0 });
      setNextMatch(null);
      return;
    }

    // Sort matches by date/time to get the next one
    const sortedMatches = [...matches].sort((a, b) => {
      const dateA = parseMatchDateTime(a.date, a.time);
      const dateB = parseMatchDateTime(b.date, b.time);
      return dateA.getTime() - dateB.getTime();
    });

    const nextUpcomingMatch = sortedMatches[0];
    setNextMatch(nextUpcomingMatch);

    const matchDateTime = parseMatchDateTime(nextUpcomingMatch.date, nextUpcomingMatch.time);
    const now = new Date();
    const timeDiff = matchDateTime.getTime() - now.getTime();

    if (timeDiff <= 0) {
      // Match has passed, set countdown to zero and find next match
      setCountdown({ days: 0, hours: 0, minutes: 0 });
      // Remove the passed match and try the next one
      const remainingMatches = sortedMatches.slice(1);
      if (remainingMatches.length > 0) {
        calculateCountdown(remainingMatches);
      } else {
        setNextMatch(null);
      }
      return;
    }

    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

    setCountdown({ days, hours, minutes });
  };

  // Parse match date and time string to Date object
  const parseMatchDateTime = (dateStr: string, timeStr: string): Date => {
    try {
      // Add a guard clause to handle undefined or null inputs
      if (!dateStr || !timeStr) {
        console.error('Error parsing date/time: [TypeError: Cannot read property \'includes\' of undefined]', { dateStr, timeStr });
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        return tomorrow;
      }

      // Handle both formats: "DD/MM/YY" and "DD.MM.YY"
      let dateParts;
      if (dateStr.includes('/')) {
        dateParts = dateStr.split('/');
      } else if (dateStr.includes('.')) {
        dateParts = dateStr.split('.');
      } else {
        console.error('❌ Unsupported date format:', dateStr);
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        return tomorrow;
      }

      const [day, month, year] = dateParts; // DD/MM/YY format
      const timeParts = timeStr.split(':');
      const [hours, minutes] = timeParts;

      // Validate that we have all parts
      if (dateParts.length !== 3 || timeParts.length !== 2) {
        console.error('❌ Invalid date/time format:', { dateStr, timeStr });
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        return tomorrow;
      }

      // Convert strings to numbers
      const dayNum = parseInt(day, 10);
      const monthNum = parseInt(month, 10);
      const yearNum = parseInt(year, 10);
      const hoursNum = parseInt(hours, 10);
      const minutesNum = parseInt(minutes, 10);

      // Convert 2-digit year to 4-digit (assuming 20XX for years 00-99)
      const fullYear = yearNum < 100 ? 2000 + yearNum : yearNum;

      // Create date object (month is 0-indexed in JavaScript)
      const matchDate = new Date(fullYear, monthNum - 1, dayNum, hoursNum, minutesNum, 0, 0);

      // Validate the created date
      if (isNaN(matchDate.getTime())) {
        console.error('❌ Invalid date created from:', { dateStr, timeStr });
        // Return a future date as fallback
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        return tomorrow;
      }

      return matchDate;
    } catch (error) {
      console.error('Error parsing date/time:', error, { dateStr, timeStr });
      // Return a future date as fallback
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      return tomorrow;
    }
  };

  // Fetch match scores from match events
  const fetchMatchScores = async (matchIds: string[]) => {
    const scores: Record<string, { home: number, away: number }> = {};
    
    for (const matchId of matchIds) {
      if (!matchId) continue;
      
      try {
        const logsResponse = await MatchService.getMatchLogs(matchId);
        if (logsResponse.success && logsResponse.data) {
          const homeGoals = logsResponse.data.filter((log: any) => 
            log.type === 'goal' && log.data?.team === 'home'
          ).length;
          const awayGoals = logsResponse.data.filter((log: any) => 
            log.type === 'goal' && log.data?.team === 'away'
          ).length;
          
          scores[matchId] = { home: homeGoals, away: awayGoals };
          console.log(`Scores for match ${matchId}: ${homeGoals} - ${awayGoals}`);
        }
      } catch (error) {
        console.error(`Error fetching scores for match ${matchId}:`, error);
      }
    }
    
    setMatchScores(scores);
    return scores;
  };

  // Fetch matches data
  const fetchMatches = async (showLoading = true) => {
    try {
      if (showLoading) setIsLoading(true);
      setError(null);

      // Check if user is authenticated before making API calls
      if (!user) {
        console.log('⚠️ User not authenticated, skipping API calls');
        setUpcomingMatches(fallbackUpcomingMatches);
        setPreviousMatches(fallbackPreviousMatches);
        calculateCountdown(fallbackUpcomingMatches);
        return;
      }

      console.log('🔄 Fetching matches for authenticated user:', user.uid);

      // Test API connectivity and auth first
      try {
        const healthCheck = await apiClient.healthCheck();
        console.log('🏥 API Health Check:', healthCheck.success ? 'OK' : 'Failed');
      } catch (error) {
        console.error('🏥 API Health Check Failed:', error);
      }

      // Check if auth token is set
      console.log('🔑 Has Auth Token:', apiClient.hasAuthToken());

      // Fetch upcoming and previous matches
      const [upcomingResponse, previousResponse] = await Promise.all([
        MatchService.getUpcomingMatches(),
        MatchService.getPreviousMatches()
      ]);

      if (upcomingResponse.success && upcomingResponse.data) {
        const formattedUpcoming = MatchService.convertApiResponseToDashboardFormat(upcomingResponse.data);
        setUpcomingMatches(formattedUpcoming);
        calculateCountdown(formattedUpcoming);
      } else {
        console.warn('Failed to fetch upcoming matches:', upcomingResponse.error);
        setUpcomingMatches(fallbackUpcomingMatches);
        calculateCountdown(fallbackUpcomingMatches);
      }

      if (previousResponse.success && previousResponse.data) {
        console.log('Previous matches API response:', previousResponse.data);
        console.log('Previous matches count:', previousResponse.data.length);

        // Debug each match in the response
        previousResponse.data.forEach((match: any, index: number) => {
          console.log(`Match ${index}:`, {
            id: match.id,
            competition: match.competition,
            status: match.status,
            homeTeamName: match.homeTeamName,
            awayTeamName: match.awayTeamName
          });
        });

        const formattedPrevious = MatchService.convertApiResponseToDashboardFormat(previousResponse.data);
        console.log('Formatted previous matches:', formattedPrevious);
        console.log('Formatted previous matches count:', formattedPrevious.length);
        setPreviousMatches(formattedPrevious);
        
        // Fetch scores for previous matches
        const matchIds = formattedPrevious.map((match: any) => match.id).filter(Boolean);
        if (matchIds.length > 0) {
          console.log('Fetching scores for previous matches:', matchIds);
          fetchMatchScores(matchIds);
        }
      } else {
        console.warn('Failed to fetch previous matches:', previousResponse.error);
        console.log('Using fallback previous matches');
        setPreviousMatches(fallbackPreviousMatches);
      }

    } catch (error: any) {
      console.error('Error fetching matches:', error);
      setError('Failed to load matches');
      // Use fallback data
      setUpcomingMatches(fallbackUpcomingMatches);
      setPreviousMatches(fallbackPreviousMatches);
      calculateCountdown(fallbackUpcomingMatches);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Refresh handler
  const handleRefresh = () => {
    setIsRefreshing(true);
    fetchMatches(false);
  };

  // Load data on component mount - wait for auth to complete
  useEffect(() => {
    if (!authLoading) {
      fetchMatches();
    }
  }, [authLoading]);

  // Timer to update countdown every minute
  useEffect(() => {
    const timer = setInterval(() => {
      if (upcomingMatches.length > 0) {
        calculateCountdown(upcomingMatches);
      }
    }, 60000); // Update every minute

    return () => clearInterval(timer);
  }, [upcomingMatches]);

  useFocusEffect(
    React.useCallback(() => {
      // Only refresh if auth is complete
      if (!authLoading) {
        fetchMatches(false);
      }
    }, [authLoading])
  );

  const renderUserProfile = () => (
    <View style={styles.profileCard}>
      <View style={styles.avatar} />
      <Text style={styles.userName}>Dave John</Text>
    </View>
  );

  const renderTabs = () => (
    <View style={styles.tabContainer}>
      <TouchableOpacity style={[styles.tab, activeTab === 'upcoming' ? styles.tabActive : {}]} onPress={() => setActiveTab('upcoming')}> 
        <Text style={[styles.tabText, activeTab === 'upcoming' ? styles.tabTextActive : styles.tabTextInactive]}>Upcoming</Text>
      </TouchableOpacity>
      <TouchableOpacity style={[styles.tab, activeTab === 'previous' ? styles.tabActive : {}]} onPress={() => setActiveTab('previous')}> 
        <Text style={[styles.tabText, activeTab === 'previous' ? styles.tabTextActive : styles.tabTextInactive]}>Previous</Text>
      </TouchableOpacity>
    </View>
  );

  const renderCountdown = () => {
    if (!nextMatch) return null;

    const getTeamDisplayName = (teamName: string) => {
      // Truncate long team names for display
      if (teamName.length > 15) {
        return teamName.substring(0, 12) + '...';
      }
      return teamName;
    };

    const nextGameText = `Next Game ${getTeamDisplayName(nextMatch.homeTeam.name)} v ${getTeamDisplayName(nextMatch.awayTeam.name)}`;

    return (
      <View style={styles.countdownSection}>
        <Text style={styles.nextGameText} numberOfLines={1}>{nextGameText}</Text>
        <View style={styles.countdownCard}>
          <View style={styles.countdownItem}>
            <Text style={styles.countdownNumber}>{countdown.days}</Text>
            <Text style={styles.countdownLabel}>Days</Text>
          </View>
          <View style={styles.separator} />
          <View style={styles.countdownItem}>
            <Text style={styles.countdownNumber}>{countdown.hours}</Text>
            <Text style={styles.countdownLabel}>Hours</Text>
          </View>
          <View style={styles.separator} />
          <View style={styles.countdownItem}>
            <Text style={styles.countdownNumber}>{countdown.minutes}</Text>
            <Text style={styles.countdownLabel}>Mins</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderMatchCard = (match: Match, index: number) => (
    <View key={index} style={styles.matchCard}>
      <View style={styles.matchHeader}>
        <View style={styles.matchDivision}>
          <View style={[styles.divisionDot, { backgroundColor: theme.primary }]} />
          <Text style={styles.divisionText} numberOfLines={1}>{match.division}</Text>
        </View>
        <View style={styles.headerRightSection}>
          {/* Show Abandoned badge for cancelled matches */}
          {match.status === 'CANCELLED' && (
            <View style={styles.abandonedBadge}>
              <Text style={styles.abandonedText}>Abandoned</Text>
            </View>
          )}
          <View style={styles.roleBadge}>
            <Text style={styles.roleText}>{match.role}</Text>
          </View>
        </View>
      </View>
      <TouchableOpacity onPress={() => {
        if (activeTab === 'previous') {
          (navigation as any).navigate('MatchResults', { matchId: match.id });
        } else {
          (navigation as any).navigate('MatchDetail', { matchId: match.id });
        }
      }}>
        <View style={styles.teamsCard}>
          {/* Home Team Column */}
          <View style={styles.teamColumn}>
            <StripedTshirt baseColor={match.homeTeam.kitColor} stripeColor={match.homeTeam.kitStripe} />
            <Text style={styles.teamName}>{match.homeTeam.name}</Text>
          </View>

          {/* Center Column for Score/Separator */}
          {activeTab === 'previous' && match.id && matchScores[match.id] ? (
            <View style={styles.scoreContainer}>
              <Text style={styles.scoreText}>{`${matchScores[match.id].home} - ${matchScores[match.id].away}`}</Text>
            </View>
          ) : typeof match.homeScore === 'number' && typeof match.awayScore === 'number' ? (
            <View style={styles.scoreContainer}>
              <Text style={styles.scoreText}>{`${match.homeScore} - ${match.awayScore}`}</Text>
            </View>
          ) : (
            <View style={styles.teamSeparatorContainer}>
              <View style={styles.teamSeparator} />
            </View>
          )}

          {/* Away Team Column */}
          <View style={styles.teamColumn}>
            <StripedTshirt baseColor={match.awayTeam.kitColor} stripeColor={match.awayTeam.kitStripe} />
            <Text style={styles.teamName}>{match.awayTeam.name}</Text>
          </View>
        </View>
      </TouchableOpacity>
      <View style={styles.matchDetails}>
        <View style={styles.locationSection}>
          <MaterialIcons name="location-on" size={16} color={theme.text} style={{ opacity: 0.7 }} />
          <Text style={styles.locationText}>{match.location}</Text>
        </View>
        <View style={styles.timeSection}>
          <Text style={styles.dateText}>{match.date}</Text>
          <View style={styles.timeSeparator} />
          <Text style={styles.timeText}>{match.time}</Text>
        </View>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <Header
        title="Dashboard"
        rightComponent={
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <TouchableOpacity onPress={() => navigation.navigate('FilterMatches')} style={{ padding: 4 }}>
              <MaterialIcons name="filter-list" size={24} color={theme.primary} />
            </TouchableOpacity>
            <TouchableOpacity onPress={() => navigation.navigate('CreateMatch')} style={styles.addButton}>
              <MaterialIcons name="add" size={24} color={theme.primary} />
            </TouchableOpacity>
          </View>
        }
      />
      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            tintColor={theme.primary}
            colors={[theme.primary]}
          />
        }
      >
        {renderUserProfile()}
        {renderTabs()}

        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.primary} />
            <Text style={styles.loadingText}>Loading matches...</Text>
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <MaterialIcons name="error-outline" size={48} color={theme.text} style={{ opacity: 0.5 }} />
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity style={styles.retryButton} onPress={() => fetchMatches()}>
              <Text style={styles.retryButtonText}>Retry</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View>
            {activeTab === 'upcoming' ? (
              <>
                {filteredUpcomingMatches.length > 0 && renderCountdown()}
                {filteredUpcomingMatches.length > 0 ? (
                  filteredUpcomingMatches.map(renderMatchCard)
                ) : (
                  <View style={styles.emptyContainer}>
                    <MaterialIcons name="event" size={48} color={theme.text} style={{ opacity: 0.3 }} />
                    <Text style={styles.emptyText}>No upcoming matches</Text>
                    <Text style={styles.emptySubText}>Create your first match to get started</Text>
                  </View>
                )}
              </>
            ) : (
              <>
                {filteredPreviousMatches.length > 0 ? (
                  filteredPreviousMatches.map(renderMatchCard)
                ) : (
                  <View style={styles.emptyContainer}>
                    <MaterialIcons name="history" size={48} color={theme.text} style={{ opacity: 0.3 }} />
                    <Text style={styles.emptyText}>No previous matches</Text>
                    <Text style={styles.emptySubText}>Your match history will appear here</Text>
                  </View>
                )}
              </>
            )}
          </View>
        )}
      </ScrollView>
    </View>
  );
};

// --- DYNAMIC STYLES ---
const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  container: { flex: 1, backgroundColor: theme.background },
  addButton: { padding: 4, marginLeft: 16 },
  content: { flex: 1 },
  profileCard: {
    flexDirection: 'row', alignItems: 'center', marginHorizontal: 16,
    marginTop: 12, marginBottom: 8, padding: 12, borderRadius: 10,
    backgroundColor: theme.card,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.05,
    shadowRadius: 2,
    elevation: isDarkMode ? 0 : 2,
  },
  countdownCard: {
    flexDirection: 'row', alignItems: 'center', paddingHorizontal: 20,
    paddingVertical: 16, backgroundColor: theme.card, borderRadius: 10,
    width: width * 0.75, justifyContent: 'space-evenly',
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.05,
    shadowRadius: 2,
    elevation: isDarkMode ? 0 : 2,
  },
  matchCard: {
    marginHorizontal: 16, marginBottom: 12, padding: 12,
    backgroundColor: theme.card, borderRadius: 10,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: isDarkMode ? 0 : 2,
  },
  avatar: { width: 48, height: 48, borderRadius: 24, backgroundColor: theme.border },
  userName: { marginLeft: 16, fontSize: 18, fontWeight: '700', color: theme.text, flexShrink: 1 },
  tabContainer: {
    flexDirection: 'row', marginHorizontal: 16, marginVertical: 12,
    height: 40, borderRadius: 8, backgroundColor: `${theme.text}12`,
  },
  tab: { flex: 1, alignItems: 'center', justifyContent: 'center', borderRadius: 6, margin: 2 },
  tabActive: { backgroundColor: theme.primary },
  tabText: { fontSize: 14, fontWeight: '600' },
  tabTextActive: { color: '#ffffff' },
  tabTextInactive: { color: theme.text, opacity: 0.7 },
  countdownSection: { alignItems: 'center', marginTop: 4, marginBottom: 16 },
  nextGameText: { fontSize: 14, fontWeight: '600', color: theme.text, marginBottom: 12 },
  countdownItem: { alignItems: 'center', justifyContent: 'center', minWidth: 50 },
  countdownNumber: { fontSize: 24, fontWeight: '700', color: theme.text, lineHeight: 28 },
  countdownLabel: { fontSize: 11, fontWeight: '500', color: theme.text, opacity: 0.7, marginTop: 2 },
  separator: { width: 1, height: 30, backgroundColor: theme.border },
  matchHeader: {
    flexDirection: 'row', alignItems: 'center',
    justifyContent: 'space-between', marginBottom: 8, gap: 8,
  },
  matchDivision: { flexDirection: 'row', alignItems: 'center', flexShrink: 1, overflow: 'hidden' },
  divisionDot: { width: 12, height: 12, borderRadius: 6, marginRight: 8 },
  divisionText: { fontSize: 12, fontWeight: '500', color: theme.text, opacity: 0.7 },
  roleBadge: {
    backgroundColor: theme.background, paddingHorizontal: 12,
    paddingVertical: 6, borderRadius: 6,
  },
  headerRightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  abandonedBadge: {
    backgroundColor: '#FF4444',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  abandonedText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  roleText: { fontSize: 12, fontWeight: '600', color: theme.text },
  teamsCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 4,
    backgroundColor: `${theme.text}08`,
    borderRadius: 8,
    marginBottom: 8,
  },
  teamColumn: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  teamName: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.text,
    textAlign: 'center',
    lineHeight: 16,
    marginTop: 6,
  },
  teamSeparatorContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    height: 36,
    marginHorizontal: 4,
  },
  teamSeparator: {
    width: 1,
    height: 24,
    backgroundColor: theme.text,
  },
  scoreContainer: {
    backgroundColor: theme.border,
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 16,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    marginHorizontal: 4,
  },
  scoreText: { fontSize: 16, fontWeight: 'bold', color: theme.text },
  matchDetails: {
    flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between',
    padding: 12, backgroundColor: 'transparent',
    borderRadius: 8, borderWidth: 1, borderColor: theme.border1, gap: 16,
  },
  locationSection: { flexDirection: 'row', alignItems: 'center', flex: 1 },
  locationText: { marginLeft: 8, fontSize: 13, fontWeight: '500', color: theme.text, opacity: 0.8, flexShrink: 1 },
  timeSection: { flexDirection: 'row', alignItems: 'center' },
  dateText: { fontSize: 13, fontWeight: '600', color: theme.text },
  timeSeparator: { width: 1, height: 16, backgroundColor: theme.border1, marginHorizontal: 12 },
  timeText: { fontSize: 13, fontWeight: '600', color: theme.text },

  // Loading, error, and empty states
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.text,
    opacity: 0.7,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 32,
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.text,
    textAlign: 'center',
    opacity: 0.7,
  },
  retryButton: {
    marginTop: 16,
    backgroundColor: theme.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 32,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: '600',
    color: theme.text,
    textAlign: 'center',
  },
  emptySubText: {
    marginTop: 8,
    fontSize: 14,
    color: theme.text,
    opacity: 0.6,
    textAlign: 'center',
  },
});

export default UserDashboard;
