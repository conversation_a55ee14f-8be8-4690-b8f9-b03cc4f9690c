import { Injectable, ValidationPipe, BadRequestException } from '@nestjs/common';

@Injectable()
export class EnhancedValidationPipe extends ValidationPipe {
  constructor() {
    super({
      // Transform payloads to DTO instances
      transform: true,
      // Strip properties that don't have decorators
      whitelist: true,
      // Throw error if non-whitelisted properties are present
      forbidNonWhitelisted: true,
      // Detailed error messages
      disableErrorMessages: false,
      // Custom error factory for sanitized error messages
      exceptionFactory: (errors) => {
        const messages = errors.map(error => {
          const constraints = error.constraints;
          if (constraints) {
            return Object.values(constraints).join(', ');
          }
          return 'Invalid input';
        });
        
        // Don't expose internal validation details to client
        return new BadRequestException({
          message: 'Input validation failed',
          errors: messages,
          statusCode: 400
        });
      }
    });
  }
}