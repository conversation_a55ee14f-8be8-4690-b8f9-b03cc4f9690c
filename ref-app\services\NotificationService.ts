import { Alert } from 'react-native';
import WebSocketService from '../api/websocketService';
import BroadcastService from '../api/broadcastService';

interface NotificationListener {
  id: string;
  callback: (notification: any) => void;
}

class NotificationService {
  private listeners: NotificationListener[] = [];
  private isInitialized = false;
  private currentUserId: string | null = null;

  /**
   * Initialize notification service
   */
  async initialize(userId: string, token?: string) {
    if (this.isInitialized && this.currentUserId === userId) {
      console.log('🔔 NotificationService already initialized for user:', userId);
      return;
    }

    try {
      console.log('🔔 Initializing NotificationService for user:', userId);
      this.currentUserId = userId;

      // Connect to WebSocket
      console.log('🔌 Connecting to WebSocket...');
      await WebSocketService.connect();
      console.log('🔌 WebSocket connected, authenticating...');

      // Authenticate
      WebSocketService.authenticate(userId, token);
      console.log('🔑 Authentication request sent');
      
      // Listen for notifications
      WebSocketService.on('notification', (data: any) => {
        console.log('🔔 WebSocket notification event received:', data);
        this.handleNotification(data);
      });
      WebSocketService.on('sync_invite', (data: any) => {
        console.log('🔔 WebSocket sync_invite event received:', data);
        this.handleSyncInvite(data);
      });
      WebSocketService.on('match_started', this.handleMatchStarted.bind(this));
      WebSocketService.on('action_broadcast', this.handleActionReceived.bind(this));
      
      // Listen for connection events
      WebSocketService.on('authenticated', () => {
        console.log('✅ Notification service authenticated successfully');
        this.isInitialized = true;
        this.loadPendingNotifications();
      });

      WebSocketService.on('auth_failed', (error: any) => {
        console.error('❌ WebSocket authentication failed:', error);
        this.isInitialized = false;
      });

      WebSocketService.on('disconnected', () => {
        console.log('🔌 Notification service disconnected');
        this.isInitialized = false;
      });

      WebSocketService.on('error', (error: any) => {
        console.error('❌ Notification service error:', error);
      });

      this.isInitialized = true;
      console.log('🔔 Notification service initialized for user:', userId);
      
    } catch (error) {
      console.error('❌ Failed to initialize notification service:', error);
      throw error;
    }
  }

  /**
   * Load pending notifications from server
   */
  private async loadPendingNotifications() {
    try {
      const response = await BroadcastService.getUserNotifications();
      if (response.success && response.data) {
        response.data.forEach(notification => {
          this.handleNotification(notification);
        });
      }
    } catch (error) {
      console.error('❌ Failed to load pending notifications:', error);
    }
  }

  /**
   * Handle incoming notifications
   */
  private handleNotification(notification: any) {
    console.log('🔔 NotificationService received notification:', notification);
    console.log('🔔 Active listeners count:', this.listeners.length);

    // Notify all listeners
    this.listeners.forEach((listener, index) => {
      try {
        console.log(`🔔 Calling listener ${index} (${listener.id})`);
        listener.callback(notification);
      } catch (error) {
        console.error('❌ Error in notification listener:', error);
      }
    });
  }

  /**
   * Handle sync invitation
   */
  private handleSyncInvite(invitation: any) {
    console.log('📧 Received sync invitation:', invitation);
    
    // This will be handled by the main app component
    this.handleNotification({
      type: 'sync_invite',
      ...invitation
    });
  }

  /**
   * Handle match started notification
   */
  private handleMatchStarted(data: any) {
    console.log('🏁 Match started notification:', data);
    
    // Show alert and navigate to match screen
    Alert.alert(
      'Match Started',
      `The match you're synced with has started. Tap OK to join.`,
      [
        { text: 'Later', style: 'cancel' },
        {
          text: 'Join Match',
          onPress: () => {
            this.handleNotification({
              type: 'match_started',
              ...data
            });
          }
        }
      ]
    );
  }

  /**
   * Handle action received (for approval)
   */
  private handleActionReceived(action: any) {
    console.log('📡 Received action for approval:', action);

    // Show action approval modal
    Alert.alert(
      'Action Received',
      `The referee has recorded: ${NotificationService.getActionDescription(action.data)}. Do you want to accept this action?`,
      [
        {
          text: 'Decline',
          style: 'cancel',
          onPress: () => NotificationService.respondToAction(action.actionId, 'decline', action.matchId)
        },
        {
          text: 'Accept',
          onPress: () => NotificationService.respondToAction(action.actionId, 'accept', action.matchId)
        }
      ]
    );
  }

  /**
   * Handle action broadcast notification (public method for App.tsx)
   */
  handleActionBroadcast(notification: any) {
    console.log('📡 Handling action broadcast:', notification);

    const actionData = notification.data;

    // Validate action data structure
    if (!actionData?.actionType || !actionData?.actionData) {
      console.error('❌ Invalid action broadcast data structure:', actionData);
      return;
    }

    // Generate a unique action ID for this broadcast
    const actionId = `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Show action approval modal
    Alert.alert(
      'Action Received',
      `The referee has recorded: ${NotificationService.getActionDescription(actionData)}. Do you want to accept this action?`,
      [
        {
          text: 'Decline',
          style: 'cancel',
          onPress: () => NotificationService.respondToAction(actionId, 'decline', notification.matchId)
        },
        {
          text: 'Accept',
          onPress: () => NotificationService.respondToAction(actionId, 'accept', notification.matchId)
        }
      ]
    );
  }

  /**
   * Get human-readable description of action
   */
  private static getActionDescription(actionData: any): string {
    switch (actionData.actionType) {
      case 'goal':
        return `Goal by ${actionData.actionData.playerName || 'Player'} (${actionData.actionData.team})`;
      case 'yellow_card':
        return `Yellow card for ${actionData.actionData.playerName || 'Player'} (${actionData.actionData.team})`;
      case 'red_card':
        return `Red card for ${actionData.actionData.playerName || 'Player'} (${actionData.actionData.team})`;
      case 'substitution':
        return `Substitution: ${actionData.actionData.playerOut} → ${actionData.actionData.playerIn} (${actionData.actionData.team})`;
      default:
        return `${actionData.actionType} action`;
    }
  }

  /**
   * Respond to action (accept/decline)
   */
  private static async respondToAction(actionId: string, response: 'accept' | 'decline', matchId: string) {
    try {
      console.log(`📝 ${response === 'accept' ? 'Accepting' : 'Declining'} action:`, actionId);

      const result = await BroadcastService.respondToAction(actionId, response, matchId);

      if (result.success) {
        console.log(`✅ Action ${response}ed successfully`);
        Alert.alert(
          'Response Sent',
          `You have ${response}ed the action. ${response === 'accept' ? 'It will be added to your match log.' : 'It will not be added to your match log.'}`
        );
      } else {
        console.error('❌ Failed to respond to action:', result.message);
        Alert.alert('Error', `Failed to ${response} action: ${result.message}`);
      }
    } catch (error) {
      console.error('❌ Error responding to action:', error);
      Alert.alert('Error', `Failed to ${response} action. Please try again.`);
    }
  }

  /**
   * Add notification listener
   */
  addListener(id: string, callback: (notification: any) => void) {
    this.listeners.push({ id, callback });
    
    return () => {
      this.removeListener(id);
    };
  }

  /**
   * Remove notification listener
   */
  removeListener(id: string) {
    this.listeners = this.listeners.filter(listener => listener.id !== id);
  }

  /**
   * Join match room for real-time sync
   */
  joinMatch(matchId: string) {
    if (WebSocketService.isConnected()) {
      WebSocketService.joinMatch(matchId);
    }
  }

  /**
   * Leave match room
   */
  leaveMatch() {
    // Implementation depends on WebSocket service
    // For now, we'll just log it
    console.log('👋 Left match room');
  }



  /**
   * Broadcast match action
   */
  broadcastAction(action: any) {
    if (WebSocketService.isConnected()) {
      WebSocketService.broadcastAction(action);
    }
  }

  /**
   * Disconnect notification service
   */
  disconnect() {
    WebSocketService.disconnect();
    this.listeners = [];
    this.isInitialized = false;
    this.currentUserId = null;
    console.log('🔌 Notification service disconnected');
  }

  /**
   * Check if service is connected
   */
  isConnected(): boolean {
    return this.isInitialized && WebSocketService.isConnected();
  }

  /**
   * Get current user ID
   */
  getCurrentUserId(): string | null {
    return this.currentUserId;
  }
}

// Export singleton instance
export default new NotificationService();
