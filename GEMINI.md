
# Project Overview

This is a mobile application built with React Native (using Expo) and a NestJS backend. The project is divided into two main parts: `ref-app` (the mobile app) and `ref-api` (the backend API).

## Technologies

**Frontend (ref-app):**

*   **Framework:** React Native (with Expo)
*   **Language:** TypeScript
*   **Navigation:** React Navigation
*   **Authentication:** Firebase (Google Sign-in, Facebook)
*   **UI Libraries:** React Native Vector Icons, React Native Gifted Charts, etc.

**Backend (ref-api):**

*   **Framework:** NestJS
*   **Language:** TypeScript
*   **Database:** PostgreSQL (with Prisma ORM)
*   **Caching:** Redis
*   **Object Storage:** Minio
*   **API:** RESTful with some WebSocket functionality

## Architecture

The mobile app (`ref-app`) communicates with the backend API (`ref-api`). The backend handles business logic, data storage, and interactions with other services like Firebase for authentication. The entire stack can be run locally using Docker.

# Building and Running

## Mobile App (ref-app)

To run the mobile app, navigate to the `ref-app` directory and use the following commands:

*   **Install dependencies:** `npm install`
*   **Start the development server:** `npm start`
*   **Run on Android:** `npm run android`
*   **Run on iOS:** `npm run ios`

## Backend API (ref-api)

The backend can be run using Docker or locally.

**Using Docker (Recommended):**

From the root directory of the project, run:

```bash
docker-compose up -d
```

This will start the API along with the PostgreSQL database, Redis, and Minio.

**Running Locally:**

To run the backend locally, navigate to the `ref-api` directory and use the following commands:

*   **Install dependencies:** `npm install`
*   **Start the development server:** `npm run start:dev`

**Database Seeding:**

To seed the database, run the following command from the `ref-api` directory:

```bash
npm run db:seed
```

# Development Conventions

*   **Code Style:** The project uses Prettier for code formatting.
*   **Linting:** ESLint is used for linting.
*   **Testing:** Jest is used for testing the backend.
*   **Commits:** No specific commit message convention is enforced, but clear and descriptive messages are encouraged.
