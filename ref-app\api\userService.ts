import { apiClient } from './client';
import { API_CONFIG, ApiResponse } from './config';

export interface UserProfile {
  id: string;
  email: string;
  name: string;
  displayName?: string;
  photoURL?: string;
  firebaseUid: string;
  emailVerified: boolean;
  provider: string;
  role: 'REFEREE' | 'AR1' | 'AR2' | 'FOURTH_OFFICIAL' | 'ASSESSOR' | 'ADMIN' | 'MANAGER';
  username?: string;
  weight?: number;
  height?: number;
  gender?: string;
  country?: string;
  countryFA?: string;
  refereeLevel?: string;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
  stats?: {
    totalMatches: number;
    upcomingMatches: number;
    completedMatches: number;
    cancelledMatches: number;
  };
}

export interface UpdateProfileRequest {
  name?: string;
  displayName?: string;
  photoURL?: string;
  username?: string;
  weight?: string;
  height?: string;
  gender?: string;
  country?: string;
  footballAssociation?: string;
  refereeLevel?: string;
}

export interface UpdateRoleRequest {
  role: 'REFEREE' | 'AR1' | 'AR2' | 'FOURTH_OFFICIAL' | 'ASSESSOR' | 'ADMIN' | 'MANAGER';
}

export class UserService {
  // Get user profile
  static async getProfile(): Promise<ApiResponse<UserProfile>> {
    return apiClient.get<UserProfile>('/users/profile');
  }

  // Update user profile
  static async updateProfile(profileData: UpdateProfileRequest): Promise<ApiResponse<UserProfile>> {
    return apiClient.patch<UserProfile>('/users/profile', profileData);
  }

  // Update user role (what they do in matches)
  static async updateRole(roleData: UpdateRoleRequest): Promise<ApiResponse<UserProfile>> {
    return apiClient.patch<UserProfile>('/users/role', roleData);
  }

  // Get user statistics
  static async getStats(): Promise<ApiResponse<UserProfile['stats']>> {
    return apiClient.get<UserProfile['stats']>('/users/stats');
  }

  // Helper method to get role display name
  static getRoleDisplayName(role: UserProfile['role']): string {
    const roleNames = {
      REFEREE: 'Referee',
      AR1: 'Assistant Referee 1',
      AR2: 'Assistant Referee 2',
      FOURTH_OFFICIAL: '4th Official',
      ASSESSOR: 'Assessor',
      ADMIN: 'Administrator',
      MANAGER: 'Manager',
    };
    return roleNames[role] || role;
  }

  // Delete user account
  static async deleteAccount(): Promise<ApiResponse<{ message: string }>> {
    return apiClient.delete<{ message: string }>('/users/account');
  }

  // Helper method to get available roles for selection
  static getAvailableRoles(): Array<{ value: UserProfile['role']; label: string }> {
    return [
      { value: 'REFEREE', label: 'Referee' },
      { value: 'AR1', label: 'Assistant Referee 1' },
      { value: 'AR2', label: 'Assistant Referee 2' },
      { value: 'FOURTH_OFFICIAL', label: '4th Official' },
      { value: 'ASSESSOR', label: 'Assessor' },
    ];
  }
}

export default UserService;