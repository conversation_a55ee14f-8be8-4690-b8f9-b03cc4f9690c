import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { UpdateUserDto } from './dto/update-user.dto';
import { User, UserRole } from '@prisma/client';

@Injectable()
export class UsersService {
  constructor(private prisma: PrismaService) {}

  async findByFirebaseUid(firebaseUid: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { firebaseUid },
    });
  }

  async findById(id: string): Promise<User> {
    const user = await this.prisma.user.findUnique({
      where: { id },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    return user;
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { email },
    });
  }

  async findByUsername(username: string): Promise<User | null> {
    return this.prisma.user.findFirst({
      where: { username },
    });
  }

  async createUser(userData: {
    email: string;
    name: string;
    displayName?: string;
    photoURL?: string;
    firebaseUid: string;
    emailVerified?: boolean;
    provider?: string;
  }): Promise<User> {
    try {
      return await this.prisma.user.create({
        data: {
          email: userData.email,
          name: userData.name,
          displayName: userData.displayName,
          photoURL: userData.photoURL,
          firebaseUid: userData.firebaseUid,
          emailVerified: userData.emailVerified || false,
          provider: userData.provider || 'email',
          // No default role - referee chooses during match creation
          lastLoginAt: new Date(),
        },
      });
    } catch (error) {
      if (error.code === 'P2002') {
        throw new BadRequestException('User with this email or Firebase UID already exists');
      }
      throw new BadRequestException(`Failed to create user: ${error.message}`);
    }
  }

  async updateUser(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    // Check if user exists
    await this.findById(id);

    try {
      return await this.prisma.user.update({
        where: { id },
        data: {
          ...updateUserDto,
          updatedAt: new Date(),
        },
      });
    } catch (error) {
      if (error.code === 'P2002') {
        throw new BadRequestException('Email already exists');
      }
      throw new BadRequestException(`Failed to update user: ${error.message}`);
    }
  }

  async updateUserByFirebaseUid(firebaseUid: string, updateData: Partial<User>): Promise<User> {
    const user = await this.findByFirebaseUid(firebaseUid);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    return this.prisma.user.update({
      where: { firebaseUid },
      data: {
        ...updateData,
        lastLoginAt: new Date(),
        updatedAt: new Date(),
      },
    });
  }

  async updateUserRole(id: string, role: UserRole): Promise<User> {
    // Check if user exists
    await this.findById(id);

    return this.prisma.user.update({
      where: { id },
      data: {
        role,
        updatedAt: new Date(),
      },
    });
  }

  async getUserProfile(firebaseUid: string): Promise<User> {
    const user = await this.findByFirebaseUid(firebaseUid);
    if (!user) {
      throw new NotFoundException('User profile not found');
    }
    return user;
  }

  async updateUserProfile(firebaseUid: string, updateData: UpdateUserDto): Promise<User> {
    const user = await this.findByFirebaseUid(firebaseUid);
    if (!user) {
      throw new NotFoundException('User profile not found');
    }

    // DTO already handles validation and transformation
    const processedData = { ...updateData };
    
    // Handle footballAssociation -> countryFA mapping
    if (updateData.footballAssociation !== undefined) {
      processedData.countryFA = updateData.footballAssociation;
      delete processedData.footballAssociation;
    }



    return this.prisma.user.update({
      where: { firebaseUid },
      data: {
        ...processedData,
        updatedAt: new Date(),
      },
    });
  }

  async deleteUser(id: string): Promise<User> {
    // Check if user exists
    await this.findById(id);

    // Delete all user's matches first
    await this.prisma.match.deleteMany({
      where: { refereeId: id },
    });

    // Delete the user
    return this.prisma.user.delete({
      where: { id },
    });
  }

  async getAllUsers(): Promise<User[]> {
    return this.prisma.user.findMany({
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async getUserStats(userId: string): Promise<{
    totalMatches: number;
    upcomingMatches: number;
    completedMatches: number;
    cancelledMatches: number;
  }> {
    const [totalMatches, upcomingMatches, completedMatches, cancelledMatches] = await Promise.all([
      this.prisma.match.count({ where: { refereeId: userId } }),
      this.prisma.match.count({ 
        where: { 
          refereeId: userId, 
          status: 'SCHEDULED',
          matchDate: { gte: new Date() }
        } 
      }),
      this.prisma.match.count({ 
        where: { 
          refereeId: userId, 
          status: 'COMPLETED' 
        } 
      }),
      this.prisma.match.count({ 
        where: { 
          refereeId: userId, 
          status: 'CANCELLED' 
        } 
      }),
    ]);

    return {
      totalMatches,
      upcomingMatches,
      completedMatches,
      cancelledMatches,
    };
  }
}