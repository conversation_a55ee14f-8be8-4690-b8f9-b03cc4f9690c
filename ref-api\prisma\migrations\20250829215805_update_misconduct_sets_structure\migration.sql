/*
  Warnings:

  - You are about to drop the column `playerType` on the `misconduct_sets` table. All the data in the column will be lost.
  - You are about to drop the column `redCodes` on the `misconduct_sets` table. All the data in the column will be lost.
  - You are about to drop the column `yellowCodes` on the `misconduct_sets` table. All the data in the column will be lost.
  - Added the required column `officialsRedCodes` to the `misconduct_sets` table without a default value. This is not possible if the table is not empty.
  - Added the required column `officialsYellowCodes` to the `misconduct_sets` table without a default value. This is not possible if the table is not empty.
  - Added the required column `playersRedCodes` to the `misconduct_sets` table without a default value. This is not possible if the table is not empty.
  - Added the required column `playersYellowCodes` to the `misconduct_sets` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "misconduct_sets" DROP COLUMN "playerType",
DROP COLUMN "redCodes",
DROP COLUMN "yellowCodes",
ADD COLUMN     "officialsRedCodes" JSONB NOT NULL,
ADD COLUMN     "officialsYellowCodes" JSONB NOT NULL,
ADD COLUMN     "playersRedCodes" JSONB NOT NULL,
ADD COLUMN     "playersYellowCodes" JSONB NOT NULL;

-- DropEnum
DROP TYPE "PlayerType";
