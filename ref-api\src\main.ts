import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { EnhancedValidationPipe } from './common/validation/enhanced-validation.pipe';
import { DatabaseExceptionFilter } from './common/filters/database-exception.filter';
import { Logger } from '@nestjs/common';
const helmet = require('helmet');
const compression = require('compression');

async function bootstrap() {
  const logger = new Logger('Bootstrap');
  const app = await NestFactory.create(AppModule);

  // Security middleware
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
    // Disable HSTS for local development
    hsts: process.env.NODE_ENV === 'production' ? {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true
    } : false
  }));

  // Compression
  app.use(compression());

  // Enhanced CORS with security
  app.enableCors({
    origin: process.env.NODE_ENV === 'production' 
      ? ['https://yourdomain.com'] // Replace with your actual domain
      : true,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    maxAge: 86400 // 24 hours
  });

  // Enhanced validation with sanitization
  app.useGlobalPipes(new EnhancedValidationPipe());

  // Database error handling
  app.useGlobalFilters(new DatabaseExceptionFilter());

  // Request size limits
  app.use(require('express').json({ limit: '1mb' }));
  app.use(require('express').urlencoded({ limit: '1mb', extended: true }));

  const port = process.env.PORT ?? 3000;
  
  logger.log('🚀 API starting with enhanced security...');
  logger.log(`🔒 Security features enabled: Helmet, CORS, Validation, Error Filtering`);
  logger.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  
  await app.listen(port);
  logger.log(`✅ API is running securely on port ${port}`);
}

void bootstrap();
