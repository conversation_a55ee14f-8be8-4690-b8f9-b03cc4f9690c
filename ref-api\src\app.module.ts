import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { PrismaService } from './prisma.service';
import { RedisModule } from './redis/redis.module';
import { S3Module } from './s3/s3.module';
import { AuthModule } from './auth/auth.module';
import { MatchesModule } from './matches/matches.module';
import { UsersModule } from './users/users.module';
import { MisconductSetsModule } from './misconduct-sets/misconduct-sets.module';
import { TemplatesModule } from './templates/templates.module';
import { EvaluationModule } from './evaluation/evaluation.module';
import { BroadcastModule } from './broadcast/broadcast.module';

@Module({
  imports: [RedisModule, S3Module, AuthModule, MatchesModule, UsersModule, MisconductSetsModule, TemplatesModule, EvaluationModule, BroadcastModule],
  controllers: [AppController],
  providers: [AppService, PrismaService],
})
export class AppModule {}
