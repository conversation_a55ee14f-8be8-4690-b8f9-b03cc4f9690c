import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiClient } from './client';
import { API_CONFIG, ApiResponse } from './config';

export interface EvaluationQuestion {
  id: number;
  text: string;
  userId?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface EvaluationAnswer {
  id?: string;
  questionId: number;
  question?: string; // For compatibility with frontend usage
  questionText?: string; // Field name from database
  rating: number; // 1-5 scale
  label: string; // Very Dissatisfied, Dissatisfied, Somehow, Satisfied, Very Satisfied
  emoji: number; // Emoji level (1-5)
  evaluationId?: string; // From database
  createdAt?: string; // From database
}

export interface MatchEvaluation {
  id?: string;
  matchId: string;
  userId?: string;
  answers: EvaluationAnswer[];
  completedAt: string;
  createdAt?: string;
  updatedAt?: string;
}

// Database request/response interfaces
export interface CreateEvaluationQuestionsRequest {
  userId: string;
  questions: Array<{
    id: number;
    text: string;
  }>;
}

export interface CreateMatchEvaluationRequest {
  matchId: string;
  userId: string;
  answers: Array<{
    questionId: number;
    question: string;
    rating: number;
    label: string;
    emoji: number;
  }>;
}

export class EvaluationService {
  private static readonly QUESTIONS_KEY = 'evaluation_questions';
  private static readonly EVALUATION_KEY_PREFIX = 'match_evaluation_';

  // Default evaluation questions (same as EvaluateQuestions.tsx)
  private static readonly DEFAULT_QUESTIONS: EvaluationQuestion[] = [
    { id: 1, text: 'How was the overall team performance?' },
    { id: 2, text: 'Did the team show good sportsmanship?' },
    { id: 3, text: 'What was the most significant moment of the match?' },
    { id: 4, text: 'Which player had the biggest impact?' },
    { id: 5, text: 'What could the team improve for the next match?' },
  ];

  // Rating labels mapping
  private static readonly RATING_LABELS: Record<number, string> = {
    1: 'Very Dissatisfied',
    2: 'Dissatisfied', 
    3: 'Somehow',
    4: 'Satisfied',
    5: 'Very Satisfied'
  };

  /**
   * Get current evaluation questions from database or return defaults
   */
  static async getEvaluationQuestions(userId?: string): Promise<EvaluationQuestion[]> {
    try {
      if (!userId) {
        // Try to get from local storage first as fallback
        const stored = await AsyncStorage.getItem(this.QUESTIONS_KEY);
        if (stored) {
          const questions = JSON.parse(stored);
          return questions.slice(0, 5);
        }
        return this.DEFAULT_QUESTIONS;
      }

      console.log('📚 Fetching evaluation questions from database for userId:', userId);
      const response: ApiResponse<EvaluationQuestion[]> = await apiClient.get(
        API_CONFIG.ENDPOINTS.EVALUATION.QUESTIONS.BY_USER(userId)
      );

      if (response.success && response.data && response.data.length > 0) {
        console.log('✅ Questions loaded from database:', response.data.length);
        return response.data.slice(0, 5); // Limit to 5 questions
      } else {
        console.log('🔄 No questions found in database, using defaults');
        return this.DEFAULT_QUESTIONS;
      }
    } catch (error) {
      console.error('❌ Error loading evaluation questions from database:', error);
      
      // Fallback to AsyncStorage
      try {
        const stored = await AsyncStorage.getItem(this.QUESTIONS_KEY);
        if (stored) {
          const questions = JSON.parse(stored);
          return questions.slice(0, 5);
        }
      } catch (storageError) {
        console.error('❌ Error loading from AsyncStorage fallback:', storageError);
      }
      
      return this.DEFAULT_QUESTIONS;
    }
  }

  /**
   * Update evaluation questions (called from EvaluateQuestions screen)
   */
  static async updateEvaluationQuestions(questions: EvaluationQuestion[], userId: string): Promise<void> {
    try {
      // Only store up to 5 questions
      const limitedQuestions = questions.slice(0, 5);
      
      console.log('💾 Saving evaluation questions to database for userId:', userId);
      
      const requestData: CreateEvaluationQuestionsRequest = {
        userId,
        questions: limitedQuestions.map(q => ({ id: q.id, text: q.text }))
      };
      
      const response: ApiResponse<EvaluationQuestion[]> = await apiClient.post(
        API_CONFIG.ENDPOINTS.EVALUATION.QUESTIONS.UPDATE(userId),
        requestData
      );
      
      if (response.success) {
        console.log('✅ Questions successfully saved to database');
        
        // Also save to AsyncStorage as backup
        await AsyncStorage.setItem(this.QUESTIONS_KEY, JSON.stringify(limitedQuestions));
      } else {
        console.error('❌ Failed to save questions to database:', response.error);
        throw new Error(response.error || 'Failed to save questions');
      }
    } catch (error) {
      console.error('❌ Error saving evaluation questions:', error);
      
      // Fallback to AsyncStorage only
      try {
        const limitedQuestions = questions.slice(0, 5);
        await AsyncStorage.setItem(this.QUESTIONS_KEY, JSON.stringify(limitedQuestions));
        console.log('⚠️ Questions saved to AsyncStorage as fallback');
      } catch (storageError) {
        console.error('❌ Failed to save to AsyncStorage fallback:', storageError);
        throw error;
      }
    }
  }

  /**
   * Save match evaluation answers to database
   */
  static async saveMatchEvaluation(matchId: string, userId: string, answers: EvaluationAnswer[]): Promise<void> {
    try {
      console.log('💾 Saving match evaluation to database:', { matchId, userId, answerCount: answers.length });
      
      const requestData: CreateMatchEvaluationRequest = {
        matchId,
        userId,
        answers: answers.map(answer => ({
          questionId: answer.questionId,
          question: answer.question || answer.questionText || '',
          rating: answer.rating,
          label: answer.label,
          emoji: answer.rating // emoji level is same as rating (1-5)
        }))
      };
      
      const response: ApiResponse<MatchEvaluation> = await apiClient.post(
        API_CONFIG.ENDPOINTS.EVALUATION.ANSWERS.CREATE,
        requestData
      );
      
      if (response.success) {
        console.log('✅ Match evaluation saved to database successfully');
        
        // Also save to AsyncStorage as backup
        const evaluation: MatchEvaluation = {
          matchId,
          userId,
          answers,
          completedAt: new Date().toISOString()
        };
        
        const key = `${this.EVALUATION_KEY_PREFIX}${matchId}`;
        await AsyncStorage.setItem(key, JSON.stringify(evaluation));
      } else {
        console.error('❌ Failed to save evaluation to database:', response.error);
        throw new Error(response.error || 'Failed to save evaluation');
      }
    } catch (error) {
      console.error('❌ Error saving match evaluation:', error);
      
      // Fallback to AsyncStorage only
      try {
        const evaluation: MatchEvaluation = {
          matchId,
          userId,
          answers,
          completedAt: new Date().toISOString()
        };
        
        const key = `${this.EVALUATION_KEY_PREFIX}${matchId}`;
        await AsyncStorage.setItem(key, JSON.stringify(evaluation));
        console.log('⚠️ Evaluation saved to AsyncStorage as fallback');
      } catch (storageError) {
        console.error('❌ Failed to save to AsyncStorage fallback:', storageError);
        throw error;
      }
    }
  }

  /**
   * Get match evaluation answers from database
   */
  static async getMatchEvaluation(matchId: string): Promise<MatchEvaluation | null> {
    try {
      console.log('📖 Fetching match evaluation from database for matchId:', matchId);
      
      const response: ApiResponse<MatchEvaluation> = await apiClient.get(
        API_CONFIG.ENDPOINTS.EVALUATION.ANSWERS.BY_MATCH(matchId)
      );
      
      if (response.success && response.data) {
        console.log('✅ Match evaluation loaded from database:', response.data);
        return response.data;
      } else {
        console.log('🔄 No evaluation found in database, checking AsyncStorage fallback');
      }
    } catch (error) {
      console.error('❌ Error loading match evaluation from database:', error);
    }
    
    // Fallback to AsyncStorage
    try {
      const key = `${this.EVALUATION_KEY_PREFIX}${matchId}`;
      const stored = await AsyncStorage.getItem(key);
      
      if (stored) {
        console.log('✅ Match evaluation loaded from AsyncStorage fallback');
        return JSON.parse(stored);
      }
      
      console.log('❌ No evaluation data found in database or AsyncStorage');
      return null;
    } catch (storageError) {
      console.error('❌ Error loading from AsyncStorage fallback:', storageError);
      return null;
    }
  }

  /**
   * Get rating label by number
   */
  static getRatingLabel(rating: number): string {
    return this.RATING_LABELS[rating] || 'Unknown';
  }

  /**
   * Convert answers to display format for MatchResultsScreen
   */
  static convertAnswersToDisplayFormat(answers: EvaluationAnswer[]): Record<string, { level: number; label: string }> {
    console.log('🔄 Converting answers to display format:', answers);
    const result: Record<string, { level: number; label: string }> = {};

    answers.forEach(answer => {
      // Handle both question and questionText fields for compatibility
      const questionText = answer.question || (answer as any).questionText;
      console.log('📝 Processing answer:', { questionText, rating: answer.rating, label: answer.label });
      if (questionText) {
        result[questionText] = {
          level: answer.rating,
          label: answer.label
        };
      }
    });

    console.log('✅ Final display format result:', result);
    return result;
  }

  /**
   * Delete match evaluation (for cleanup)
   */
  static async deleteMatchEvaluation(matchId: string): Promise<void> {
    try {
      const key = `${this.EVALUATION_KEY_PREFIX}${matchId}`;
      await AsyncStorage.removeItem(key);
    } catch (error) {
      console.error('Error deleting match evaluation:', error);
    }
  }
}

export default EvaluationService;