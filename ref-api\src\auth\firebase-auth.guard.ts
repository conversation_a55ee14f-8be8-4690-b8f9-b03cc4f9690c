import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { FirebaseService } from './firebase.service';
import { UsersService } from '../users/users.service';

@Injectable()
export class FirebaseAuthGuard implements CanActivate {
  constructor(
    private firebaseService: FirebaseService,
    private usersService: UsersService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedException('No valid authorization header found');
    }

    const idToken = authHeader.split('Bearer ')[1];

    try {
      // Verify the Firebase ID token
      const decodedToken = await this.firebaseService.verifyIdToken(idToken);
      
      // Get or create user in our database
      let user = await this.usersService.findByFirebaseUid(decodedToken.uid);
      
      if (!user) {
        // Create user if doesn't exist
        user = await this.usersService.createUser({
          email: decodedToken.email || '',
          name: decodedToken.name || decodedToken.email?.split('@')[0] || 'User',
          displayName: decodedToken.name,
          photoURL: decodedToken.picture,
          firebaseUid: decodedToken.uid,
          emailVerified: decodedToken.email_verified || false,
          provider: decodedToken.firebase?.sign_in_provider || 'email',
        });
      } else {
        // Update user's last login and any changed info
        user = await this.usersService.updateUserByFirebaseUid(decodedToken.uid, {
          displayName: decodedToken.name || user.displayName,
          photoURL: decodedToken.picture || user.photoURL,
          emailVerified: decodedToken.email_verified || user.emailVerified,
          lastLoginAt: new Date(),
        });
      }

      // Attach user to request
      request.user = {
        id: user.id,
        email: user.email,
        name: user.name,
        displayName: user.displayName,
        photoURL: user.photoURL,
        firebaseUid: user.firebaseUid,
        role: user.role,
        emailVerified: user.emailVerified,
        provider: user.provider,
      };

      console.log('🔐 User authenticated:', {
        id: user.id,
        email: user.email,
        name: user.name
      });

      return true;
    } catch (error) {
      throw new UnauthorizedException('Invalid or expired token');
    }
  }
}