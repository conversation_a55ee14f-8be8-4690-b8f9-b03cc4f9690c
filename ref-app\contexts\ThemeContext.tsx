import React, { createContext, useState, useContext, ReactNode } from 'react';
import { lightTheme, darkTheme } from '../themes/colors';

interface Theme {
  background: string;
  text: string;
  textSecondary: string;
  primary: string;
  card: string;
  border: string;
  border1: string;
  percentborder: string;
  HedaersafeArea:string;
  red: string;
  white?: string;
}

interface ThemeContextData {
  theme: Theme;
  isDarkMode: boolean;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextData>({} as ThemeContextData);

export const ThemeProvider = ({ children }: { children: ReactNode }) => {
  const [isDarkMode, setIsDarkMode] = useState(false);

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
  };

  const theme = isDarkMode ? darkTheme : lightTheme;

  return (
    <ThemeContext.Provider value={{ theme, isDarkMode, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
