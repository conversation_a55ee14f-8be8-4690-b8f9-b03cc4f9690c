import { apiClient } from './client';
import { API_CONFIG, ApiResponse } from './config';

export interface SyncInvitation {
  matchId: string;
  role: string;
  invitedUserEmail?: string;
  invitedUserUsername?: string;
}

export interface MatchParticipant {
  id: string;
  userId: string;
  role: string;
  status: string;
  joinedAt?: string;
  user: {
    id: string;
    name: string;
    email: string;
    displayName?: string;
  };
}

export interface MatchAction {
  id: string;
  type: 'card' | 'goal' | 'substitution' | 'period_start' | 'period_end' | 'match_start' | 'match_end';
  matchId: string;
  userId: string;
  timestamp: number;
  realTime: Date;
  data: any;
}

export interface SyncNotification {
  id: string;
  type: 'match_start' | 'role_assigned' | 'action_broadcast' | 'sync_invite';
  matchId: string;
  fromUserId: string;
  data: any;
  timestamp: number;
}

class BroadcastService {
  /**
   * Invite user to sync with match
   */
  static async inviteUser(invitation: SyncInvitation): Promise<ApiResponse<void>> {
    try {
      console.log('📧 Inviting user to match sync:', invitation);
      
      const response = await apiClient.post<void>(
        '/broadcast/invite',
        invitation
      );
      
      if (response.success) {
        console.log('✅ User invited successfully');
      }
      
      return response;
    } catch (error) {
      console.error('❌ Failed to invite user:', error);
      throw error;
    }
  }

  /**
   * Accept sync invitation
   */
  static async acceptInvitation(matchId: string): Promise<ApiResponse<void>> {
    try {
      console.log('✅ Accepting sync invitation for match:', matchId);
      
      const response = await apiClient.post<void>(
        '/broadcast/accept-invitation',
        { matchId }
      );
      
      if (response.success) {
        console.log('✅ Invitation accepted successfully');
      }
      
      return response;
    } catch (error) {
      console.error('❌ Failed to accept invitation:', error);
      throw error;
    }
  }

  /**
   * Get match participants
   */
  static async getMatchParticipants(matchId: string): Promise<ApiResponse<MatchParticipant[]>> {
    try {
      console.log('👥 Fetching match participants for:', matchId);
      
      const response = await apiClient.get<MatchParticipant[]>(
        `/broadcast/match/${matchId}/participants`
      );
      
      if (response.success) {
        console.log('✅ Match participants loaded:', response.data?.length);
      }
      
      return response;
    } catch (error) {
      console.error('❌ Failed to get match participants:', error);
      throw error;
    }
  }

  /**
   * Get match actions from Redis Stream
   */
  static async getMatchActions(matchId: string): Promise<ApiResponse<MatchAction[]>> {
    try {
      console.log('📡 Fetching match actions for:', matchId);
      
      const response = await apiClient.get<MatchAction[]>(
        `/broadcast/match/${matchId}/actions`
      );
      
      if (response.success) {
        console.log('✅ Match actions loaded:', response.data?.length);
      }
      
      return response;
    } catch (error) {
      console.error('❌ Failed to get match actions:', error);
      throw error;
    }
  }

  /**
   * Get user notifications
   */
  static async getUserNotifications(): Promise<ApiResponse<SyncNotification[]>> {
    try {
      console.log('🔔 Fetching user notifications');
      
      const response = await apiClient.get<SyncNotification[]>(
        '/broadcast/notifications'
      );
      
      if (response.success) {
        console.log('✅ Notifications loaded:', response.data?.length);
      }
      
      return response;
    } catch (error) {
      console.error('❌ Failed to get notifications:', error);
      throw error;
    }
  }

  /**
   * Get user's role in match
   */
  static async getMyRole(matchId: string): Promise<ApiResponse<{ role: string | null }>> {
    try {
      console.log('🎭 Fetching user role for match:', matchId);
      
      const response = await apiClient.get<{ role: string | null }>(
        `/broadcast/match/${matchId}/my-role`
      );
      
      if (response.success) {
        console.log('✅ User role loaded:', response.data?.role);
      }
      
      return response;
    } catch (error) {
      console.error('❌ Failed to get user role:', error);
      throw error;
    }
  }

  /**
   * Find user by email (for invitation UI)
   */
  static async findUserByEmail(email: string): Promise<ApiResponse<{ id: string; name: string; email: string }>> {
    try {
      console.log('🔍 Finding user by email:', email);
      
      // This endpoint might need to be created in UsersController
      const response = await apiClient.get<{ id: string; name: string; email: string }>(
        `/users/search?email=${encodeURIComponent(email)}`
      );
      
      if (response.success) {
        console.log('✅ User found:', response.data?.name);
      }
      
      return response;
    } catch (error) {
      console.error('❌ Failed to find user:', error);
      throw error;
    }
  }

  /**
   * Find user by username (for invitation UI)
   */
  static async findUserByUsername(username: string): Promise<ApiResponse<{ id: string; name: string; email: string }>> {
    try {
      console.log('🔍 Finding user by username:', username);
      
      // This endpoint might need to be created in UsersController
      const response = await apiClient.get<{ id: string; name: string; email: string }>(
        `/users/search?username=${encodeURIComponent(username)}`
      );
      
      if (response.success) {
        console.log('✅ User found:', response.data?.name);
      }
      
      return response;
    } catch (error) {
      console.error('❌ Failed to find user:', error);
      throw error;
    }
  }

  /**
   * Broadcast match start to synced participants
   */
  static async broadcastMatchStart(
    matchId: string,
    kickoffTeam: string,
    period: number,
    matchData?: any
  ): Promise<ApiResponse<any>> {
    try {
      console.log('📡 Broadcasting match start for:', matchId);

      const response = await apiClient.post<any>('/broadcast/match-start', {
        matchId,
        kickoffTeam,
        period,
        matchData
      });

      if (response.success) {
        console.log('✅ Match start broadcasted successfully');
      }

      return response;
    } catch (error) {
      console.error('❌ Failed to broadcast match start:', error);
      throw error;
    }
  }

  /**
   * Broadcast match action to synced participants
   */
  static async broadcastAction(
    matchId: string,
    actionType: string,
    actionData: any,
    timestamp: string
  ): Promise<ApiResponse<any>> {
    try {
      console.log('📡 Broadcasting action:', actionType, 'for match:', matchId);

      const response = await apiClient.post<any>('/broadcast/action', {
        matchId,
        actionType,
        actionData,
        timestamp
      });

      if (response.success) {
        console.log('✅ Action broadcasted successfully');
      }

      return response;
    } catch (error) {
      console.error('❌ Failed to broadcast action:', error);
      throw error;
    }
  }

  /**
   * Respond to a broadcasted action
   */
  static async respondToAction(
    actionId: string,
    response: 'accept' | 'decline',
    matchId: string
  ): Promise<ApiResponse<any>> {
    try {
      console.log('📝 Responding to action:', actionId, 'with:', response);

      const apiResponse = await apiClient.post<any>('/broadcast/action-response', {
        actionId,
        response,
        matchId
      });

      if (apiResponse.success) {
        console.log('✅ Action response sent successfully');
      }

      return apiResponse;
    } catch (error) {
      console.error('❌ Failed to respond to action:', error);
      throw error;
    }
  }
}

export default BroadcastService;
