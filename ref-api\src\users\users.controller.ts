import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
  Query,
  HttpException,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { UpdateUserDto } from './dto/update-user.dto';
import { FirebaseAuthGuard } from '../auth/firebase-auth.guard';
import { UserRole } from '@prisma/client';

@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @UseGuards(FirebaseAuthGuard)
  @Get('profile')
  async getProfile(@Request() req) {
    const firebaseUid = req.user.firebaseUid;
    const user = await this.usersService.getUserProfile(firebaseUid);
    
    // Get user stats
    const stats = await this.usersService.getUserStats(user.id);
    
    return {
      success: true,
      data: {
        ...user,
        stats,
      },
    };
  }

  @UseGuards(FirebaseAuthGuard)
  @Patch('profile')
  async updateProfile(@Request() req, @Body() updateData: UpdateUserDto) {
    const firebaseUid = req.user.firebaseUid;
    const updatedUser = await this.usersService.updateUserProfile(firebaseUid, updateData);
    
    return {
      success: true,
      data: updatedUser,
      message: 'Profile updated successfully',
    };
  }

  @UseGuards(FirebaseAuthGuard)
  @Patch('role')
  async updateRole(@Request() req, @Body() body: { role: UserRole }) {
    const firebaseUid = req.user.firebaseUid;
    const user = await this.usersService.getUserProfile(firebaseUid);
    const updatedUser = await this.usersService.updateUserRole(user.id, body.role);
    
    return {
      success: true,
      data: updatedUser,
      message: 'Role updated successfully',
    };
  }

  @UseGuards(FirebaseAuthGuard)
  @Get('stats')
  async getUserStats(@Request() req) {
    const firebaseUid = req.user.firebaseUid;
    const user = await this.usersService.getUserProfile(firebaseUid);
    const stats = await this.usersService.getUserStats(user.id);
    
    return {
      success: true,
      data: stats,
    };
  }

  @UseGuards(FirebaseAuthGuard)
  @Delete('account')
  @HttpCode(HttpStatus.OK)
  async deleteAccount(@Request() req) {
    const firebaseUid = req.user.firebaseUid;
    const user = await this.usersService.getUserProfile(firebaseUid);
    await this.usersService.deleteUser(user.id);
    
    return {
      success: true,
      message: 'Account deleted successfully',
    };
  }

  /**
   * GET /users/search?email=...&username=...
   * Search users by email or username
   */
  @UseGuards(FirebaseAuthGuard)
  @Get('search')
  async searchUsers(
    @Query('email') email?: string,
    @Query('username') username?: string,
  ) {
    try {
      if (!email && !username) {
        throw new HttpException(
          {
            success: false,
            error: 'Either email or username parameter is required',
            message: 'Invalid search parameters'
          },
          HttpStatus.BAD_REQUEST
        );
      }

      let user;

      if (email) {
        user = await this.usersService.findByEmail(email);
      } else if (username) {
        user = await this.usersService.findByUsername(username);
      }

      if (!user) {
        return {
          success: false,
          data: null,
          message: 'User not found'
        };
      }

      // Return limited user info for privacy
      return {
        success: true,
        data: {
          id: user.id,
          name: user.name,
          email: user.email,
          displayName: user.displayName
        },
        message: 'User found successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to search users'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  // Admin endpoints (you might want to add admin guard later)
  @Get()
  async findAll() {
    const users = await this.usersService.getAllUsers();
    return {
      success: true,
      data: users,
    };
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const user = await this.usersService.findById(id);
    const stats = await this.usersService.getUserStats(id);
    
    return {
      success: true,
      data: {
        ...user,
        stats,
      },
    };
  }

  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    const updatedUser = await this.usersService.updateUser(id, updateUserDto);
    return {
      success: true,
      data: updatedUser,
      message: 'User updated successfully',
    };
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string) {
    await this.usersService.deleteUser(id);
    return {
      success: true,
      message: 'User deleted successfully',
    };
  }
}