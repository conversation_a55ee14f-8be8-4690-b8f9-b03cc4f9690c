import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet, Modal, TextInput, Alert, ActivityIndicator } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import Header from '../components/Header';
import { useTheme } from '../contexts/ThemeContext';
import { useUser } from '../contexts/UserContext';
import TemplateService, { MatchTemplate } from '../api/templateService';

// SVG Icons (same as CreateMatchScreen)
import Svg, { Path } from 'react-native-svg';

interface SvgIconProps { 
  color?: string; 
  size?: number; 
}

const ChevronDownIcon: React.FC<SvgIconProps> = ({ color, size = 18 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M6 9L12 15L18 9" stroke={color} strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
  </Svg>
);

const PlusIcon: React.FC<SvgIconProps> = ({ color, size = 16 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M12 5V19M5 12H19" stroke={color} strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" />
  </Svg>
);

const MinusIcon: React.FC<SvgIconProps> = ({ color, size = 16 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M5 12H19" stroke={color} strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" />
  </Svg>
);

const EditIcon: React.FC<SvgIconProps> = ({ color, size = 16 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="m18.5 2.5 a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </Svg>
);

const DeleteIcon: React.FC<SvgIconProps> = ({ color, size = 16 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M3 6h18m-2 0v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </Svg>
);

const CopyIcon: React.FC<SvgIconProps> = ({ color, size = 16 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M20 9H11a2 2 0 00-2 2v9a2 2 0 002 2h9a2 2 0 002-2v-9a2 2 0 00-2-2z" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <Path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </Svg>
);

// Use MatchTemplate from TemplateService instead of local Template type
type Template = MatchTemplate;

interface NumericInputRowProps { 
  label: string; 
  value: number; 
  onValueChange: (val: number) => void; 
  styles: any; 
  theme: any; 
}

interface PickerLikeRowProps { 
  label: string; 
  value: string; 
  options?: string[]; 
  onValueChange: (value: string) => void; 
  styles: any; 
  theme: any; 
}

const NumericInputRow: React.FC<NumericInputRowProps> = ({ label, value, onValueChange, styles, theme }) => (
  <View style={styles.inputField}>
    <Text style={styles.inputLabel}>{label}</Text>
    <View style={styles.numericInputControls}>
      <TouchableOpacity onPress={() => onValueChange(Math.max(0, value - 1))} style={styles.numericButton}>
        <MinusIcon size={14} color={theme.text} />
      </TouchableOpacity>
      <Text style={styles.numericValueText}>{value}</Text>
      <TouchableOpacity onPress={() => onValueChange(value + 1)} style={styles.numericButton}>
        <PlusIcon size={14} color={theme.text} />
      </TouchableOpacity>
    </View>
  </View>
);

const PickerLikeRow: React.FC<PickerLikeRowProps> = ({ label, value, options = ['Yes', 'No'], onValueChange, styles, theme }) => {
  const cycleValue = () => {
    const currentIndex = options.indexOf(value);
    const nextIndex = (currentIndex + 1) % options.length;
    onValueChange(options[nextIndex]);
  };
  
  return (
    <TouchableOpacity style={styles.inputField} onPress={cycleValue} activeOpacity={0.7}>
      <Text style={styles.inputLabel}>{label}</Text>
      <View style={styles.inputValueContainer}>
        <Text style={styles.inputValueText}>{value}</Text>
        <ChevronDownIcon color={theme.text} />
      </View>
    </TouchableOpacity>
  );
};

const ManageTemplateScreen = () => {
  const { isDarkMode, theme } = useTheme();
  const { userProfile } = useUser();
  const styles = getStyles(theme, isDarkMode);
  const modalStyles = getModalStyles(theme, isDarkMode);
  const navigation = useNavigation();

  // State for templates
  const [templates, setTemplates] = useState<MatchTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<MatchTemplate | null>(null);
  const [showDropdown, setShowDropdown] = useState(false);
  const [loading, setLoading] = useState(true);

  // State for template editing modal
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<MatchTemplate | null>(null);
  const [newTemplateName, setNewTemplateName] = useState('');

  // Template form state (same as CreateMatchScreen)
  const [teamSize, setTeamSize] = useState(11);
  const [maxSubstitute, setMaxSubstitute] = useState(5);
  const [numberOfPeriods, setNumberOfPeriods] = useState(2);
  const [periodLengths, setPeriodLengths] = useState<number[]>([45, 45]);
  const [halfTime, setHalfTime] = useState(0);
  const [extraTime, setExtraTime] = useState('No');
  const [extraTimeLengths, setExtraTimeLengths] = useState<[number, number]>([15, 15]);
  const [SubstituteOpportunities, setSubstituteOpportunities] = useState('No');
  const [SubstituteOpportunitiesAllowance, setSubstituteOpportunitiesAllowance] = useState(1);
  const [rollOnRollOff, setRollOnRollOff] = useState('No');
  const [extraTimeSubOpportunities, setExtraTimeSubOpportunities] = useState('No');
  const [extraTimeSubOpportunitiesAllowance, setExtraTimeSubOpportunitiesAllowance] = useState(1);
  const [extraTimeAdditionalSub, setExtraTimeAdditionalSub] = useState('No');
  const [extraTimeAdditionalSubAllowance, setExtraTimeAdditionalSubAllowance] = useState(1);
  const [penalties, setPenalties] = useState('No');
  const [misconductCode, setMisconductCode] = useState('Wales');
  const [misconductSets] = useState<string[]>(['Wales', 'England with FA Codes']);
  const [temporaryDismissals, setTemporaryDismissals] = useState('No');
  const [temporaryDismissalsTime, setTemporaryDismissalsTime] = useState(10);
  const [injuryTimeAllowance, setInjuryTimeAllowance] = useState('No');
  const [injuryTime, setInjuryTime] = useState({ subs: 0, sanctions: 0, goals: 0 });

  // Load templates on component mount
  useEffect(() => {
    if (userProfile?.id) {
      loadTemplates();
    }
  }, [userProfile?.id]);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      const loadedTemplates = await TemplateService.getAllTemplates(userProfile?.id);
      setTemplates(loadedTemplates);
      
      // Auto-select first template if available
      if (loadedTemplates.length > 0 && !selectedTemplate) {
        setSelectedTemplate(loadedTemplates[0]);
      }
    } catch (error) {
      console.error('Error loading templates:', error);
      Alert.alert('Error', 'Failed to load templates');
    } finally {
      setLoading(false);
    }
  };

  const handleTemplateSelect = (template: MatchTemplate) => {
    setSelectedTemplate(template);
    setShowDropdown(false);
  };

  const handleCreateNew = () => {
    // Reset form to defaults
    setTeamSize(11);
    setMaxSubstitute(5);
    setNumberOfPeriods(2);
    setPeriodLengths([45, 45]);
    setHalfTime(0);
    setExtraTime('No');
    setExtraTimeLengths([15, 15]);
    setSubstituteOpportunities('No');
    setSubstituteOpportunitiesAllowance(1);
    setRollOnRollOff('No');
    setExtraTimeSubOpportunities('No');
    setExtraTimeSubOpportunitiesAllowance(1);
    setExtraTimeAdditionalSub('No');
    setExtraTimeAdditionalSubAllowance(1);
    setPenalties('No');
    setMisconductCode('Wales');
    setTemporaryDismissals('No');
    setTemporaryDismissalsTime(10);
    setInjuryTimeAllowance('No');
    setInjuryTime({ subs: 0, sanctions: 0, goals: 0 });
    
    setEditingTemplate(null);
    setNewTemplateName('');
    setShowEditModal(true);
  };

  const handleEditTemplate = (template: MatchTemplate) => {
    // Convert template to form data using TemplateService
    const formData = TemplateService.applyTemplateToForm(template as any);
    
    // Load template data into form
    setTeamSize(formData.teamSize);
    setMaxSubstitute(formData.maxSubstitute);
    setNumberOfPeriods(formData.numberOfPeriods);
    setPeriodLengths(formData.periodLengths);
    setHalfTime(formData.halfTime);
    setExtraTime(formData.extraTime);
    setExtraTimeLengths(formData.extraTimeLengths);
    setSubstituteOpportunities(formData.SubstituteOpportunities);
    setSubstituteOpportunitiesAllowance(formData.SubstituteOpportunitiesAllowance);
    setRollOnRollOff(formData.rollOnRollOff);
    setExtraTimeSubOpportunities(formData.extraTimeSubOpportunities);
    setExtraTimeSubOpportunitiesAllowance(formData.extraTimeSubOpportunitiesAllowance);
    setExtraTimeAdditionalSub(formData.extraTimeAdditionalSub);
    setExtraTimeAdditionalSubAllowance(formData.extraTimeAdditionalSubAllowance);
    setPenalties(formData.penalties);
    setMisconductCode(formData.misconductCode);
    setTemporaryDismissals(formData.temporaryDismissals);
    setTemporaryDismissalsTime(formData.temporaryDismissalsTime);
    setInjuryTimeAllowance(formData.injuryTimeAllowance);
    setInjuryTime(formData.injuryTime);
    
    setEditingTemplate(template);
    setNewTemplateName(template.name);
    setShowEditModal(true);
  };

  const handleDuplicateTemplate = (template: MatchTemplate) => {
    // Convert template to form data using TemplateService
    const formData = TemplateService.applyTemplateToForm(template as any);
    
    // Load template data into form
    setTeamSize(formData.teamSize);
    setMaxSubstitute(formData.maxSubstitute);
    setNumberOfPeriods(formData.numberOfPeriods);
    setPeriodLengths(formData.periodLengths);
    setHalfTime(formData.halfTime);
    setExtraTime(formData.extraTime);
    setExtraTimeLengths(formData.extraTimeLengths);
    setSubstituteOpportunities(formData.SubstituteOpportunities);
    setSubstituteOpportunitiesAllowance(formData.SubstituteOpportunitiesAllowance);
    setRollOnRollOff(formData.rollOnRollOff);
    setExtraTimeSubOpportunities(formData.extraTimeSubOpportunities);
    setExtraTimeSubOpportunitiesAllowance(formData.extraTimeSubOpportunitiesAllowance);
    setExtraTimeAdditionalSub(formData.extraTimeAdditionalSub);
    setExtraTimeAdditionalSubAllowance(formData.extraTimeAdditionalSubAllowance);
    setPenalties(formData.penalties);
    setMisconductCode(formData.misconductCode);
    setTemporaryDismissals(formData.temporaryDismissals);
    setTemporaryDismissalsTime(formData.temporaryDismissalsTime);
    setInjuryTimeAllowance(formData.injuryTimeAllowance);
    setInjuryTime(formData.injuryTime);
    
    setEditingTemplate(null); // Not editing, creating new
    setNewTemplateName(`${template.name} (Copy)`);
    setShowEditModal(true);
  };

  const handleDeleteTemplate = (template: MatchTemplate) => {
    Alert.alert(
      'Delete Template',
      `Are you sure you want to delete "${template.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: async () => {
            try {
              await TemplateService.deleteTemplate(template.id);
              await loadTemplates();
              
              // If deleted template was selected, select first available or clear
              if (selectedTemplate?.id === template.id) {
                const remaining = templates.filter(t => t.id !== template.id);
                setSelectedTemplate(remaining.length > 0 ? remaining[0] : null);
              }
              
              Alert.alert('Success', 'Template deleted successfully');
            } catch (error) {
              console.error('Error deleting template:', error);
              Alert.alert('Error', 'Failed to delete template');
            }
          }
        }
      ]
    );
  };

  const handleSaveTemplate = async () => {
    if (!newTemplateName.trim()) {
      Alert.alert('Error', 'Please enter a template name');
      return;
    }

    try {
      // Convert form data to template format using TemplateService
      const formData = {
        teamSize,
        maxSubstitute,
        numberOfPeriods,
        periodLengths,
        halfTime,
        extraTime,
        extraTimeLengths,
        SubstituteOpportunities,
        SubstituteOpportunitiesAllowance,
        extraTimeSubOpportunities,
        extraTimeSubOpportunitiesAllowance,
        extraTimeAdditionalSub,
        extraTimeAdditionalSubAllowance,
        rollOnRollOff,
        penalties,
        misconductCode,
        temporaryDismissals,
        temporaryDismissalsTime,
        injuryTimeAllowance,
        injuryTime,
      };
      
      const templateData = TemplateService.convertFormDataToTemplate(
        formData, 
        newTemplateName.trim(), 
        userProfile?.id || ''
      );

      if (editingTemplate) {
        // Update existing template
        const response = await TemplateService.updateTemplate(editingTemplate.id, templateData);
        if (response.success) {
          Alert.alert('Success', 'Template updated successfully');
        } else {
          throw new Error(response.error || 'Failed to update template');
        }
      } else {
        // Create new template
        const response = await TemplateService.createTemplate(templateData);
        if (response.success && response.data) {
          setSelectedTemplate(response.data as any);
          Alert.alert('Success', 'Template created successfully');
        } else {
          throw new Error(response.error || 'Failed to create template');
        }
      }

      setShowEditModal(false);
      await loadTemplates();
    } catch (error) {
      console.error('Error saving template:', error);
      Alert.alert('Error', 'Failed to save template');
    }
  };

  const handlePeriodLengthChange = (index: number, value: number) => {
    const newLengths = [...periodLengths];
    newLengths[index] = value;
    setPeriodLengths(newLengths);
  };

  const handleExtraTimeLengthChange = (index: number, value: number) => {
    const newLengths: [number, number] = [...extraTimeLengths];
    newLengths[index] = value;
    setExtraTimeLengths(newLengths);
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Header title="Manage Templates" showBackButton={true} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.primary} />
          <Text style={styles.loadingText}>Loading templates...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header title="Manage Templates" showBackButton={true} />
      
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Template Selector */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Selected Template</Text>
          
          <TouchableOpacity
            style={styles.templateSelector}
            onPress={() => setShowDropdown(!showDropdown)}
            activeOpacity={0.7}
          >
            <Text style={styles.templateSelectorText}>
              {selectedTemplate ? selectedTemplate.name : 'No template selected'}
            </Text>
            <ChevronDownIcon color={theme.text} />
          </TouchableOpacity>

          {showDropdown && (
            <View style={styles.dropdownContainer}>
              <TouchableOpacity
                style={[styles.dropdownItem, styles.createNewItem]}
                onPress={handleCreateNew}
                activeOpacity={0.7}
              >
                <View style={styles.createNewContent}>
                  <PlusIcon color={theme.primary} size={18} />
                  <Text style={[styles.dropdownItemText, { color: theme.primary, marginLeft: 8 }]}>
                    Create New Template
                  </Text>
                </View>
              </TouchableOpacity>
              
              {templates.map((template) => (
                <TouchableOpacity
                  key={template.id}
                  style={[
                    styles.dropdownItem,
                    selectedTemplate?.id === template.id && styles.dropdownItemSelected
                  ]}
                  onPress={() => handleTemplateSelect(template)}
                  activeOpacity={0.7}
                >
                  <Text style={[
                    styles.dropdownItemText,
                    selectedTemplate?.id === template.id && styles.dropdownItemTextSelected
                  ]}>
                    {template.name}
                  </Text>
                  {selectedTemplate?.id === template.id && (
                    <Text style={styles.dropdownCheckmark}>✓</Text>
                  )}
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>

        {/* Template Actions */}
        {selectedTemplate && (
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>Template Actions</Text>
            
            <View style={styles.actionButtonsContainer}>
              <TouchableOpacity
                style={[styles.actionButton, styles.editButton]}
                onPress={() => handleEditTemplate(selectedTemplate)}
                activeOpacity={0.7}
              >
                <EditIcon color="#FFFFFF" size={16} />
                <Text style={styles.actionButtonText}>Edit</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.actionButton, styles.duplicateButton]}
                onPress={() => handleDuplicateTemplate(selectedTemplate)}
                activeOpacity={0.7}
              >
                <CopyIcon color="#FFFFFF" size={16} />
                <Text style={styles.actionButtonText}>Duplicate</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.actionButton, styles.deleteButton]}
                onPress={() => handleDeleteTemplate(selectedTemplate)}
                activeOpacity={0.7}
              >
                <DeleteIcon color="#FFFFFF" size={16} />
                <Text style={styles.actionButtonText}>Delete</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Template Preview */}
        {selectedTemplate && (() => {
          // Convert template to display format
          const displayData = TemplateService.applyTemplateToForm(selectedTemplate as any);
          return (
            <View style={styles.sectionContainer}>
              <Text style={styles.sectionTitle}>Template Details</Text>
              
              <View style={styles.templatePreview}>
                <View style={styles.previewRow}>
                  <Text style={styles.previewLabel}>Team Size:</Text>
                  <Text style={styles.previewValue}>{displayData.teamSize}</Text>
                </View>
                <View style={styles.previewRow}>
                  <Text style={styles.previewLabel}>Roll on Roll off:</Text>
                  <Text style={styles.previewValue}>{displayData.rollOnRollOff}</Text>
                </View>
                <View style={styles.previewRow}>
                  <Text style={styles.previewLabel}>Max Substitutions:</Text>
                  <Text style={styles.previewValue}>{displayData.maxSubstitute}</Text>
                </View>
                <View style={styles.previewRow}>
                  <Text style={styles.previewLabel}>Substitute Opportunities:</Text>
                  <Text style={styles.previewValue}>{displayData.SubstituteOpportunities}</Text>
                </View>
                {displayData.SubstituteOpportunities === 'Yes' && (
                  <View style={styles.previewRow}>
                    <Text style={styles.previewLabel}>Substitute Opportunities Allowance:</Text>
                    <Text style={styles.previewValue}>{displayData.SubstituteOpportunitiesAllowance}</Text>
                  </View>
                )}
                <View style={styles.previewRow}>
                  <Text style={styles.previewLabel}>Number of Periods:</Text>
                  <Text style={styles.previewValue}>{displayData.numberOfPeriods}</Text>
                </View>
                <View style={styles.previewRow}>
                  <Text style={styles.previewLabel}>Period Lengths:</Text>
                  <Text style={styles.previewValue}>{displayData.periodLengths.join(', ')} min</Text>
                </View>
                <View style={styles.previewRow}>
                  <Text style={styles.previewLabel}>Half Time:</Text>
                  <Text style={styles.previewValue}>{displayData.halfTime} min</Text>
                </View>
                <View style={styles.previewRow}>
                  <Text style={styles.previewLabel}>Injury Time Allowance:</Text>
                  <Text style={styles.previewValue}>{displayData.injuryTimeAllowance}</Text>
                </View>
                {displayData.injuryTimeAllowance === 'Yes' && (
                  <>
                    <View style={styles.previewRow}>
                      <Text style={styles.previewLabel}>Subs (Seconds):</Text>
                      <Text style={styles.previewValue}>{displayData.injuryTime.subs}</Text>
                    </View>
                    <View style={styles.previewRow}>
                      <Text style={styles.previewLabel}>Sanctions (Seconds):</Text>
                      <Text style={styles.previewValue}>{displayData.injuryTime.sanctions}</Text>
                    </View>
                    <View style={styles.previewRow}>
                      <Text style={styles.previewLabel}>Goals (Seconds):</Text>
                      <Text style={styles.previewValue}>{displayData.injuryTime.goals}</Text>
                    </View>
                  </>
                )}
                <View style={styles.previewRow}>
                  <Text style={styles.previewLabel}>Extra Time:</Text>
                  <Text style={styles.previewValue}>
                    {displayData.extraTime === 'Yes' 
                      ? `${displayData.extraTimeLengths[0]} + ${displayData.extraTimeLengths[1]} min`
                      : 'No'
                    }
                  </Text>
                </View>
                {displayData.extraTime === 'Yes' && (
                  <>
                    <View style={styles.previewRow}>
                      <Text style={styles.previewLabel}>Extra time additional sub opportunities:</Text>
                      <Text style={styles.previewValue}>{displayData.extraTimeSubOpportunities}</Text>
                    </View>
                    {displayData.extraTimeSubOpportunities === 'Yes' && (
                      <View style={styles.previewRow}>
                        <Text style={styles.previewLabel}>Extra time sub opportunities allowance:</Text>
                        <Text style={styles.previewValue}>{displayData.extraTimeSubOpportunitiesAllowance}</Text>
                      </View>
                    )}
                    <View style={styles.previewRow}>
                      <Text style={styles.previewLabel}>Extra Time additional Sub:</Text>
                      <Text style={styles.previewValue}>{displayData.extraTimeAdditionalSub}</Text>
                    </View>
                    {displayData.extraTimeAdditionalSub === 'Yes' && (
                      <View style={styles.previewRow}>
                        <Text style={styles.previewLabel}>Extra Time additional Sub allowance:</Text>
                        <Text style={styles.previewValue}>{displayData.extraTimeAdditionalSubAllowance}</Text>
                      </View>
                    )}
                  </>
                )}
                <View style={styles.previewRow}>
                  <Text style={styles.previewLabel}>Penalties:</Text>
                  <Text style={styles.previewValue}>{displayData.penalties}</Text>
                </View>
                <View style={styles.previewRow}>
                  <Text style={styles.previewLabel}>Misconduct Code:</Text>
                  <Text style={styles.previewValue}>{displayData.misconductCode}</Text>
                </View>
                <View style={styles.previewRow}>
                  <Text style={styles.previewLabel}>Temporary Dismissals:</Text>
                  <Text style={styles.previewValue}>
                    {displayData.temporaryDismissals === 'Yes'
                      ? `${displayData.temporaryDismissalsTime} min`
                      : 'No'
                    }
                  </Text>
                </View>
              </View>
            </View>
          );
        })()}

        {templates.length === 0 && (
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateTitle}>No Templates Found</Text>
            <Text style={styles.emptyStateText}>
              You haven't created any templates yet. Create your first template to get started.
            </Text>
            <TouchableOpacity
              style={styles.createNewButton}
              onPress={handleCreateNew}
              activeOpacity={0.7}
            >
              <PlusIcon color="#FFFFFF" size={18} />
              <Text style={styles.createNewButtonText}>Create First Template</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>

      {/* Edit Template Modal */}
      <Modal
        animationType="slide"
        transparent={false}
        visible={showEditModal}
        onRequestClose={() => setShowEditModal(false)}
        statusBarTranslucent={true}
      >
        <View style={modalStyles.container}>
          <Header 
            title={editingTemplate ? 'Edit Template' : 'Create Template'} 
            showBackButton={true}
            onBackPress={() => setShowEditModal(false)}
          />
          
          <ScrollView style={modalStyles.scrollView} contentContainerStyle={modalStyles.scrollContent}>
            {/* Template Name */}
            <View style={modalStyles.sectionContainer}>
              <Text style={modalStyles.sectionTitle}>Template Name</Text>
              <TextInput
                style={modalStyles.textInput}
                value={newTemplateName}
                onChangeText={setNewTemplateName}
                placeholder="Enter template name"
                placeholderTextColor={theme.textSecondary}
              />
            </View>

            {/* Basic Settings */}
            <View style={modalStyles.sectionContainer}>
              <Text style={modalStyles.sectionTitle}>Basic Settings</Text>
              
              <NumericInputRow
                label="Team Size"
                value={teamSize}
                onValueChange={setTeamSize}
                styles={modalStyles}
                theme={theme}
              />
              <PickerLikeRow
                label="Roll on Roll off"
                value={rollOnRollOff}
                options={['Yes', 'No']}
                onValueChange={setRollOnRollOff}
                styles={modalStyles}
                theme={theme}
              />
              <NumericInputRow
                label="Max Substitutions"
                value={maxSubstitute}
                onValueChange={setMaxSubstitute}
                styles={modalStyles}
                theme={theme}
              />
              
              <PickerLikeRow
                label="Substitute Opportunities"
                value={SubstituteOpportunities}
                options={['Yes', 'No']}
                onValueChange={setSubstituteOpportunities}
                styles={modalStyles}
                theme={theme}
              />

              {SubstituteOpportunities === 'Yes' && (
                <NumericInputRow
                  label="Substitute Opportunities Allowance"
                  value={SubstituteOpportunitiesAllowance}
                  onValueChange={setSubstituteOpportunitiesAllowance}
                  styles={modalStyles}
                  theme={theme}
                />
              )}
              <NumericInputRow
                label="Number of Periods"
                value={numberOfPeriods}
                onValueChange={setNumberOfPeriods}
                styles={modalStyles}
                theme={theme}
              />

              {/* Period Lengths */}
              {Array.from({ length: numberOfPeriods }).map((_, index) => (
                <NumericInputRow
                  key={index}
                  label={`Period ${index + 1} Length (minutes)`}
                  value={periodLengths[index] || 45}
                  onValueChange={(value) => handlePeriodLengthChange(index, value)}
                  styles={modalStyles}
                  theme={theme}
                />
              ))}

              <NumericInputRow
                label="Half Time (minutes)"
                value={halfTime}
                onValueChange={setHalfTime}
                styles={modalStyles}
                theme={theme}
              />
               <PickerLikeRow
                label="Injury Time Allowance"
                value={injuryTimeAllowance}
                options={['Yes', 'No']}
                onValueChange={setInjuryTimeAllowance}
                styles={modalStyles}
                theme={theme}
              />

              {injuryTimeAllowance === 'Yes' && (
                <>
                  <NumericInputRow
                    label="Substitutions (seconds)"
                    value={injuryTime.subs}
                    onValueChange={(value) => setInjuryTime(prev => ({ ...prev, subs: value }))}
                    styles={modalStyles}
                    theme={theme}
                  />
                  <NumericInputRow
                    label="Sanctions (seconds)"
                    value={injuryTime.sanctions}
                    onValueChange={(value) => setInjuryTime(prev => ({ ...prev, sanctions: value }))}
                    styles={modalStyles}
                    theme={theme}
                  />
                  <NumericInputRow
                    label="Goals (seconds)"
                    value={injuryTime.goals}
                    onValueChange={(value) => setInjuryTime(prev => ({ ...prev, goals: value }))}
                    styles={modalStyles}
                    theme={theme}
                  />
                </>
              )}
              <PickerLikeRow
                label="Extra Time"
                value={extraTime}
                options={['Yes', 'No']}
                onValueChange={setExtraTime}
                styles={modalStyles}
                theme={theme}
              />

              {extraTime === 'Yes' && (
                <>
                  <NumericInputRow
                    label="Extra Time Period 1 (minutes)"
                    value={extraTimeLengths[0]}
                    onValueChange={(value) => handleExtraTimeLengthChange(0, value)}
                    styles={modalStyles}
                    theme={theme}
                  />
                  <NumericInputRow
                    label="Extra Time Period 2 (minutes)"
                    value={extraTimeLengths[1]}
                    onValueChange={(value) => handleExtraTimeLengthChange(1, value)}
                    styles={modalStyles}
                    theme={theme}
                  />
                   <PickerLikeRow
                    label="Extra time additional sub opportunities"
                    value={extraTimeSubOpportunities}
                    options={['Yes', 'No']}
                    onValueChange={setExtraTimeSubOpportunities}
                    styles={modalStyles}
                    theme={theme}
                  />

                  {extraTimeSubOpportunities === 'Yes' && (
                    <NumericInputRow
                      label="Extra time sub opportunities allowance"
                      value={extraTimeSubOpportunitiesAllowance}
                      onValueChange={setExtraTimeSubOpportunitiesAllowance}
                      styles={modalStyles}
                      theme={theme}
                    />
                  )}

                  <PickerLikeRow
                    label="Extra Time additional Sub"
                    value={extraTimeAdditionalSub}
                    options={['Yes', 'No']}
                    onValueChange={setExtraTimeAdditionalSub}
                    styles={modalStyles}
                    theme={theme}
                  />

                  {extraTimeAdditionalSub === 'Yes' && (
                    <NumericInputRow
                      label="Extra Time additional Sub allowance"
                      value={extraTimeAdditionalSubAllowance}
                      onValueChange={setExtraTimeAdditionalSubAllowance}
                      styles={modalStyles}
                      theme={theme}
                    />
                  )}
                </>
              )}
              <PickerLikeRow
                label="Penalties"
                value={penalties}
                options={['Yes', 'No']}
                onValueChange={setPenalties}
                styles={modalStyles}
                theme={theme}
              />
              <PickerLikeRow
                label="Misconduct code"
                value={misconductCode}
                options={misconductSets}
                onValueChange={setMisconductCode}
                styles={modalStyles}
                theme={theme}
              />

              <PickerLikeRow
                label="Temporary dismissals"
                value={temporaryDismissals}
                options={['Yes', 'No']}
                onValueChange={setTemporaryDismissals}
                styles={modalStyles}
                theme={theme}
              />

              {temporaryDismissals === 'Yes' && (
                <NumericInputRow
                  label="Temporary dismissals time"
                  value={temporaryDismissalsTime}
                  onValueChange={setTemporaryDismissalsTime}
                  styles={modalStyles}
                  theme={theme}
                />
              )}
            </View>

            {/* Save Button */}
            <TouchableOpacity
              style={modalStyles.saveButton}
              onPress={handleSaveTemplate}
              activeOpacity={0.7}
            >
              <Text style={modalStyles.saveButtonText}>
                {editingTemplate ? 'Update Template' : 'Create Template'}
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
};

const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: theme.text,
    marginTop: 12,
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.text,
    marginBottom: 12,
  },
  templateSelector: {
    backgroundColor: theme.card,
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.1,
    shadowRadius: 2,
    elevation: isDarkMode ? 0 : 2,
  },
  templateSelectorText: {
    fontSize: 16,
    color: theme.text,
    fontWeight: '500',
  },
  dropdownContainer: {
    backgroundColor: theme.card,
    borderRadius: 12,
    marginTop: 8,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: isDarkMode ? 0 : 0.15,
    shadowRadius: 4,
    elevation: isDarkMode ? 0 : 4,
  },
  dropdownItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.border,
  },
  dropdownItemSelected: {
    backgroundColor: `${theme.primary}15`,
  },
  dropdownItemText: {
    fontSize: 16,
    color: theme.text,
  },
  dropdownItemTextSelected: {
    color: theme.primary,
    fontWeight: '600',
  },
  dropdownCheckmark: {
    color: theme.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  createNewItem: {
    borderBottomWidth: 2,
    borderBottomColor: theme.border,
  },
  createNewContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  editButton: {
    backgroundColor: '#007AFF',
  },
  duplicateButton: {
    backgroundColor: '#34C759',
  },
  deleteButton: {
    backgroundColor: '#FF3B30',
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  templatePreview: {
    backgroundColor: theme.card,
    borderRadius: 12,
    padding: 16,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.1,
    shadowRadius: 2,
    elevation: isDarkMode ? 0 : 2,
  },
  previewRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  previewLabel: {
    fontSize: 14,
    color: theme.textSecondary,
    flex: 1,
  },
  previewValue: {
    fontSize: 14,
    color: theme.text,
    fontWeight: '500',
    textAlign: 'right',
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.text,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 16,
    color: theme.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  createNewButton: {
    backgroundColor: theme.primary,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    gap: 8,
  },
  createNewButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

const getModalStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.background,
    paddingTop: 0, // Remove any top padding that might cause white gap
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.text,
    marginBottom: 12,
  },
  textInput: {
    backgroundColor: theme.card,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: theme.text,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.1,
    shadowRadius: 2,
    elevation: isDarkMode ? 0 : 2,
  },
  inputField: {
    backgroundColor: theme.card,
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.1,
    shadowRadius: 2,
    elevation: isDarkMode ? 0 : 2,
  },
  inputLabel: {
    fontSize: 16,
    color: theme.text,
    fontWeight: '500',
    flex: 1,
  },
  inputValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputValueText: {
    fontSize: 16,
    color: theme.text,
    marginRight: 8,
  },
  numericInputControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  numericButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
  },
  numericValueText: {
    fontSize: 16,
    color: theme.text,
    fontWeight: '600',
    minWidth: 40,
    textAlign: 'center',
  },
  saveButton: {
    backgroundColor: theme.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 24,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
});

export default ManageTemplateScreen;