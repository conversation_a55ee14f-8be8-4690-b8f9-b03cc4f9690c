// API Configuration
export const API_CONFIG = {
  // Use your local IP address or localhost
  // For Android emulator, use ********
  // For iOS simulator, use localhost
  // For physical device, use your computer's IP address
  BASE_URL: __DEV__
    ? 'http://*************:3000'  // Development - For Android emulator with Docker
    : 'https://your-production-api.com', // Production
  
  TIMEOUT: 10000, // 10 seconds
  
  ENDPOINTS: {
    // Auth endpoints
    AUTH: {
      VERIFY_TOKEN: '/auth/verify-token',
    },
    
    // Match endpoints
    MATCHES: {
      BASE: '/matches',
      UPCOMING: '/matches/upcoming',
      PREVIOUS: '/matches/previous',
      BY_ID: (id: string) => `/matches/${id}`,
      UPDATE_STATUS: (id: string) => `/matches/${id}/status`,
      MATCH_LOGS: (id: string) => `/matches/${id}/logs`, // Add endpoint for match logs
      FILTER_OPTIONS: '/matches/filters/options',
      FILTERED_MATCHES: '/matches/filters/search',
    },
    
    // Misconduct endpoints
    MISCONDUCT: {
      BASE: '/misconduct-sets',
      BY_USER: (userId: string) => `/misconduct-sets/${userId}`,
      BY_ID: (id: string) => `/misconduct-sets/${id}`,
    },
    
    // Evaluation endpoints
    EVALUATION: {
      QUESTIONS: {
        BASE: '/evaluation-questions',
        BY_USER: (userId: string) => `/evaluation-questions/${userId}`,
        UPDATE: (userId: string) => `/evaluation-questions/${userId}`,
      },
      ANSWERS: {
        BASE: '/evaluation-answers',
        BY_MATCH: (matchId: string) => `/evaluation-answers/match/${matchId}`,
        CREATE: '/evaluation-answers',
      },
    },
  }
};

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message: string;
  isExpectedError?: boolean; // Flag for expected errors like squad not found
}

// Match types for API
export interface CreateMatchRequest {
  // Basic match info
  competition: string;
  venue: string;
  matchDate: string;
  officialRole?: string;

  // Home team
  homeTeamName: string;
  homeTeamShortName?: string;
  homeKitBase?: string;
  homeKitStripe?: string;

  // Away team
  awayTeamName: string;
  awayTeamShortName?: string;
  awayKitBase?: string;
  awayKitStripe?: string;

  // Match format
  teamSize?: number;
  maxSubstitute?: number;
  numberOfPeriods?: number;
  periodLengths?: number[];
  halfTime?: number;

  // Extra time settings
  extraTime?: boolean;
  extraTimeLengths?: [number, number];

  // Substitution rules
  substituteOpportunities?: boolean;
  substituteOpportunitiesAllowance?: number;
  extraTimeSubOpportunities?: boolean;
  extraTimeSubOpportunitiesAllowance?: number;
  extraTimeAdditionalSub?: boolean;
  extraTimeAdditionalSubAllowance?: number;
  rollOnRollOff?: boolean;

  // Other rules
  penalties?: boolean;
  misconductCode?: string;
  temporaryDismissals?: boolean;
  temporaryDismissalsTime?: number;

  // Injury time
  injuryTimeAllowance?: boolean;
  injuryTimeSubs?: number;
  injuryTimeSanctions?: number;
  injuryTimeGoals?: number;

  // Match officials
  matchOfficials?: Array<{ role: string; name: string }>;

  // Earnings
  fees?: string;
  expenses?: string;
  mileage?: string;
  travelTime?: string;

  // Notes
  notes?: string;

  // Template info
  templateName?: string;
}

export interface MatchResponse {
  id: string;
  competition: string;
  venue: string;
  matchDate: string;
  officialRole: string;
  
  homeTeamName: string;
  homeTeamShortName?: string;
  homeKitBase: string;
  homeKitStripe: string;
  
  awayTeamName: string;
  awayTeamShortName?: string;
  awayKitBase: string;
  awayKitStripe: string;
  
  // Match format details
  teamSize?: number;
  maxSubstitute?: number;
  numberOfPeriods?: number;
  periodLengths?: number[];
  halfTime?: number;
  
  // Extra time settings
  extraTime?: boolean;
  extraTimeLengths?: [number, number];
  
  // Substitution rules
  substituteOpportunities?: boolean;
  substituteOpportunitiesAllowance?: number;
  extraTimeSubOpportunities?: boolean;
  extraTimeSubOpportunitiesAllowance?: number;
  extraTimeAdditionalSub?: boolean;
  extraTimeAdditionalSubAllowance?: number;
  rollOnRollOff?: boolean;
  
  // Other rules
  penalties?: boolean;
  temporaryDismissals?: boolean;
  temporaryDismissalsTime?: number;
  
  // Injury time settings
  injuryTimeAllowance?: boolean;
  injuryTimeSubs?: number;
  injuryTimeSanctions?: number;
  injuryTimeGoals?: number;
  
  // Match officials
  matchOfficials?: Array<{ role: string; name: string }>;
  
  // Earnings
  fees?: string;
  expenses?: string;
  mileage?: string;
  travelTime?: string;
  
  // Notes
  notes?: string;
  
  // Misconduct code
  misconductCode?: string;
  
  // Template info
  templateName?: string;
  
  // Match summary with creation and end info
  summary?: any;
  
  status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  createdAt: string;
  updatedAt: string;
  
  referee: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
}

// Add MatchLogEvent interface
export interface MatchLogEvent {
  id: string;
  type: 'period_start' | 'period_end' | 'halftime_start' | 'halftime_end' | 'extra_time_start' | 
        'card' | 'substitution' | 'goal' | 'sin_bin_start' | 'sin_bin_end' | 'warning' | 
        'injury_time_added' | 'match_completed' | 'match_abandoned' | 'penalty_start' | 
        'penalty_shot' | 'penalty_end' | 'penalty_gk_substitution';
  timestamp: number; // Match time in seconds
  realTime: string | Date; // ISO string format or Date object
  data?: any;
}
