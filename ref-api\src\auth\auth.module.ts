import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { FirebaseService } from './firebase.service';
import { PrismaService } from '../prisma.service';
import { UsersService } from '../users/users.service';

@Module({
  providers: [AuthService, FirebaseService, PrismaService, UsersService],
  controllers: [AuthController],
  exports: [AuthService, FirebaseService, UsersService],
})
export class AuthModule {}
