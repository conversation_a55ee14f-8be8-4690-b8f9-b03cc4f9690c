import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  GoogleAuthProvider,
  FacebookAuthProvider,
  signInWithCredential,
  updateProfile,
  sendPasswordResetEmail,
  sendEmailVerification,
  deleteUser,
  updatePassword,
  reauthenticateWithCredential,
  EmailAuthProvider,
} from 'firebase/auth';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { LoginManager, AccessToken } from 'react-native-fbsdk-next';

// Initialize Firebase auth
import '../firebaseConfig';
import { auth } from '../firebaseConfig';

// Configure the native Google Sign-In SDK
GoogleSignin.configure({
  webClientId: '************-692lcotd6v1hdntqhduf9jqvqak1to7o.apps.googleusercontent.com',
  iosClientId: 'com.googleusercontent.apps.************-692lcotd6v1hdntqhduf9jqvqak1to7o',
  offlineAccess: false,
  forceCodeForRefreshToken: true,
  accountName: '',
  hostedDomain: '',
  loginHint: '',
  includeServerAuthCode: false,
});

class AuthService {
  constructor() {
    if (!auth) throw new Error('Firebase auth not initialized');
    this.auth = auth;
    this.currentUser = null;
  }

  getCurrentUser() {
    return this.auth.currentUser;
  }

  async signInWithEmail(email, password) {
    try {
      const { user } = await signInWithEmailAndPassword(this.auth, email, password);
      return { success: true, user, message: 'Signed in with email' };
    } catch (e) {
      console.error('Email sign-in error:', e);
      return { success: false, error: e.code, message: this.getErrorMessage(e.code) };
    }
  }

  async createAccount(email, password, displayName) {
    try {
      const { user } = await createUserWithEmailAndPassword(this.auth, email, password);
      if (displayName) {
        await updateProfile(user, { displayName });
      }
      await sendEmailVerification(user);
      return { success: true, user, message: 'Account created successfully' };
    } catch (e) {
      console.error('Account creation error:', e);
      return { success: false, error: e.code, message: this.getErrorMessage(e.code) };
    }
  }

  // Main Google Sign-In method
  async signInWithGoogle() {
    return this.signInWithGoogleNative();
  }

  // Native Google Sign-In for Expo native builds
  async signInWithGoogleNative() {
    try {
      await GoogleSignin.hasPlayServices({
        showPlayServicesUpdateDialog: true
      });

      // Sign out first to force account selection
      try {
        await GoogleSignin.signOut();
      } catch (signOutError) {
        // Ignore sign out errors - user might not be signed in
        console.log('Google sign out (for account selection):', signOutError.message);
      }

      const userInfo = await GoogleSignin.signIn();
      console.log('Google user info:', userInfo);

      const idToken = userInfo.data?.idToken || userInfo.idToken;
      if (!idToken) {
        throw new Error('Failed to retrieve Google ID token');
      }

      const credential = GoogleAuthProvider.credential(idToken);
      const { user } = await signInWithCredential(this.auth, credential);

      return {
        success: true,
        user,
        message: 'Successfully signed in with Google'
      };
    } catch (error) {
      console.error('Google Sign-In Error:', error);

      if (error.code === 'SIGN_IN_CANCELLED') {
        return {
          success: false,
          error: 'cancelled',
          message: 'Google sign-in was cancelled'
        };
      }

      if (error.code === 'IN_PROGRESS') {
        return {
          success: false,
          error: 'in_progress',
          message: 'Google sign-in already in progress'
        };
      }

      if (error.code === 'PLAY_SERVICES_NOT_AVAILABLE') {
        return {
          success: false,
          error: 'play_services',
          message: 'Google Play Services not available'
        };
      }

      return {
        success: false,
        error: error.code || 'unknown',
        message: error.message || 'Google sign-in failed'
      };
    }
  }

  // Facebook Sign-In method
  async signInWithFacebook() {
    try {
      console.log('Starting Facebook sign-in...');

      // Request permissions and login
      LoginManager.setLoginBehavior('native_with_fallback');
      const result = await LoginManager.logInWithPermissions(['public_profile', 'email']);

      if (result.isCancelled) {
        return {
          success: false,
          error: 'cancelled',
          message: 'Facebook sign-in was cancelled'
        };
      }

      console.log('Facebook login result:', result);

      // Get the access token
      const data = await AccessToken.getCurrentAccessToken();

      if (!data) {
        throw new Error('Failed to get Facebook access token');
      }

      console.log('Facebook access token:', data.accessToken);

      // Create Firebase credential with Facebook access token
      const credential = FacebookAuthProvider.credential(data.accessToken);

      // Sign in to Firebase with the credential
      const { user } = await signInWithCredential(this.auth, credential);

      return {
        success: true,
        user,
        message: 'Successfully signed in with Facebook'
      };
    } catch (error) {
      console.error('Facebook Sign-In Error:', error);

      // Handle specific Facebook errors
      if (error.code === 'auth/account-exists-with-different-credential') {
        return {
          success: false,
          error: 'account_exists',
          message: 'An account already exists with the same email address but different sign-in credentials.'
        };
      }

      if (error.code === 'auth/invalid-credential') {
        return {
          success: false,
          error: 'invalid_credential',
          message: 'Invalid Facebook credentials.'
        };
      }

      return {
        success: false,
        error: error.code || 'unknown',
        message: error.message || 'Facebook sign-in failed'
      };
    }
  }

  async signOut() {
    try {
      // Sign out from Firebase
      await firebaseSignOut(this.auth);

      // Sign out from Google
      try {
        await GoogleSignin.signOut();
      } catch (googleError) {
        console.warn('Google sign-out warning:', googleError);
      }

      // Sign out from Facebook
      try {
        LoginManager.logOut();
      } catch (facebookError) {
        console.warn('Facebook sign-out warning:', facebookError);
      }

      return { success: true, message: 'Successfully signed out' };
    } catch (e) {
      console.error('Sign-out error:', e);
      return { success: false, error: e.code, message: this.getErrorMessage(e.code) };
    }
  }

  async resetPassword(email) {
    try {
      await sendPasswordResetEmail(this.auth, email);
      return { success: true, message: 'Password reset email sent' };
    } catch (e) {
      console.error('Password reset error:', e);
      return { success: false, error: e.code, message: this.getErrorMessage(e.code) };
    }
  }

  async updateUserPassword(currentPassword, newPassword) {
    try {
      const user = this.auth.currentUser;
      if (!user) throw new Error('No user signed in');

      const credential = EmailAuthProvider.credential(user.email, currentPassword);
      await reauthenticateWithCredential(user, credential);

      await updatePassword(user, newPassword);
      return { success: true, message: 'Password updated successfully' };
    } catch (e) {
      console.error('Password update error:', e);
      return { success: false, error: e.code, message: this.getErrorMessage(e.code) };
    }
  }

  async deleteAccount(password) {
    try {
      const user = this.auth.currentUser;
      if (!user) throw new Error('No user signed in');

      if (password) {
        const credential = EmailAuthProvider.credential(user.email, password);
        await reauthenticateWithCredential(user, credential);
      }

      await deleteUser(user);
      return { success: true, message: 'Account deleted successfully' };
    } catch (e) {
      console.error('Account deletion error:', e);
      return { success: false, error: e.code, message: this.getErrorMessage(e.code) };
    }
  }

  onAuthStateChange(callback) {
    return onAuthStateChanged(this.auth, (user) => {
      this.currentUser = user;
      callback(user);
    });
  }

  getErrorMessage(code) {
    const messages = {
      'auth/user-not-found': 'No account found with this email.',
      'auth/wrong-password': 'Incorrect password.',
      'auth/email-already-in-use': 'Email is already registered.',
      'auth/weak-password': 'Password is too weak. Use at least 6 characters.',
      'auth/invalid-email': 'Invalid email address.',
      'auth/user-disabled': 'This account has been disabled.',
      'auth/too-many-requests': 'Too many failed attempts. Try again later.',
      'auth/network-request-failed': 'Network error. Check your connection.',
      'auth/invalid-credential': 'Invalid credentials provided.',
      'auth/account-exists-with-different-credential': 'Account exists with different sign-in method.',
      'auth/operation-not-allowed': 'This authentication method is not enabled.',
      'auth/credential-already-in-use': 'This credential is already associated with another account.',
      'auth/invalid-verification-code': 'Invalid verification code.',
      'auth/invalid-verification-id': 'Invalid verification ID.',
      'auth/missing-verification-code': 'Missing verification code.',
      'auth/missing-verification-id': 'Missing verification ID.',
      'auth/code-expired': 'Verification code has expired.',
      'auth/requires-recent-login': 'Please sign in again to complete this action.',
    };
    return messages[code] || 'An unexpected error occurred. Please try again.';
  }

  isEmailVerified() {
    return this.auth.currentUser?.emailVerified || false;
  }

  async resendEmailVerification() {
    try {
      const user = this.auth.currentUser;
      if (!user) throw new Error('No user signed in');

      await sendEmailVerification(user);
      return { success: true, message: 'Verification email sent' };
    } catch (e) {
      console.error('Email verification error:', e);
      return { success: false, error: e.code || 'unknown', message: e.message };
    }
  }

  async updateDisplayName(displayName) {
    try {
      const user = this.auth.currentUser;
      if (!user) throw new Error('No user signed in');

      await updateProfile(user, { displayName });
      return { success: true, message: 'Display name updated' };
    } catch (e) {
      console.error('Display name update error:', e);
      return { success: false, error: e.code, message: this.getErrorMessage(e.code) };
    }
  }

  async updatePhotoURL(photoURL) {
    try {
      const user = this.auth.currentUser;
      if (!user) throw new Error('No user signed in');

      await updateProfile(user, { photoURL });
      return { success: true, message: 'Profile photo updated' };
    } catch (e) {
      console.error('Photo URL update error:', e);
      return { success: false, error: e.code, message: this.getErrorMessage(e.code) };
    }
  }

  // Google helper methods
  async isGoogleSignedIn() {
    try {
      return await GoogleSignin.isSignedIn();
    } catch (error) {
      console.error('Error checking Google sign-in status:', error);
      return false;
    }
  }

  async getCurrentGoogleUser() {
    try {
      return await GoogleSignin.getCurrentUser();
    } catch (error) {
      console.error('Error getting current Google user:', error);
      return null;
    }
  }

  async revokeGoogleAccess() {
    try {
      await GoogleSignin.revokeAccess();
      return { success: true, message: 'Google access revoked' };
    } catch (error) {
      console.error('Error revoking Google access:', error);
      return { success: false, error: error.code, message: error.message };
    }
  }

  // Facebook helper methods
  async isFacebookLoggedIn() {
    try {
      const accessToken = await AccessToken.getCurrentAccessToken();
      return accessToken !== null;
    } catch (error) {
      console.error('Error checking Facebook login status:', error);
      return false;
    }
  }

  async getCurrentFacebookAccessToken() {
    try {
      return await AccessToken.getCurrentAccessToken();
    } catch (error) {
      console.error('Error getting Facebook access token:', error);
      return null;
    }
  }
}

// Export singleton instance
export default new AuthService();
export { AuthService };
