import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  StatusBar,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { useUser } from '../contexts/UserContext';
import EvaluationService, { EvaluationQuestion, EvaluationAnswer } from '../api/evaluationService';
import Svg, { Circle, Path } from 'react-native-svg';

interface MatchEvaluationModalProps {
  visible: boolean;
  matchId: string;
  onComplete: (answers: EvaluationAnswer[]) => void;
  onClose: () => void;
}

// Emoji face components (same as MatchResultsScreen)
const Face1_VeryDissatisfied = ({ size = 48, isSelected = false }: { size?: number; isSelected?: boolean }) => (
  <Svg height={size} width={size} viewBox="0 0 24 24">
    <Circle cx="12" cy="12" r="11.5" fill={isSelected ? "#FF0000" : "#CCCCCC"} stroke="black" strokeWidth="0.5" />
    <Path d="M9 9V11" stroke="black" strokeWidth="1.5" strokeLinecap="round" />
    <Path d="M15 9V11" stroke="black" strokeWidth="1.5" strokeLinecap="round" />
    <Path d="M8 16 Q 12 13 16 16" stroke="black" strokeWidth="1.5" fill="none" strokeLinecap="round" />
  </Svg>
);

const Face2_Dissatisfied = ({ size = 48, isSelected = false }: { size?: number; isSelected?: boolean }) => (
  <Svg height={size} width={size} viewBox="0 0 24 24">
    <Circle cx="12" cy="12" r="11.5" fill={isSelected ? "#EE7D4C" : "#CCCCCC"} stroke="black" strokeWidth="0.5" />
    <Path d="M9 9V11" stroke="black" strokeWidth="1.5" strokeLinecap="round" />
    <Path d="M15 9V11" stroke="black" strokeWidth="1.5" strokeLinecap="round" />
    <Path d="M8 15 Q 12 14 16 15" stroke="black" strokeWidth="1.5" fill="none" strokeLinecap="round" />
  </Svg>
);

const Face3_Somehow = ({ size = 48, isSelected = false }: { size?: number; isSelected?: boolean }) => (
  <Svg height={size} width={size} viewBox="0 0 24 24">
    <Circle cx="12" cy="12" r="11.5" fill={isSelected ? "#FFDF0F" : "#CCCCCC"} stroke="black" strokeWidth="0.5" />
    <Path d="M9 9V11" stroke="black" strokeWidth="1.5" strokeLinecap="round" />
    <Path d="M15 9V11" stroke="black" strokeWidth="1.5" strokeLinecap="round" />
    <Path d="M8 15 H 16" stroke="black" strokeWidth="1.5" fill="none" strokeLinecap="round" />
  </Svg>
);

const Face4_Satisfied = ({ size = 48, isSelected = false }: { size?: number; isSelected?: boolean }) => (
  <Svg height={size} width={size} viewBox="0 0 24 24">
    <Circle cx="12" cy="12" r="11.5" fill={isSelected ? "#7FD120" : "#CCCCCC"} stroke="black" strokeWidth="0.5" />
    <Path d="M9 9V11" stroke="black" strokeWidth="1.5" strokeLinecap="round" />
    <Path d="M15 9V11" stroke="black" strokeWidth="1.5" strokeLinecap="round" />
    <Path d="M8 14 Q 12 16 16 14" stroke="black" strokeWidth="1.5" fill="none" strokeLinecap="round" />
  </Svg>
);

const Face5_VerySatisfied = ({ size = 48, isSelected = false }: { size?: number; isSelected?: boolean }) => (
  <Svg height={size} width={size} viewBox="0 0 24 24">
    <Circle cx="12" cy="12" r="11.5" fill={isSelected ? "#00C42A" : "#CCCCCC"} stroke="black" strokeWidth="0.5" />
    <Path d="M9 9V11" stroke="black" strokeWidth="1.5" strokeLinecap="round" />
    <Path d="M15 9V11" stroke="black" strokeWidth="1.5" strokeLinecap="round" />
    <Path d="M8 13 Q 12 18 16 13" stroke="black" strokeWidth="1.5" fill="none" strokeLinecap="round" />
  </Svg>
);

const EmojiRating = ({ level, size = 48, isSelected = false }: { level: number; size?: number; isSelected?: boolean }) => {
  switch (level) {
    case 1:
      return <Face1_VeryDissatisfied size={size} isSelected={isSelected} />;
    case 2:
      return <Face2_Dissatisfied size={size} isSelected={isSelected} />;
    case 3:
      return <Face3_Somehow size={size} isSelected={isSelected} />;
    case 4:
      return <Face4_Satisfied size={size} isSelected={isSelected} />;
    case 5:
      return <Face5_VerySatisfied size={size} isSelected={isSelected} />;
    default:
      return null;
  }
};

const MatchEvaluationModal: React.FC<MatchEvaluationModalProps> = ({
  visible,
  matchId,
  onComplete,
  onClose,
}) => {
  const { theme, isDarkMode } = useTheme();
  const { userProfile } = useUser();
  const styles = getStyles(theme, isDarkMode);

  const [questions, setQuestions] = useState<EvaluationQuestion[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<number, number>>({});
  const [loading, setLoading] = useState(true);
  const [isCompleting, setIsCompleting] = useState(false);

  // Load evaluation questions when modal opens
  useEffect(() => {
    if (visible) {
      loadQuestions();
    }
  }, [visible]);

  const loadQuestions = async () => {
    try {
      setLoading(true);
      const evaluationQuestions = await EvaluationService.getEvaluationQuestions(userProfile?.id);
      setQuestions(evaluationQuestions);
      setCurrentQuestionIndex(0);
      setAnswers({});
    } catch (error) {
      console.error('Error loading evaluation questions:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRatingSelect = (rating: number) => {
    const currentQuestion = questions[currentQuestionIndex];
    setAnswers(prev => ({
      ...prev,
      [currentQuestion.id]: rating
    }));
  };

  const handleNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const handlePrev = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleDone = async () => {
    try {
      setIsCompleting(true);

      // Convert answers to EvaluationAnswer format
      const evaluationAnswers: EvaluationAnswer[] = questions.map(question => {
        const rating = answers[question.id] || 1;
        return {
          questionId: question.id,
          question: question.text,
          rating: rating,
          label: EvaluationService.getRatingLabel(rating),
          emoji: rating // Store emoji level (1-5 corresponding to the emoji faces)
        };
      });

      // Save to database
      await EvaluationService.saveMatchEvaluation(matchId, userProfile?.id || '', evaluationAnswers);

      // Call completion callback
      onComplete(evaluationAnswers);
    } catch (error) {
      console.error('Error saving evaluation:', error);
    } finally {
      setIsCompleting(false);
    }
  };

  const isAllAnswered = questions.every(q => answers[q.id] !== undefined);
  const isLastQuestion = currentQuestionIndex === questions.length - 1;
  const currentQuestion = questions[currentQuestionIndex];
  const currentRating = currentQuestion ? answers[currentQuestion.id] : undefined;

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={false}
      statusBarTranslucent={true}
      onRequestClose={onClose}
    >
      <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} />
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Evaluation Time</Text>
          <Text style={styles.headerSubtitle}>
            Question {currentQuestionIndex + 1} of {questions.length}
          </Text>
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.primary} />
            <Text style={styles.loadingText}>Loading questions...</Text>
          </View>
        ) : (
          <ScrollView 
            style={styles.scrollView} 
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
            {currentQuestion && (
              <View style={styles.questionContainer}>
                <Text style={styles.questionText}>{currentQuestion.text}</Text>
                
                <Text style={styles.confidenceTitle}>What is your confidence?</Text>
                
                <View style={styles.emojisContainer}>
                  {[1, 2, 3, 4, 5].map(rating => (
                    <TouchableOpacity
                      key={rating}
                      style={styles.emojiButton}
                      onPress={() => handleRatingSelect(rating)}
                      activeOpacity={0.7}
                    >
                      <EmojiRating 
                        level={rating} 
                        size={64}
                        isSelected={currentRating === rating}
                      />
                    </TouchableOpacity>
                  ))}
                </View>

                {currentRating && (
                  <Text style={styles.selectedRatingText}>
                    {EvaluationService.getRatingLabel(currentRating)}
                  </Text>
                )}
              </View>
            )}
          </ScrollView>
        )}

        <View style={styles.navigationContainer}>
          <TouchableOpacity
            style={[
              styles.navButton,
              styles.prevButton,
              currentQuestionIndex === 0 && styles.navButtonDisabled
            ]}
            onPress={handlePrev}
            disabled={currentQuestionIndex === 0}
            activeOpacity={0.7}
          >
            <Text style={[
              styles.navButtonText,
              currentQuestionIndex === 0 && styles.navButtonTextDisabled
            ]}>
              Prev
            </Text>
          </TouchableOpacity>

          {isLastQuestion ? (
            <TouchableOpacity
              style={[
                styles.navButton,
                styles.doneButton,
                (!isAllAnswered || isCompleting) && styles.navButtonDisabled
              ]}
              onPress={handleDone}
              disabled={!isAllAnswered || isCompleting}
              activeOpacity={0.7}
            >
              {isCompleting ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={styles.doneButtonText}>Done</Text>
              )}
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={[
                styles.navButton,
                styles.nextButton,
                !currentRating && styles.navButtonDisabled
              ]}
              onPress={handleNext}
              disabled={!currentRating}
              activeOpacity={0.7}
            >
              <Text style={[
                styles.navButtonText,
                !currentRating && styles.navButtonTextDisabled
              ]}>
                Next
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Modal>
  );
};

const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.background,
    paddingTop: 60, // Account for status bar
  },
  header: {
    alignItems: 'center',
    paddingVertical: 24,
    paddingHorizontal: 16,
    backgroundColor: theme.background,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.text,
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: theme.textSecondary,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: theme.text,
    marginTop: 16,
    fontSize: 16,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 16,
  },
  questionContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  questionText: {
    fontSize: 24,
    fontWeight: '600',
    color: theme.text,
    textAlign: 'center',
    marginBottom: 48,
    lineHeight: 32,
    paddingHorizontal: 16,
  },
  confidenceTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.text,
    marginBottom: 32,
    textAlign: 'center',
  },
  emojisContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    width: '100%',
    marginBottom: 32,
    paddingHorizontal: 8,
  },
  emojiButton: {
    padding: 8,
    borderRadius: 40,
    backgroundColor: 'transparent',
  },
  selectedRatingText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.primary,
    textAlign: 'center',
    marginTop: 16,
  },
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 24,
    paddingBottom: 40,
    backgroundColor: theme.background,
  },
  navButton: {
    flex: 1,
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 8,
  },
  prevButton: {
    backgroundColor: theme.card,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
  },
  nextButton: {
    backgroundColor: theme.primary,
  },
  doneButton: {
    backgroundColor: theme.primary,
  },
  navButtonDisabled: {
    backgroundColor: theme.textSecondary,
    opacity: 0.5,
  },
  navButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.text,
  },
  navButtonTextDisabled: {
    color: theme.textSecondary,
  },
  doneButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});

export default MatchEvaluationModal;