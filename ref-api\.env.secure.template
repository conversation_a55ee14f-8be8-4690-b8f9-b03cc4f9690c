# Secure Environment Configuration Template
# Copy this to .env and replace all placeholder values

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Use the app_user created by setup-database-security.sql
DATABASE_URL=postgres://app_user:<EMAIL>:5432/referee_app?sslmode=require
DATABASE_URL_DOCKER=*************************************************************/referee_app?sslmode=require

# PostgreSQL superuser password (for initial setup only)
POSTGRES_SUPERUSER_PASSWORD=CHANGE_THIS_SUPERUSER_PASSWORD

# Application database user password
APP_USER_PASSWORD=CHANGE_THIS_SECURE_PASSWORD

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://:CHANGE_THIS_REDIS_PASSWORD@redis:6379
REDIS_PASSWORD=CHANGE_THIS_REDIS_PASSWORD

# =============================================================================
# MINIO/S3 CONFIGURATION
# =============================================================================
S3_ENDPOINT=https://minio:9000
S3_ACCESS_KEY=CHANGE_THIS_MINIO_ACCESS_KEY
S3_SECRET_KEY=CHANGE_THIS_MINIO_SECRET_KEY

# MinIO root credentials
MINIO_ROOT_USER=CHANGE_THIS_MINIO_ROOT_USER
MINIO_ROOT_PASSWORD=CHANGE_THIS_MINIO_ROOT_PASSWORD

# =============================================================================
# APPLICATION SECURITY
# =============================================================================
NODE_ENV=production
JWT_SECRET=CHANGE_THIS_JWT_SECRET_TO_SOMETHING_VERY_SECURE_AND_LONG
SESSION_SECRET=CHANGE_THIS_SESSION_SECRET_TO_SOMETHING_VERY_SECURE_AND_LONG
BCRYPT_ROUNDS=12

# =============================================================================
# FIREBASE CONFIGURATION
# =============================================================================
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n"

# =============================================================================
# SECURITY NOTES
# =============================================================================
# 1. Generate strong passwords using: openssl rand -base64 32
# 2. Keep JWT_SECRET and SESSION_SECRET different and very long (64+ chars)
# 3. Never commit this file to version control
# 4. Use a secrets management system in production
# 5. Rotate secrets regularly
# 6. Enable SSL certificates for all services

# =============================================================================
# PASSWORD GENERATION COMMANDS
# =============================================================================
# Generate secure passwords:
# openssl rand -base64 32
# 
# Generate JWT secret:
# node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
#
# Generate session secret:
# node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"