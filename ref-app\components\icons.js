// components/icons.js - This file remains correct.
import React from 'react';
import { View, Text } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import Svg, { Path, Circle, G, Rect, Defs, ClipPath } from 'react-native-svg';

export const AppColors = {
    primaryGreen: '#066b36',
    textBlack: '#000000',
    textGray: '#5F5F5F',
    screenBackground: '#FFFFFF',
    cardBackground: '#FFFFFF',
    inputFieldBorder: '#E0E0E0',
    captainSelectBorderColor: '#068B47',
};

// --- SVG Icons ---
export const CloseCircleIcon = ({ color = "#707070", size = 20 }) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
        <Circle cx="12" cy="12" r="9" stroke={color} strokeWidth="1.5" />
        <Path d="M14.5 9.5L9.5 14.5M9.5 9.5L14.5 14.5" stroke={color} strokeWidth="1.5" strokeLinecap="round" />
    </Svg>
);

export const UserPlusIcon = ({ color = AppColors.primaryGreen, size = 20 }) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
        <Path d="M16 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2" stroke={color} strokeWidth="2" />
        <Circle cx="8.5" cy="7" r="4" stroke={color} strokeWidth="2" />
        <Path d="M20 8v6m-3-3h6" stroke={color} strokeWidth="2" />
    </Svg>
);

export const CaptainIcon = ({ color = AppColors.textBlack, size = 24 }) => (
    <Svg width={size} height={size} viewBox="0 0 107 123" fill="none" xmlns="http://www.w3.org/2000/svg">
        <Path d="M37.7386 28.4182C34.4176 28.6642 31.4902 28.984 29.9896 29.2546C29.7928 29.3038 29.0794 29.4022 28.3906 29.5006C26.3734 29.8204 22.1422 30.8782 19.8298 31.6408C16.5826 32.6986 14.1226 34.0024 12.7696 35.38C10.9 37.2742 10.8016 37.7416 10.8754 44.0392L10.9246 49.4512L12.1054 50.2384C12.7696 50.6812 13.7782 51.1732 14.3194 51.3454C14.8852 51.493 15.4264 51.6898 15.5248 51.7636C15.6724 51.9112 15.943 54.7402 15.7954 54.7402C15.4018 54.7402 13.3354 53.7316 12.253 53.0182L10.8754 52.0834L10.8262 54.937C10.777 57.766 10.777 57.766 11.4658 58.381C12.13 58.9468 13.9996 59.8816 15.1804 60.2506C15.6724 60.4228 15.7216 60.595 15.7216 61.8988C15.7216 62.686 15.6724 63.3502 15.5986 63.3502C15.1558 63.3502 11.9578 61.5544 11.3428 60.964C11.1706 60.7918 10.9492 60.6442 10.9 60.6442C10.8262 60.6442 10.8016 61.8742 10.8262 63.3748L10.8508 66.13L11.6626 66.8926C12.0808 67.3108 13.1878 68.0242 14.0734 68.467C15.6724 69.2542 15.7216 69.3034 15.7216 70.2382C15.7216 70.7548 15.7954 71.3698 15.8938 71.5912C16.0168 71.9356 15.943 71.9848 15.451 71.8372C14.836 71.6404 12.3022 70.2628 11.3674 69.5986L10.8262 69.205L10.8754 71.788C10.9246 73.9774 11.023 74.4694 11.4166 74.9368C12.1054 75.6994 13.5076 76.5358 14.6884 76.8802L15.6724 77.2L15.7708 78.7006C15.82 79.5124 15.8446 80.2258 15.82 80.275C15.7954 80.3242 15.574 80.2504 15.3034 80.1274C15.0574 80.0044 14.1226 79.5616 13.2124 79.1188C12.2776 78.676 11.5396 78.2086 11.5396 78.1102C11.5396 78.0118 11.3674 77.8642 11.1706 77.7904C10.8262 77.6428 10.8016 77.7658 11.023 80.5948C11.0722 81.3328 13.0402 83.2762 14.6146 84.1372C15.4756 84.6292 17.0992 85.3426 18.2308 85.7608C20.2726 86.5234 25.4386 87.8518 27.0376 88.0486C27.505 88.0978 28.1692 88.2208 28.5136 88.2946C29.2024 88.4668 29.8912 88.5406 35.2786 88.9834C42.7324 89.623 56.6068 89.254 62.2156 88.2946C62.683 88.2208 63.4456 88.0978 63.913 88.0486C67.1356 87.6304 73.3348 85.7608 75.7702 84.4816C77.5168 83.5468 79.0912 82.243 79.5586 81.2836C79.8784 80.6686 80.0998 77.8642 79.8292 77.8642C79.7554 77.8642 79.2142 78.184 78.6238 78.6022C78.0334 78.9958 77.0248 79.537 76.3852 79.8076L75.2536 80.2996V79.3894C75.2536 77.7412 75.475 77.3968 76.9756 76.6342C77.7382 76.2406 78.7222 75.6256 79.165 75.2812L79.9276 74.617V71.911V69.205L79.3864 69.5986C78.8452 69.9676 75.598 71.7142 75.4258 71.7142C75.3766 71.7142 75.3274 71.173 75.2782 70.5088L75.229 69.328L76.6066 68.6392C77.3446 68.2456 78.4024 67.5568 78.9436 67.0894L79.9276 66.253V63.4732V60.6688L77.9104 61.9972C76.828 62.7352 75.7702 63.3502 75.5734 63.3502C75.352 63.3502 75.2536 62.9566 75.2536 61.9972C75.2536 60.7426 75.3028 60.6196 75.9424 60.349C76.3114 60.1768 77.3446 59.5618 78.2794 58.9714L79.9276 57.889V54.9124V51.9604L79.3864 52.4524C78.8452 52.969 75.7456 54.7402 75.4258 54.7402C75.3274 54.7402 75.2536 54.1006 75.2536 53.3134V51.8866L76.4344 51.4684C77.074 51.2224 78.1072 50.6566 78.7714 50.1892L79.9276 49.3282V43.8424V38.3566L79.165 37.1266C77.8858 35.134 76.1884 33.8548 73.1626 32.6002C69.8908 31.2472 64.3804 29.8204 60.4936 29.2792C59.8786 29.2054 59.116 29.0824 58.7716 29.0332C54.7126 28.2706 43.9132 27.9754 37.7386 28.4182ZM54.4666 31.8868C56.8036 32.1082 60.0016 32.5018 63.1996 32.9938C64.9462 33.2644 69.5464 34.5682 71.3914 35.2816C77.074 37.5448 78.0088 39.9064 74.245 42.4648C71.3668 44.4082 66.004 45.835 57.9598 46.7944C53.458 47.3356 37.9354 47.4094 33.8026 46.9174C31.5394 46.6468 27.3328 45.9826 26.4226 45.7858C26.2996 45.7366 25.7338 45.6382 25.1926 45.5398C24.1348 45.343 21.0844 44.482 20.2726 44.1622C20.002 44.0638 19.141 43.7194 18.3538 43.4242C17.5666 43.1044 16.3858 42.3664 15.7216 41.776C14.59 40.7674 14.4916 40.5706 14.4916 39.5866C14.4916 36.5608 19.2394 34.4452 30.3586 32.5018C35.8936 31.5178 47.308 31.2472 54.4666 31.8868ZM51.5448 54.937C25.9362 76.6096 53.5567 55.8369 53.3845 58.9611L53.9746 59.7832L53.8762 54.937L48.4396 59.9308V59.1928C48.4396 57.3232 47.3818 56.5852 44.9956 56.7574C42.4372 56.9296 28.5136 47.6554 28.5136 55.8964L42.5356 71.9356L43.1752 72.4276C43.6918 72.8458 62.5108 78.2332 63.913 78.2332C66.3238 78.2332 48.292 72.6982 48.4396 70.558L48.5626 68.8852H51.244H53.95L53.8762 71.4682C53.8024 74.3956 29.8666 75.5764 28.3906 76.8802C26.7178 78.3316 49.7188 78.2332 45.2416 78.2332C41.4778 78.2332 41.0842 78.184 40.0264 77.6674C38.5012 76.9048 62.3656 72.8704 62.0212 71.4682C61.6768 70.0414 36.2872 57.2002 36.6562 55.8964C37.099 54.2728 61.9942 74.371 63.913 73.8052C64.3804 73.6822 47.3512 56.7574 49.4422 56.7574C66.9082 84.9982 50.3886 54.3958 51.5448 54.937Z" fill={color} />
        <Path d="M43.2102 32.8597C35.7031 33.2206 29.3013 34.5858 25.9107 36.5655C24.8053 37.2091 24.0463 37.9601 23.8978 38.5647C23.6338 39.5789 24.5 40.5736 26.4882 41.5683C29.5158 43.0701 34.0366 44.1234 39.8279 44.6597C42.014 44.8645 47.764 44.8938 49.81 44.7182C57.1274 44.0648 62.1349 42.9044 65.113 41.1782C68.1159 39.4424 67.5714 37.5895 63.5456 35.8244C60.3283 34.4103 55.7497 33.4253 50.3874 32.9963C49.051 32.889 44.4229 32.8012 43.2102 32.8597ZM40.4879 38.4086V42.7483H39.8279H39.1679V38.4086V34.069H39.8279H40.4879V38.4086Z" fill={color} />
    </Svg>
);

export const GoalkeeperIcon = ({ color = AppColors.textBlack, size = 18 }) => (
    <Svg width={size} height={size} viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <G>
            <Path d="M13.7903 0H1.20968C0.542661 0 0 0.542661 0 1.20968V6.04839H0.483871V4.35484H2.12226C2.04774 4.43782 1.99113 4.53726 1.96113 4.64782C1.87815 4.61492 1.7879 4.59677 1.69355 4.59677C1.29339 4.59677 0.967742 4.92242 0.967742 5.32258V10.4497L1.45161 11.6594V13.7903H1.93548V15H2.41935V13.7903H4.1129V15H4.59677V13.7903H5.08065V11.4711L5.5454 11.0064L5.78734 9.79669L7.22879 8.35524C7.40371 8.18056 7.5 7.94782 7.5 7.70032C7.5 7.18984 7.08435 6.77419 6.57387 6.77419C6.32661 6.77419 6.09387 6.87048 5.91895 7.0454L5.32258 7.64177V4.83871C5.32258 4.65218 5.24976 4.48355 5.13387 4.35484H7.25806V5.32258H7.74194V4.35484H9.67742V4.83871V5.32258V7.64177L9.08105 7.0454C8.90637 6.87048 8.67363 6.77419 8.42613 6.77419C7.91564 6.77419 7.5 7.18984 7.5 7.70032C7.5 7.94758 7.59629 8.18032 7.77121 8.35524L9.21266 9.79669L9.4546 11.0064L9.91935 11.4711V13.7903H10.4032V15H10.8871V13.7903H12.5806V15H13.0645V13.7903H13.5484V11.6594L14.0323 10.4497V5.32258C14.0323 4.92242 13.7066 4.59677 13.3065 4.59677C13.2121 4.59677 13.1219 4.61492 13.0391 4.64782C13.009 4.53741 12.9531 4.4377 12.8787 4.35484H14.5161V6.04839H15V1.20968C15 0.542661 14.4573 0 13.7903 0ZM3.52887 0.483871L4.49661 1.45161H2.76145L1.79371 0.483871H3.52887ZM2.90323 2.66129V1.93548H4.83871V2.66129H2.90323ZM4.83871 3.14516V3.87097H4.16806C4.035 3.7229 3.8429 3.62903 3.62903 3.62903C3.41516 3.62903 3.22282 3.7229 3.09 3.87097H2.90323V3.14516H4.83871ZM0.483871 1.20968C0.483871 0.840484 0.761855 0.538306 1.11871 0.493065L2.41935 1.79371V2.66129H0.483871V1.20968ZM0.483871 3.87097V3.14516H2.41935V3.87097H0.483871ZM5.42274 8.22581L6.26105 7.3875C6.34331 7.30524 6.45726 7.25806 6.57387 7.25806C6.81774 7.25806 7.01613 7.45645 7.01613 7.70032C7.01613 7.81669 6.96895 7.93089 6.88669 8.01314L5.34169 9.55814L5.09976 10.7678L4.59677 11.2708V12.3387H2.41935V12.8226H4.59677V13.3065H1.93548V11.5665L1.45161 10.3568V5.32258C1.45161 5.18903 1.56 5.08065 1.69355 5.08065C1.8271 5.08065 1.93548 5.18903 1.93548 5.32258V7.5H2.41935V4.83871C2.41935 4.70516 2.52774 4.59677 2.66129 4.59677C2.79484 4.59677 2.90323 4.70516 2.90323 4.83871V7.5H3.3871V4.35484C3.3871 4.22129 3.49548 4.1129 3.62903 4.1129C3.76258 4.1129 3.87097 4.22129 3.87097 4.35484V7.5H4.35484V4.83871C4.35484 4.70516 4.46323 4.59677 4.59677 4.59677C4.73032 4.59677 4.83871 4.70516 4.83871 4.83871V8.22581H5.42274ZM7.25806 3.87097H5.32258V3.14516H7.25806V3.87097ZM7.25806 2.66129H5.32258V1.93548H7.25806V2.66129ZM7.25806 1.45161H5.18081L4.21306 0.483871H7.25806V1.45161ZM13.8813 0.493065C14.2381 0.538306 14.5161 0.840484 14.5161 1.20968V2.66129H12.5806V1.79371L13.8813 0.493065ZM13.2063 0.483871L12.2385 1.45161H10.5034L11.4711 0.483871H13.2063ZM11.911 3.87097C11.778 3.72272 11.5853 3.62903 11.371 3.62903C11.1569 3.62903 10.9648 3.7229 10.8319 3.87097H10.1613V3.14516H12.0968V3.87097H11.911ZM10.1613 2.66129V1.93548H12.0968V2.66129H10.1613ZM7.74194 0.483871H10.7869L9.81919 1.45161H7.74194V0.483871ZM7.74194 1.93548H9.67742V2.66129H7.74194V1.93548ZM7.74194 3.87097V3.14516H9.67742V3.87097H7.74194ZM13.0645 7.5V5.32258C13.0645 5.18903 13.1729 5.08065 13.3065 5.08065C13.44 5.08065 13.5484 5.18903 13.5484 5.32258V10.3568L13.0645 11.5665V13.3065H10.4032V12.8226H12.5806V12.3387H10.4032V11.2708L9.90024 10.7678L9.65831 9.55814L8.11331 8.01314C8.03105 7.93089 7.98387 7.81694 7.98387 7.70032C7.98387 7.45645 8.18226 7.25806 8.42613 7.25806C8.5425 7.25806 8.65669 7.30524 8.73895 7.3875L9.57726 8.22581H10.1613V5.32258V4.83871C10.1613 4.70516 10.2697 4.59677 10.4032 4.59677C10.5368 4.59677 10.6452 4.70516 10.6452 4.83871V7.5H11.129V4.35484C11.129 4.22129 11.2374 4.1129 11.371 4.1129C11.5045 4.1129 11.6129 4.22129 11.6129 4.35484V7.5H12.0968V4.83871C12.0968 4.70516 12.2052 4.59677 12.3387 4.59677C12.4723 4.59677 12.5806 4.70516 12.5806 4.83871V7.5H13.0645ZM12.5806 3.87097V3.14516H14.5161V3.87097H12.5806Z" fill={color} />
        </G>
    </Svg>
);

export const SwapIcon = ({ color = AppColors.textBlack, size = 20 }) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <Path d="M16 3l4 4-4 4M20 7H4M8 21l-4-4 4-4M4 17h16" />
    </Svg>
);

export const UpDownArrowIcon = ({ color = AppColors.textGray, size = 16 }) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <Path d="M12 19V5M5 12l7-7 7 7" />
    </Svg>
);

export const MainCaptainAssignIcon = ({ color = AppColors.textBlack, size = 24 }) => (
    <View style={{
        width: size,
        height: size,
        borderRadius: size / 2,
        borderWidth: 1.5,
        borderColor: color,
        justifyContent: 'center',
        alignItems: 'center',
    }}>
        <Text style={{
            color: color,
            fontSize: size * 0.7,
            fontWeight: 'bold',
            includeFontPadding: false,
        }}>C</Text>
    </View>
);

export const SwapArrowsIcon = ({ size = 24 }) => (
    <Svg width={size} height={size} viewBox="0 0 241 469" fill="none">
        <G clipPath="url(#clip0_0_1)">
            <Path d="M68.1544 180.931C85.3691 164.357 105.218 155.511 125.565 155.348C134.874 155.266 144.045 156.985 152.86 160.427L137.109 193.672L213.76 191.145L187.514 87.2839L172.324 119.346C157.63 111.714 141.986 107.839 126.06 107.968C99.2761 108.185 73.173 119.816 50.5405 141.594C35.4631 156.091 22.6593 174.354 12.4615 195.83L39.1838 222.171C46.9322 205.866 56.6765 191.967 68.1544 180.931Z" fill="#FF0000" />
        </G>
        <Path d="M172.937 297.34C154.661 311.357 134.299 317.305 114.043 314.542C104.774 313.286 95.768 310.257 87.235 305.564L105.207 274.749L28.7637 266.245L47.6931 373.361L65.0263 343.642C79.1188 353.349 94.4161 359.454 110.272 361.615C136.937 365.249 163.715 357.429 187.741 339.013C203.747 326.757 217.75 310.425 229.383 290.522L204.616 260.47C195.779 275.581 185.121 288.01 172.937 297.34Z" fill="#066B36" />
        <Defs>
            <ClipPath id="clip0_0_1">
                <Rect width="190.506" height="221.469" fill="white" transform="matrix(0.712619 0.701552 -0.428157 0.903704 105.242 0)" />
            </ClipPath>
        </Defs>
    </Svg>
);

export const SoccerBallIcon = ({ color = AppColors.textBlack, size = 24, fillColor = null }) => (
    <Svg width={size} height={size} viewBox="0 0 226 203" fill="none">
        <G clipPath="url(#clip0_494_232)">
            <Path fillRule="evenodd" clipRule="evenodd" d="M113 0C144.211 0 172.461 11.3659 192.895 29.7363C213.346 48.1068 226 73.4818 226 101.5C226 129.535 213.346 154.91 192.895 173.264C172.443 191.634 144.193 203 113 203C81.8073 203 53.5389 191.634 33.1055 173.264C12.6536 154.91 0 129.535 0 101.5C0 73.4652 12.6536 48.0902 33.1055 29.7363C53.5389 11.3659 81.7889 0 113 0ZM141.342 193.418L141.121 193.286L153.793 155.141L127.658 129.105L96.8519 128.857L72.4274 156.314L84.6764 193.187L84.4005 193.352C93.5045 195.632 103.087 196.854 113 196.854C122.821 196.854 132.33 195.665 141.342 193.418ZM22.4749 151.341L67.2409 151.54L90.6354 125.223L80.9613 98.5924L41.5658 83.2287L6.95215 105.861C7.77978 122.481 13.3525 137.993 22.4749 151.341ZM30.8433 41.1022L44.4533 77.6779L81.2004 92.0174L109.597 72.2428V34.5933L74.5057 12.6049C60.675 17.4453 48.242 24.8133 37.9242 34.0811C35.4412 36.3114 33.0687 38.6572 30.8433 41.1022ZM150.262 12.1754L116.421 34.3124V72.1932L143.089 90.7123L181.363 75.9763L194.568 40.4744C192.508 38.2442 190.338 36.1131 188.057 34.0646C177.445 24.5325 164.589 16.9993 150.262 12.1754ZM219.029 106.324L218.993 106.34L182.246 82.3035L145.811 96.3292L134.629 126.115L159.881 151.275C159.991 151.374 160.047 151.49 160.083 151.622L203.488 151.441C212.537 138.191 218.11 122.811 219.029 106.324Z" fill={fillColor || color} />
        </G>
        <Defs>
            <ClipPath id="clip0_494_232">
                <Rect width="226" height="203" fill="white" />
            </ClipPath>
        </Defs>
    </Svg>
);

export const SubstitutionIcon = ({ size = 32 }) => (
    <View style={{ width: size, height: size, justifyContent: 'center', alignItems: 'center' }}>
        <Svg width={size} height={size} viewBox="0 0 32 32">
            {/* Green circle with up arrow (Sub In) */}
            <Circle cx="16" cy="10" r="8" fill="#28A745" />
            <Path
                d="M16 6V14 M13 9L16 6L19 9"
                stroke="black"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                fill="none"
            />

            {/* Red circle with down arrow (Sub Out) */}
            <Circle cx="16" cy="22" r="8" fill="#DC3545" />
            <Path
                d="M16 18V26 M13 23L16 26L19 23"
                stroke="black"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                fill="none"
            />
        </Svg>
    </View>
);

