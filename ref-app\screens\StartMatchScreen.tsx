import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ActivityIndicator,
  Modal,
  ScrollView,
  Alert,
  DeviceEventEmitter,
} from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import { useNavigation, CommonActions } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Header from '../components/Header';
import { useTheme } from '../contexts/ThemeContext';
import MatchService from '../api/matchService';
import { SquadService } from '../api/squadService';
import { MatchLogEvent } from '../api/config'; // Import MatchLogEvent from config
import BroadcastService from '../api/broadcastService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Svg, { <PERSON>, Defs, ClipPath, G, Rect, Circle } from 'react-native-svg';
import { SubstitutionIcon, SoccerBallIcon, GoalkeeperIcon } from '../components/icons';
import MatchEvaluationModal from '../components/MatchEvaluationModal';
import { EvaluationAnswer } from '../api/evaluationService';

// Define the navigation stack type
type RootStackParamList = {
  MatchResults: { matchId: any; matchLog: MatchLogEvent[]; fromAbandonment?: boolean };
  DashboardHome: undefined;
  // Add other screens as needed
};

type StartMatchScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

// Helper to format seconds into MM:SS
const formatTime = (totalSeconds: number) => {
  const minutes = Math.floor(totalSeconds / 60)
    .toString()
    .padStart(2, '0');
  const seconds = (totalSeconds % 60).toString().padStart(2, '0');
  return `${minutes}:${seconds}`;
};

// Striped T-shirt component (unchanged)
const StripedTshirt: React.FC<{
  baseColor: string;
  stripeColor: string;
  size?: number;
  isSelected?: boolean;
  onPress?: () => void;
}> = ({ baseColor, stripeColor, size = 48, isSelected = false, onPress }) => (
  <TouchableOpacity
    onPress={onPress}
    style={{ opacity: isSelected || !onPress ? 1 : 0.3 }}
    disabled={!onPress}
  >
    <Svg width={size} height={size} viewBox="-1 0 19 19">
      <Defs>
        <ClipPath id="startMatchTshirtClip">
          <Path d="m15.867 7.593-1.534.967a.544.544 0 0 1-.698-.118l-.762-.957v7.256a.476.476 0 0 1-.475.475h-7.79a.476.476 0 0 1-.475-.475V7.477l-.769.965a.544.544 0 0 1-.697.118l-1.535-.967a.387.387 0 0 1-.083-.607l2.245-2.492a2.814 2.814 0 0 1 2.092-.932h.935a2.374 2.374 0 0 0 4.364 0h.934a2.816 2.816 0 0 1 2.093.933l2.24 2.49a.388.388 0 0 1-.085.608z" />
        </ClipPath>
      </Defs>
      <Path
        d="m15.867 7.593-1.534.967a.544.544 0 0 1-.698-.118l-.762-.957v7.256a.476.476 0 0 1-.475.475h-7.79a.476.476 0 0 1-.475-.475V7.477l-.769.965a.544.544 0 0 1-.697.118l-1.535-.967a.387.387 0 0 1-.083-.607l2.245-2.492a2.814 2.814 0 0 1 2.092-.932h.935a2.374 2.374 0 0 0 4.364 0h.934a2.816 2.816 0 0 1 2.093.933l2.24 2.49a.388.388 0 0 1-.085.608z"
        fill={isSelected || !onPress ? baseColor : '#888888'}
      />
      <G clipPath="url(#startMatchTshirtClip)">
        {[3.5, 6, 8.5, 11, 13.5].map(x => (
          <Rect
            key={x}
            x={x}
            y="3"
            width="1.2"
            height="16"
            fill={isSelected || !onPress ? stripeColor : '#666666'}
          />
        ))}
      </G>
    </Svg>
  </TouchableOpacity>
);

const StartMatchScreen = ({ route }: { route: any }) => {
  const { theme, isDarkMode } = useTheme();
  const navigation = useNavigation<StartMatchScreenNavigationProp>();
  const styles = getStyles(theme, isDarkMode);


  const matchId = route?.params?.matchId;
  const misconductSet = route?.params?.misconductSet;
  const autoStart = route?.params?.autoStart;
  const autoKickoffTeam = route?.params?.kickoffTeam;

  const [matchData, setMatchData] = useState<any>(null);
  // State for misconduct set
  const [loadedMisconductSet, setLoadedMisconductSet] = useState<any>(null);

  // Get misconduct set - either from route params or fetch from storage
  const activeMisconductSet = misconductSet || loadedMisconductSet;

  // Debug logging for misconduct set and match data
  useEffect(() => {
    if (activeMisconductSet) {
      console.log('🎯 Active Misconduct Set:', {
        name: activeMisconductSet.name,
        source: misconductSet ? 'route params' : 'storage',
        playersYellowCodes: activeMisconductSet.playersYellowCodes?.length || 0,
        playersRedCodes: activeMisconductSet.playersRedCodes?.length || 0,
        officialsYellowCodes: activeMisconductSet.officialsYellowCodes?.length || 0,
        officialsRedCodes: activeMisconductSet.officialsRedCodes?.length || 0,
      });
    } else {
      console.log('⚠️ No misconduct set available');
    }
  }, [activeMisconductSet]);

  // Debug logging for match data (only when rollOnRollOff changes)
  useEffect(() => {
    if (matchData) {
      console.log('🏈 Match Data Debug:', {
        rollOnRollOff: matchData.rollOnRollOff,
        rollOnRollOffType: typeof matchData.rollOnRollOff,
        hasRollOnRollOff: 'rollOnRollOff' in matchData
      });
    }
  }, [matchData?.rollOnRollOff]); // Only trigger when rollOnRollOff changes
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTeam, setSelectedTeam] = useState<'home' | 'away' | null>(null);

  // Match completion state
  const [showEndMatchButton, setShowEndMatchButton] = useState(false);
  
  // Evaluation modal state
  const [showEvaluationModal, setShowEvaluationModal] = useState(false);
  const [pendingMatchLog, setPendingMatchLog] = useState<any[]>([]);

  // Timer state
  const [isMatchStarted, setIsMatchStarted] = useState(false);
  const [isMenuVisible, setMenuVisible] = useState(false);
  const [mainSeconds, setMainSeconds] = useState(0);
  const [stoppageSeconds] = useState(0);
  const [currentPeriod, setCurrentPeriod] = useState(1);
  const [matchPhase, setMatchPhase] = useState<'period' | 'halftime' | 'extratime' | 'et-halftime' | 'completed' | 'penalties'>('period');
  const [isHalfTime, setIsHalfTime] = useState(false);
  const [halfTimeSeconds, setHalfTimeSeconds] = useState(0);
  const [kickoffTeam, setKickoffTeam] = useState<'home' | 'away' | null>(null);
  const [firstKickoffTeam, setFirstKickoffTeam] = useState<'home' | 'away' | null>(null);
  const [nextKickoffTeam, setNextKickoffTeam] = useState<'home' | 'away' | null>(null);
  const [isExtraTime, setIsExtraTime] = useState(false);
  const [currentETPhase, setCurrentETPhase] = useState<'ET1' | 'ET-HT' | 'ET2' | null>(null);
  const [justFinishedETPhase, setJustFinishedETPhase] = useState<'ET1' | 'ET2' | null>(null);
  const [isInInjuryTime, setIsInInjuryTime] = useState(false);
  const [injuryTimeStarted, setInjuryTimeStarted] = useState(0); // When injury time started

  // Swipe gesture state - simplified for smooth transitions
  const [currentView, setCurrentView] = useState<'center' | 'home' | 'away'>('center');

  // Goal tracking system
  const [matchGoals, setMatchGoals] = useState<Array<{
    id: string;
    team: 'home' | 'away';
    playerNumber: number;
    playerName: string;
    minute: number;
  }>>([]);

  // Goal system state
  const [goalFlow, setGoalFlow] = useState<{
    isActive: boolean;
    step: 'player' | 'display';
    team: 'home' | 'away' | null;
    selectedPlayer: number | null;
  }>({
    isActive: false,
    step: 'player',
    team: null,
    selectedPlayer: null,
  });

  // Card tracking system
  const [matchCards, setMatchCards] = useState<Array<{
    id: string;
    team: 'home' | 'away';
    playerNumber: number;
    playerName: string;
    cardType: 'yellow' | 'red';
    code: string;
    subCode?: string;
    minute: number;
    isSecondYellow?: boolean;
    originalYellowId?: string;
  }>>([]);

  // Card system state
  const [cardFlow, setCardFlow] = useState<{
    isActive: boolean;
    step: 'type' | 'person' | 'player' | 'code' | 'subcode' | 'confirm';
    cardType: 'yellow' | 'red' | null;
    team: 'home' | 'away' | null;
    personType: 'player' | 'substitute' | 'official' | null;
    selectedPlayer: number | null;
    selectedOfficial: { index: number; role: string; name: string } | null;
    selectedCode: string | null;
    selectedSubCode: string | null;
    selectedMainCode: any | null;
    isSecondYellow: boolean;
    originalYellowCard: any | null;
  }>({
    isActive: false,
    step: 'type',
    cardType: null,
    team: null,
    personType: null,
    selectedPlayer: null,
    selectedOfficial: null,
    selectedCode: null,
    selectedSubCode: null,
    selectedMainCode: null,
    isSecondYellow: false,
    originalYellowCard: null,
  });

  // Substitution tracking system
  const [matchSubstitutions, setMatchSubstitutions] = useState<Array<{
    id: string;
    team: 'home' | 'away';
    playerOutNumber: number;
    playerOutName: string;
    playerInNumber: number;
    playerInName: string;
    minute: number;
    opportunityId?: string;
  }>>([]);

  // Substitution opportunities tracking
  const [substitutionOpportunities, setSubstitutionOpportunities] = useState<{
    home: Array<{
      id: string;
      isActive: boolean;
      isClosed: boolean;
      substitutionsInOpportunity: number;
    }>;
    away: Array<{
      id: string;
      isActive: boolean;
      isClosed: boolean;
      substitutionsInOpportunity: number;
    }>;
  }>({
    home: [],
    away: []
  });

  // Red card warning system state
  const [redCardWarningDismissed, setRedCardWarningDismissed] = useState<{
    home: boolean;
    away: boolean;
  }>({
    home: false,
    away: false
  });

  // State to control when warning modals should be visible
  const [showRedCardWarning, setShowRedCardWarning] = useState<{
    home: boolean;
    away: boolean;
  }>({
    home: false,
    away: false
  });

  // Substitution flow state
  const [substitutionFlow, setSubstitutionFlow] = useState<{
    isActive: boolean;
    step: 'player-out' | 'player-in' | 'display' | 'opportunity-check';
    team: 'home' | 'away' | null;
    selectedPlayerOut: number | null;
    selectedPlayerIn: number | null;
    currentOpportunityId: string | null;
  }>({
    isActive: false,
    step: 'player-out',
    team: null,
    selectedPlayerOut: null,
    selectedPlayerIn: null,
    currentOpportunityId: null,
  });

  // Timer logic
  // Injury time tracking state
  const [injuryTimeSeconds, setInjuryTimeSeconds] = useState<{
    period1: number;
    period2: number;
    ET1: number;
    ET2: number;
  }>({
    period1: 0,
    period2: 0,
    ET1: 0,
    ET2: 0
  });

  // Sin bin (temporary dismissal) tracking state
  const [sinBinPlayers, setSinBinPlayers] = useState<{
    id: string;
    team: 'home' | 'away';
    playerNumber: number;
    playerName: string;
    startTime: number;
    duration: number;
    remainingTime: number;
  }[]>([]);

  // Match log state
  const [matchLog, setMatchLog] = useState<MatchLogEvent[]>([]);

  // Penalty state
  const [isPenalties, setIsPenalties] = useState(false);
  const [penaltyFlow, setPenaltyFlow] = useState<{
    isActive: boolean;
    step: 'outcome' | 'player' | 'display';
    team: 'home' | 'away' | null;
    round: number;
    outcome: 'goal' | 'miss' | 'save' | null;
    selectedPlayer: number | null;
  }>({
    isActive: false,
    step: 'outcome',
    team: null,
    round: 1,
    outcome: null,
    selectedPlayer: null,
  });
  
  // Penalty results tracking - 5 rounds each team
  const [penaltyResults, setPenaltyResults] = useState<{
    home: Array<{ player: number; playerName: string; outcome: 'goal' | 'miss' | 'save' }>;
    away: Array<{ player: number; playerName: string; outcome: 'goal' | 'miss' | 'save' }>;
  }>({
    home: [],
    away: []
  });
  
  // Current penalty team (alternates)
  const [currentPenaltyTeam, setCurrentPenaltyTeam] = useState<'home' | 'away' | null>(null);
  
  // Starting penalty team (used to track proper alternating)
  const [penaltyStartingTeam, setPenaltyStartingTeam] = useState<'home' | 'away' | null>(null);
  
  // Current penalty round (1-5)
  const [penaltyRound, setPenaltyRound] = useState(1);

  // Penalty helper functions
  const shouldGotoPenalties = (): boolean => {
    // Only go to penalties if penalties are enabled AND the match is tied
    if (!matchData?.penalties) return false;
    
    const homeGoals = matchGoals.filter(goal => goal.team === 'home').length;
    const awayGoals = matchGoals.filter(goal => goal.team === 'away').length;
    
    // Only go to penalties if the score is tied
    return homeGoals === awayGoals;
  };
  
  const initializePenalties = () => {
    console.log('⚽ Starting penalty shootout');
    
    setIsPenalties(true);
    setMatchPhase('penalties'); // Set to penalties phase, not completed
    setIsMatchStarted(false); // Stop the timer
    setIsExtraTime(false);
    setIsInInjuryTime(false);
    setCurrentETPhase(null);
    setJustFinishedETPhase(null);
    
    // Reset penalty state
    setPenaltyResults({ home: [], away: [] });
    setPenaltyRound(1);
    
    // Reset yellow cards for all players (fresh start)
    resetYellowCardsForPenalties();
    
    // Clear all sin bin players (fresh start for penalties)
    setSinBinPlayers([]);
    console.log('🔄 Sin bin cleared for penalties - all players available');
    
    // Clear any temporary dismissal warnings
    setRedCardWarningDismissed({ home: false, away: false });
    setShowRedCardWarning({ home: false, away: false });
    console.log('🔄 Temporary dismissal warnings reset for penalties');
    
    // Determine which team starts (random)
    const startingTeam = Math.random() < 0.5 ? 'home' : 'away';
    setCurrentPenaltyTeam(startingTeam);
    setPenaltyStartingTeam(startingTeam);
    
    // Log penalty start
    addMatchLogEvent('penalty_start', {
      startingTeam,
      homeGoals: matchGoals.filter(goal => goal.team === 'home').length,
      awayGoals: matchGoals.filter(goal => goal.team === 'away').length
    });
  };
  
  const resetYellowCardsForPenalties = () => {
    // Remove all yellow cards from matchCards for penalties (fresh start)
    setMatchCards(prev => prev.filter(card => card.cardType === 'red'));
    console.log('🔄 Yellow cards reset for penalties');
  };
  
  const getAvailablePenaltyPlayers = (team: 'home' | 'away'): number[] => {
    // During penalties, all players on field at the end of match can take penalties
    const teamData = team === 'home' ? matchData?.homeTeam : matchData?.awayTeam;
    if (!teamData?.squad?.players) return [];
    
    // Get players who are currently STARTER (on field) - they can all take penalties
    const playersOnField = teamData.squad.players
      .filter((p: any) => p.position === 'STARTER')
      .map((p: any) => p.number);
      
    // Return all players on field - no restrictions during penalties
    return playersOnField;
  };
  
  const calculatePenaltyScore = (): { home: number, away: number } => {
    const homeGoals = penaltyResults.home.filter(r => r.outcome === 'goal').length;
    const awayGoals = penaltyResults.away.filter(r => r.outcome === 'goal').length;
    return { home: homeGoals, away: awayGoals };
  };
  
  const canTeamTakePenalty = (team: 'home' | 'away'): boolean => {
    if (!penaltyStartingTeam) return true;
    
    const homeCount = penaltyResults.home.length;
    const awayCount = penaltyResults.away.length;
    
    // Penalties must be taken in parallel/alternating fashion
    // Each team can only be 1 penalty ahead of the other team at most
    const teamCount = team === 'home' ? homeCount : awayCount;
    const otherTeamCount = team === 'home' ? awayCount : homeCount;
    
    // Team can take penalty if:
    // 1. They have equal or fewer penalties than the other team
    // 2. During first 5 rounds: they haven't reached 5 yet
    // 3. During sudden death: strict alternating
    
    if (homeCount < 5 && awayCount < 5) {
      // First 5 rounds: team can only take if they don't have more than the other team
      return teamCount <= otherTeamCount;
    } else if (homeCount >= 5 && awayCount >= 5) {
      // Sudden death: strict alternating after both teams have 5
      const totalPenalties = homeCount + awayCount;
      return totalPenalties % 2 === 0 ? (team === penaltyStartingTeam) : 
             (team !== penaltyStartingTeam);
    } else {
      // One team has 5, other has less - the team with less must catch up
      return teamCount < 5;
    }
  };
  
  const getNextPenaltyTeam = (): 'home' | 'away' => {
    if (!penaltyStartingTeam) return 'home';
    
    const homeCount = penaltyResults.home.length;
    const awayCount = penaltyResults.away.length;
    
    // Always suggest the team with fewer penalties (parallel taking)
    if (homeCount < awayCount) {
      return 'home'; // Home is behind, should go next
    } else if (awayCount < homeCount) {
      return 'away'; // Away is behind, should go next
    } else {
      // Equal counts - determine based on phase
      if (homeCount >= 5 && awayCount >= 5) {
        // Sudden death - strict alternating
        const totalPenalties = homeCount + awayCount;
        return totalPenalties % 2 === 0 ? penaltyStartingTeam : 
               (penaltyStartingTeam === 'home' ? 'away' : 'home');
      } else {
        // First 5 rounds - suggest starting team when equal
        return penaltyStartingTeam;
      }
    }
  };
  
  const checkPenaltyWinner = (): 'continue' | 'home_wins' | 'away_wins' => {
    const { home, away } = calculatePenaltyScore();
    const homeShots = penaltyResults.home.length;
    const awayShots = penaltyResults.away.length;
    
    // Early winner detection during first 5 rounds
    if (homeShots <= 5 && awayShots <= 5) {
      if (homeShots >= 3 && awayShots >= 3) {
        const remainingHome = 5 - homeShots;
        const remainingAway = 5 - awayShots;
        
        // Check if winner is already determined
        if (home > away + remainingAway) return 'home_wins';
        if (away > home + remainingHome) return 'away_wins';
      }
      
      // All 5 rounds completed for both teams
      if (homeShots >= 5 && awayShots >= 5) {
        if (home > away) return 'home_wins';
        if (away > home) return 'away_wins';
        // If tied after 5, continue to sudden death
      }
    } else {
      // Sudden death (after round 5) - both teams must have equal shots
      if (homeShots === awayShots && homeShots > 5) {
        if (home > away) return 'home_wins';
        if (away > home) return 'away_wins';
      }
    }
    
    return 'continue';
  };

  // Helper function to add events to match log
  const addMatchLogEvent = (type: MatchLogEvent['type'], data?: any) => {
    const event: MatchLogEvent = {
      id: `log-${Date.now()}-${Math.random()}`,
      type,
      timestamp: mainSeconds,
      realTime: new Date(),
      data
    };

    setMatchLog(prev => [...prev, event]);
    console.log(`📋 Match Log: ${type}`, event);

    // Broadcast important actions to synced participants
    if (shouldBroadcastAction(type)) {
      broadcastActionToSyncedUsers(type, event);
    }
  };

  // Determine which actions should be broadcasted
  const shouldBroadcastAction = (type: MatchLogEvent['type']): boolean => {
    // Don't broadcast period_start for auto-started matches (synced users)
    if (type === 'period_start' && autoStart) {
      console.log('🚫 Not broadcasting period_start for auto-started match');
      return false;
    }

    const broadcastableActions = [
      'goal',
      'card',
      'substitution',
      'penalty_shot',
      'period_end',
      'halftime_start',
      'halftime_end',
      'match_completed',
      'match_abandoned'
    ];
    return broadcastableActions.includes(type);
  };

  // Broadcast action to synced participants
  const broadcastActionToSyncedUsers = async (actionType: MatchLogEvent['type'], event: MatchLogEvent) => {
    if (!matchId) return;

    try {
      console.log('📡 Broadcasting action to synced users:', actionType);

      const result = await BroadcastService.broadcastAction(
        matchId,
        actionType,
        {
          ...event.data,
          timestamp: event.timestamp,
          realTime: event.realTime,
          eventId: event.id
        },
        new Date().toISOString()
      );

      if (result.success) {
        console.log(`✅ Action ${actionType} broadcasted to ${result.data?.participantCount || 0} participants`);
      } else {
        console.error('❌ Failed to broadcast action:', result.message);
      }
    } catch (error) {
      console.error('❌ Error broadcasting action:', error);
      // Don't block the match flow if broadcasting fails
    }
  };

  // Helper function to get formatted match time for logging
  const getFormattedMatchTime = () => {
    // During penalties, use penalty round number instead of match time
    if (matchPhase === 'penalties') {
      return `R${penaltyRound}`;
    }
    const minutes = Math.floor(mainSeconds / 60);
    return `${minutes}'`;
  };

  // Helper function to get current period display name
  const getCurrentPeriodName = () => {
    if (isExtraTime) {
      return currentETPhase === 'ET1' ? 'ET Period 1' : 'ET Period 2';
    }
    return `Period ${currentPeriod}`;
  };

  // Helper function to add injury time for actions
  const addInjuryTime = (actionType: 'sanction' | 'substitution' | 'goal') => {
    if (!matchData?.injuryTimeAllowance || !isMatchStarted || isHalfTime) return;

    let timeToAdd = 0;
    switch (actionType) {
      case 'sanction':
        timeToAdd = matchData.injuryTime?.sanctions || 0;
        break;
      case 'substitution':
        timeToAdd = matchData.injuryTime?.subs || 0;
        break;
      case 'goal':
        timeToAdd = matchData.injuryTime?.goals || 0;
        break;
    }

    if (timeToAdd <= 0) return;

    // Determine current period for injury time tracking
    let currentPeriodKey: keyof typeof injuryTimeSeconds;
    if (isExtraTime) {
      currentPeriodKey = currentETPhase === 'ET1' ? 'ET1' : 'ET2';
    } else {
      currentPeriodKey = currentPeriod === 1 ? 'period1' : 'period2';
    }

    setInjuryTimeSeconds(prev => ({
      ...prev,
      [currentPeriodKey]: prev[currentPeriodKey] + timeToAdd
    }));
    
    // Log injury time addition
    addMatchLogEvent('injury_time_added', {
      actionType,
      timeToAdd,
      period: currentPeriodKey,
      totalInjuryTime: injuryTimeSeconds[currentPeriodKey] + timeToAdd,
      time: getFormattedMatchTime()
    });

    if (isInInjuryTime) {
      console.log(`⏱️ Added ${timeToAdd}s injury time during injury time for ${actionType} in ${currentPeriodKey}. Total: ${injuryTimeSeconds[currentPeriodKey] + timeToAdd}s`);
    } else {
      console.log(`⏱️ Added ${timeToAdd}s injury time for ${actionType} in ${currentPeriodKey}. Total: ${injuryTimeSeconds[currentPeriodKey] + timeToAdd}s`);
    }
  };

  // Get current period's injury time
  const getCurrentInjuryTime = () => {
    if (isExtraTime) {
      return currentETPhase === 'ET1' ? injuryTimeSeconds.ET1 : injuryTimeSeconds.ET2;
    } else {
      return currentPeriod === 1 ? injuryTimeSeconds.period1 : injuryTimeSeconds.period2;
    }
  };

  // Check if we're currently in injury time
  const isCurrentlyInInjuryTime = () => {
    return isInInjuryTime && matchData?.injuryTimeAllowance;
  };

  // Helper function to add player to sin bin
  const addPlayerToSinBin = (team: 'home' | 'away', playerNumber: number, playerName: string) => {
    if (!matchData?.temporaryDismissals || !isMatchStarted || isHalfTime) return;
    
    const duration = (matchData.temporaryDismissalsTime || 10) * 60; // Convert minutes to seconds
    const currentTime = mainSeconds;
    
    const sinBinEntry = {
      id: `sinbin-${Date.now()}-${Math.random()}`,
      team,
      playerNumber,
      playerName,
      startTime: currentTime,
      duration,
      remainingTime: duration
    };
    
    const newSinBinPlayers = [...sinBinPlayers, sinBinEntry];
    setSinBinPlayers(newSinBinPlayers);
    
    // Log sin bin start event
    addMatchLogEvent('sin_bin_start', {
      team,
      playerNumber,
      playerName,
      duration,
      time: getFormattedMatchTime()
    });
    
    console.log(`🟨 Player ${playerNumber} (${team}) sent to sin bin for ${duration}s`);
    
    // Perform check immediately with new sin bin data to avoid stale state
    const teamData = team === 'home' ? matchData.homeTeam : matchData.awayTeam;
    if (teamData?.squad?.players) {
        const playersOnField = teamData.squad.players.filter((p: any) => p.position === 'STARTER');
        
        const sinBinOnFieldPlayers = newSinBinPlayers.filter(sinBinPlayer => 
            sinBinPlayer.team === team && 
            playersOnField.some((player: any) => player.number === sinBinPlayer.playerNumber)
        );

        const activePlayersCount = playersOnField.length - sinBinOnFieldPlayers.length;
        const needsWarning = activePlayersCount === 6 && !redCardWarningDismissed[team];

        console.log(`🚨 Player Count Warning Check for ${team}:`, {
            playersOnField: playersOnField.length,
            sinBinOnFieldPlayers: sinBinOnFieldPlayers.length,
            activePlayersCount,
            threshold: 'exactly 7 active players',
            needsWarning,
            warningDismissed: redCardWarningDismissed[team],
        });

        if (needsWarning) {
            addMatchLogEvent('warning', {
                type: 'player_count_warning',
                team: team,
                message: `Team ${team} has only 6 players remaining on field`,
                time: getFormattedMatchTime()
            });
            setShowRedCardWarning(prev => ({ ...prev, [team]: true }));
        }
    }
  };

  // Helper function to remove player from sin bin
  const removePlayerFromSinBin = (sinBinId: string) => {
    setSinBinPlayers(prev => {
      const updated = prev.filter(player => player.id !== sinBinId);
      const removedPlayer = prev.find(player => player.id === sinBinId);
      if (removedPlayer) {
        console.log(`✅ Player ${removedPlayer.playerNumber} (${removedPlayer.team}) sin bin completed`);
      }
      return updated;
    });
  };

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (isMatchStarted && !isHalfTime && matchPhase !== 'completed') {
      timer = setInterval(() => {
        setMainSeconds(prevSeconds => {
          const newSeconds = prevSeconds + 1;
          let periodLength;

          // Determine period length based on current match phase
          if (isExtraTime) {
            // Extra time periods use extraTimeLengths from template
            const etLengths = matchData?.extraTimeLengths || [15, 15];
            periodLength = currentETPhase === 'ET1' ? etLengths[0] : etLengths[1];
            if (isInInjuryTime) {
              console.log(`⏱️ ${currentETPhase} Injury Time: ${Math.floor(newSeconds / 60)}:${String(newSeconds % 60).padStart(2, '0')}`);
            } else {
              console.log(`⏱️ ET Timer: ${currentETPhase}, Length: ${periodLength} min, Elapsed: ${Math.floor(newSeconds / 60)}:${String(newSeconds % 60).padStart(2, '0')}`);
            }
          } else {
            // Regular periods use periodLengths from template
            periodLength = matchData?.periodLengths?.[currentPeriod - 1] || 45;
            if (isInInjuryTime) {
              console.log(`⏱️ Period ${currentPeriod} Injury Time: ${Math.floor(newSeconds / 60)}:${String(newSeconds % 60).padStart(2, '0')}`);
            } else {
              console.log(`⏱️ Period ${currentPeriod} Timer: Length: ${periodLength} min, Elapsed: ${Math.floor(newSeconds / 60)}:${String(newSeconds % 60).padStart(2, '0')}`);
            }
          }

          const periodLengthInSeconds = periodLength * 60;
          const currentInjuryTime = getCurrentInjuryTime();

          // Check if we should start injury time
          if (!isInInjuryTime && newSeconds >= periodLengthInSeconds && currentInjuryTime > 0 && matchData?.injuryTimeAllowance) {
            console.log(`🚨 Starting injury time! Period ended at ${periodLength} min, injury time: ${currentInjuryTime}s`);
            setIsInInjuryTime(true);
            setInjuryTimeStarted(newSeconds);
            return 0; // Reset timer to 0 for injury time
          }

          // Check if injury time is complete
          if (isInInjuryTime && newSeconds >= currentInjuryTime) {
            console.log(`✅ Injury time completed! Moving to handlePeriodEnd`);
            handlePeriodEnd();
            return newSeconds;
          }

          // Check if regular period is complete (no injury time)
          if (!isInInjuryTime && newSeconds >= periodLengthInSeconds && (!matchData?.injuryTimeAllowance || currentInjuryTime === 0)) {
            console.log(`✅ Period completed! Moving to handlePeriodEnd`);
            handlePeriodEnd();
            return newSeconds;
          }

          return newSeconds;
        });
        
        // Update sin bin times
        setSinBinPlayers(prev => {
          const updated = prev.map(player => {
            const elapsed = mainSeconds - player.startTime;
            const remaining = Math.max(0, player.duration - elapsed);
            return { ...player, remainingTime: remaining };
          }).filter(player => {
            if (player.remainingTime <= 0) {
              console.log(`⏰ Player ${player.playerNumber} (${player.team}) sin bin time completed`);
              
              // Log sin bin end event
              addMatchLogEvent('sin_bin_end', {
                team: player.team,
                playerNumber: player.playerNumber,
                playerName: player.playerName,
                duration: player.duration,
                time: getFormattedMatchTime()
              });
              
              return false;
            }
            return true;
          });
          return updated;
        });
      }, 1000);
    } else if (isHalfTime) {
      timer = setInterval(() => {
        setHalfTimeSeconds(prevSeconds => {
          const newSeconds = prevSeconds + 1;
          const halfTimeLength = (matchData?.halfTime || 15) * 60;

          // Check if half-time is complete
          if (newSeconds >= halfTimeLength) {
            console.log(`✅ Half-time completed! Moving to handleHalfTimeEnd`);
            handleHalfTimeEnd();
            return newSeconds;
          }

          return newSeconds;
        });
      }, 1000);
    }

    return () => clearInterval(timer);
  }, [isMatchStarted, isHalfTime, matchPhase, currentPeriod, matchData, isExtraTime, currentETPhase, isInInjuryTime, injuryTimeSeconds, mainSeconds]);

  const handlePeriodEnd = () => {
    // Log period end event first
    addMatchLogEvent('period_end', {
      period: isExtraTime ? currentETPhase : currentPeriod,
      periodName: getCurrentPeriodName(),
      finalTime: getFormattedMatchTime(),
      score: `${matchGoals.filter(goal => goal.team === 'home').length} - ${matchGoals.filter(goal => goal.team === 'away').length}`
    });
    
    // Reset injury time state for next period
    setIsInInjuryTime(false);
    setInjuryTimeStarted(0);
    
    const totalPeriods = matchData?.numberOfPeriods || 2;
    // Fix extra time detection - check multiple possible formats
    const hasExtraTime = matchData?.extraTime === true ||
      matchData?.extraTime === 'Yes' ||
      matchData?.extraTime === 'true' ||
      (typeof matchData?.extraTime === 'string' && matchData.extraTime.toLowerCase() === 'yes');

    const initialKickoffTeam = firstKickoffTeam || 'home'; // Fallback to home if somehow undefined

    console.log('🏁 Period End Debug:', {
      currentPeriod,
      totalPeriods,
      isExtraTime,
      currentETPhase,
      hasExtraTime,
      extraTimeValue: matchData?.extraTime,
      extraTimeType: typeof matchData?.extraTime
    });

    if (isExtraTime) {
      // Handle extra time periods
      if (currentETPhase === 'ET1') {
        // End of ET Period 1 - start ET half-time
        console.log('🕐 Starting ET half-time after ET1');
        setIsHalfTime(true);
        setHalfTimeSeconds(0);
        setMatchPhase('et-halftime');
        setCurrentETPhase('ET-HT');
        setJustFinishedETPhase('ET1'); // Track that we just finished ET1
        
        // Log ET halftime start
        addMatchLogEvent('halftime_start', {
          type: 'et-halftime',
          previousPeriod: 'ET1'
        });

        // For ET Period 2, use opposite of the team that kicked off Period 2
        const period2KickoffTeam = getNextKickoffTeam(initialKickoffTeam, 2);
        const etPeriod2KickoffTeam = period2KickoffTeam === 'home' ? 'away' : 'home';
        setNextKickoffTeam(etPeriod2KickoffTeam);
      } else if (currentETPhase === 'ET2') {
        // End of ET Period 2 - check for penalties
        if (shouldGotoPenalties()) {
          console.log('⚽ Extra time completed, starting penalties');
          initializePenalties();
        } else {
          console.log('🏆 Match completed after ET2');
          setMatchPhase('completed');
          setIsMatchStarted(false);
          setIsExtraTime(false);
          setCurrentETPhase(null);
          setJustFinishedETPhase(null);
          setShowEndMatchButton(true); // Show end match button when match is completed
          
          // Log match completion
          addMatchLogEvent('match_completed', {
            finalScore: `${matchGoals.filter(goal => goal.team === 'home').length} - ${matchGoals.filter(goal => goal.team === 'away').length}`,
            homeGoals: matchGoals.filter(goal => goal.team === 'home').length,
            awayGoals: matchGoals.filter(goal => goal.team === 'away').length,
            extraTimeCompleted: true
          });
        }
      }
    } else {
      // Handle regular periods
      if (currentPeriod < totalPeriods) {
        // Start half-time between regular periods
        console.log(`🕐 Starting half-time after Period ${currentPeriod}`);
        setIsHalfTime(true);
        setHalfTimeSeconds(0);
        setMatchPhase('halftime');
        
        // Log halftime start
        addMatchLogEvent('halftime_start', {
          type: 'regular-halftime',
          previousPeriod: currentPeriod
        });

        // Set next kickoff team using alternation logic
        const nextTeam = getNextKickoffTeam(initialKickoffTeam, currentPeriod + 1);
        setNextKickoffTeam(nextTeam);
      } else {
        // End of regular time - check if extra time is enabled
        if (hasExtraTime) {
          console.log('⏰ Starting extra time preparation');
          // Start extra time half-time (before ET1)
          setIsHalfTime(true);
          setHalfTimeSeconds(0);
          setMatchPhase('et-halftime');
          setIsExtraTime(true);
          setCurrentETPhase('ET-HT');
          setJustFinishedETPhase(null); // No period finished yet, going into ET
          
          // Log extra time preparation
          addMatchLogEvent('extra_time_start', {
            type: 'preparation',
            previousPeriods: totalPeriods
          });

          // For ET Period 1, continue the alternation pattern
          // If Period 2 was kicked off by the opposite team, ET1 should be the same as Period 2
          const period2KickoffTeam = getNextKickoffTeam(initialKickoffTeam, 2);
          setNextKickoffTeam(period2KickoffTeam);
        } else {
          // No extra time - check for penalties
          if (shouldGotoPenalties()) {
            console.log('⚽ Regular time completed, starting penalties');
            initializePenalties();
          } else {
            console.log('🏆 Match completed - no extra time');
            // No extra time and no penalties - match completed
            setMatchPhase('completed');
            setIsMatchStarted(false);
            setShowEndMatchButton(true); // Show end match button when match is completed
            if (matchId) AsyncStorage.setItem(`match-completed-${matchId}`, 'true');
            
            // Log match completion
            addMatchLogEvent('match_completed', {
              finalScore: `${matchGoals.filter(goal => goal.team === 'home').length} - ${matchGoals.filter(goal => goal.team === 'away').length}`,
              homeGoals: matchGoals.filter(goal => goal.team === 'home').length,
              awayGoals: matchGoals.filter(goal => goal.team === 'away').length,
              extraTimeCompleted: false
            });
          }
        }
      }
    }
  };

  const handleHalfTimeEnd = () => {
    setIsHalfTime(false);
    setHalfTimeSeconds(0);
    setMainSeconds(0);
    
    // Reset injury time state for new period
    setIsInInjuryTime(false);
    setInjuryTimeStarted(0);

    // Switch kickoff team
    setKickoffTeam(nextKickoffTeam);
    setSelectedTeam(nextKickoffTeam);

    if (matchPhase === 'halftime') {
      // Regular half-time - move to next period
      setCurrentPeriod(prev => prev + 1);
      setMatchPhase('period');
    } else if (matchPhase === 'et-halftime') {
      // Extra time half-time - check what we just finished
      if (justFinishedETPhase === 'ET1') {
        // We just finished ET1, now start ET2
        console.log('▶️ Starting ET Period 2 after ET half-time');
        setCurrentETPhase('ET2');
        setJustFinishedETPhase(null); // Clear the tracking
      } else {
        // First time entering extra time (from regular time) - start ET1
        console.log('▶️ Starting ET Period 1 (first extra time)');
        setCurrentETPhase('ET1');
        setJustFinishedETPhase(null);
      }
      setMatchPhase('extratime');
    }
  };

  const fetchMatchData = async () => {
    if (!matchId) {
      setError('No match ID provided');
      setLoading(false);
      return;
    }
    try {
      setLoading(true);
      const response = await MatchService.getMatchById(matchId);
      if (response.success && response.data) {
        const formattedData = MatchService.convertApiResponseToDetailFormat(response.data);

        // Fetch squad data for both teams using SquadService
        try {
          const [homeSquadResponse, awaySquadResponse] = await Promise.all([
            SquadService.getSquadByMatch(matchId, 'home'),
            SquadService.getSquadByMatch(matchId, 'away')
          ]);

          // Add squad data to teams
          if (homeSquadResponse.success && homeSquadResponse.data) {
            formattedData.homeTeam.squad = homeSquadResponse.data;
            console.log('🏠 Home squad loaded:', homeSquadResponse.data);
          }
          if (awaySquadResponse.success && awaySquadResponse.data) {
            formattedData.awayTeam.squad = awaySquadResponse.data;
            console.log('✈️ Away squad loaded:', awaySquadResponse.data);
          }
        } catch (squadError) {
          console.error('Error fetching squad data:', squadError);
          // Continue without squad data
        }

        setMatchData(formattedData);
        setError(null);
      } else {
        setError(response.error || 'Failed to fetch match data');
      }
    } catch (err: any) {
      console.error('Error fetching match data:', err);
      setError('Failed to load match details');
    } finally {
      setLoading(false);
    }
  };

  const fetchMisconductSet = async () => {
    if (!misconductSet && matchData?.misconductCode) {
      try {
        console.log('🎯 Loading misconduct set by name:', matchData.misconductCode);

        // First try to load from AsyncStorage (selected misconduct set)
        const savedSet = await AsyncStorage.getItem('selectedMisconductSet');

        if (savedSet) {
          const parsedSet = JSON.parse(savedSet);
          // Check if the saved set matches the match's misconduct code
          if (parsedSet.name === matchData.misconductCode) {
            setLoadedMisconductSet(parsedSet);
            console.log('📋 Loaded matching misconduct set from storage:', parsedSet.name);
            return;
          }
        }

        // If no matching set in storage, create hardcoded sets and find match
        const walesSet = {
          id: 'wales-default',
          name: 'Wales',
          playersYellowCodes: [
            {
              id: 'c1', code: 'C1', description: 'Unsporting Behaviour', cardType: 'yellow', subcategories: [
                { id: 'c1-aa', code: 'AA', description: 'Adopting an aggressive attitude' },
                { id: 'c1-di', code: 'DI', description: 'Simulation' },
                { id: 'c1-dp', code: 'DP', description: 'Dangerous Play' },
                { id: 'c1-ft', code: 'FT', description: 'Foul Tackle' },
                { id: 'c1-gc', code: 'GC', description: 'Goal Celebration' },
                { id: 'c1-hb', code: 'HB', description: 'Handball' },
                { id: 'c1-rp', code: 'RP', description: 'Reckless Play' },
                { id: 'c1-sp', code: 'SP', description: 'Pushing or Pulling an opponent' },
                { id: 'c1-tr', code: 'TR', description: 'Tripping' },
                { id: 'c1-ub', code: 'UB', description: 'Unspecified Behaviour' }
              ]
            },
            { id: 'c2', code: 'C2', description: 'Shows dissent by word or action(T)', cardType: 'yellow', subcategories: [] },
            { id: 'c3', code: 'C3', description: 'Persistently infringing the Laws of the Game', cardType: 'yellow', subcategories: [] },
            { id: 'c4', code: 'C4', description: 'Delays restart', cardType: 'yellow', subcategories: [] },
            { id: 'c5', code: 'C5', description: 'Fails to retreat required distance at a restart', cardType: 'yellow', subcategories: [] },
            { id: 'c6', code: 'C6', description: 'Enters the field of play without permission', cardType: 'yellow', subcategories: [] },
            { id: 'c7', code: 'C7', description: 'Leaves the field of play without permission', cardType: 'yellow', subcategories: [] }
          ],
          playersRedCodes: [
            { id: 's1', code: 'S1', description: 'Serious foul play', cardType: 'red', subcategories: [] },
            { id: 's2', code: 'S2', description: 'Violent conduct', cardType: 'red', subcategories: [] },
            { id: 's3', code: 'S3', description: 'Spitting at an opponent or any other person', cardType: 'red', subcategories: [] },
            { id: 's4', code: 'S4', description: 'Denying a goal-scoring opportunity to opponents by deliberate handball', cardType: 'red', subcategories: [] },
            { id: 's5', code: 'S5', description: 'Denying a goal-scoring opportunity to opponents by an offense punishable by a free kick or penalty', cardType: 'red', subcategories: [] },
            { id: 's6', code: 'S6', description: 'Using offensive, insulting, or abusive language/gestures', cardType: 'red', subcategories: [] },
            { id: 's7', code: 'S7', description: 'Receiving a second caution', cardType: 'red', subcategories: [] }
          ],
          officialsYellowCodes: [{ id: 't1', code: 'T1', description: 'Yellow', cardType: 'yellow', subcategories: [] }],
          officialsRedCodes: [{ id: 't2', code: 'T2', description: 'Red', cardType: 'red', subcategories: [] }]
        };

        // Create England with FA Codes set
        const englandSet = {
          id: 'england-fa-default',
          name: 'England with FA Codes',
          playersYellowCodes: [
            {
              id: 'c1-eng',
              code: 'C1',
              description: 'Unsporting Behaviour',
              cardType: 'yellow',
              subcategories: [
                { id: 'c1-aa-eng', code: 'AA', description: 'Adopting an aggressive attitude' },
                { id: 'c1-di-eng', code: 'DI', description: 'Simulation' },
                { id: 'c1-dp-eng', code: 'DP', description: 'Dangerous Play' },
                { id: 'c1-ft-eng', code: 'FT', description: 'Foul Tackle' },
                { id: 'c1-gc-eng', code: 'GC', description: 'Goal Celebration' },
                { id: 'c1-hb-eng', code: 'HB', description: 'Handball' },
                { id: 'c1-rp-eng', code: 'RP', description: 'Reckless Play' },
                { id: 'c1-sp-eng', code: 'SP', description: 'Pushing or Pulling an opponent' },
                { id: 'c1-tr-eng', code: 'TR', description: 'Tripping' },
                { id: 'c1-ub-eng', code: 'UB', description: 'Unspecified Behaviour' }
              ]
            },
            { id: 'c2-eng', code: 'C2', description: 'Shows dissent by word or action(T)', cardType: 'yellow', subcategories: [] },
            { id: 'c3-eng', code: 'C3', description: 'Persistent infringement', cardType: 'yellow', subcategories: [] },
            { id: 'c4-eng', code: 'C4', description: 'Delays the restart of play', cardType: 'yellow', subcategories: [] },
            { id: 'c5-eng', code: 'C5', description: 'Fails to retreat required distance', cardType: 'yellow', subcategories: [] },
            { id: 'c6-eng', code: 'C6', description: 'Enters the FOP without permission', cardType: 'yellow', subcategories: [] },
            { id: 'c7-eng', code: 'C7', description: 'Leaves the FOP without permission', cardType: 'yellow', subcategories: [] }
          ],
          playersRedCodes: [
            { id: 's1-eng', code: 'S1', description: 'Serious foul play', cardType: 'red', subcategories: [] },
            {
              id: 's2-eng',
              code: 'S2',
              description: 'Violent conduct',
              cardType: 'red',
              subcategories: [
                { id: 's2a-eng', code: 'S2A', description: 'Head to head contact' },
                { id: 's2b-eng', code: 'S2B', description: 'Elbowing' },
                { id: 's2c-eng', code: 'S2C', description: 'Kicking' },
                { id: 's2d-eng', code: 'S2D', description: 'Stamping' },
                { id: 's2e-eng', code: 'S2E', description: 'Striking' },
                { id: 's2f-eng', code: 'S2F', description: 'Biting' },
                { id: 's2g-eng', code: 'S2G', description: 'Other' }
              ]
            },
            { id: 's3-eng', code: 'S3', description: 'Spitting', cardType: 'red', subcategories: [] },
            { id: 's4-eng', code: 'S4', description: 'Denies goal by hand', cardType: 'red', subcategories: [] },
            { id: 's5-eng', code: 'S5', description: 'DOGSO', cardType: 'red', subcategories: [] },
            { id: 's6-eng', code: 'S6', description: 'OFFINABUSS', cardType: 'red', subcategories: [] },
            { id: 's7-eng', code: 'S7', description: 'Second Caution', cardType: 'red', subcategories: [] }
          ],
          officialsYellowCodes: [
            { id: 't1-eng', code: 'T1', description: 'Clearly/persistently not respecting the confines of their team\'s technical area', cardType: 'yellow', subcategories: [] },
            { id: 't2-eng', code: 'T2', description: 'Delaying the restart of play by their team', cardType: 'yellow', subcategories: [] },
            { id: 't3-eng', code: 'T3', description: 'Deliberately entering the technical area of the opposing team (non-confrontational)', cardType: 'yellow', subcategories: [] },
            { id: 't4-eng', code: 'T4', description: 'Dissent by word or action', cardType: 'yellow', subcategories: [] },
            { id: 't5-eng', code: 'T5', description: 'Entering the referee review area (RRA)', cardType: 'yellow', subcategories: [] },
            { id: 't6-eng', code: 'T6', description: 'Excessively showing the TV signal for a VAR \'review\'', cardType: 'yellow', subcategories: [] },
            { id: 't7-eng', code: 'T7', description: 'Excessively/persistently gesturing for a red or yellow card', cardType: 'yellow', subcategories: [] },
            { id: 't8-eng', code: 'T8', description: 'Gestures which show a clear lack of respect for the match officials(s) e.g. sarcastic clapping', cardType: 'yellow', subcategories: [] },
            { id: 't9-eng', code: 'T9', description: 'Gesturing or acting in a provocative or inflammatory manner', cardType: 'yellow', subcategories: [] },
            { id: 't10-eng', code: 'T10', description: 'Persistent unacceptable behaviour (including repeated warning offences)', cardType: 'yellow', subcategories: [] },
            { id: 't11-eng', code: 'T11', description: 'Showing a lack of respect for the game', cardType: 'yellow', subcategories: [] },
            { id: 't12-eng', code: 'T12', description: 'Throwing/kicking drinks bottles or other objects', cardType: 'yellow', subcategories: [] },
            { id: 't13-eng', code: 'T13', description: 'Other', cardType: 'yellow', subcategories: [] }
          ],
          officialsRedCodes: [
            { id: 't14-eng', code: 'T14', description: 'Delaying the restart of play by the opposing team', cardType: 'red', subcategories: [] },
            { id: 't15-eng', code: 'T15', description: 'Deliberately leaving the technical area to show dissent or remonstrate with a Match Official', cardType: 'red', subcategories: [] },
            { id: 't16-eng', code: 'T16', description: 'Deliberately leaving the technical to act in a provocative or inflammatory manner', cardType: 'red', subcategories: [] },
            { id: 't17-eng', code: 'T17', description: 'Deliberately leaving the technical to enter the opposing technical area in an aggressive or confrontational manner', cardType: 'red', subcategories: [] },
            { id: 't18-eng', code: 'T18', description: 'Deliberately throwing/kicking an object onto the field of play', cardType: 'red', subcategories: [] },
            { id: 't19-eng', code: 'T19', description: 'Entering the field of play to confront a match official (including half time and full-time)', cardType: 'red', subcategories: [] },
            { id: 't20-eng', code: 'T20', description: 'Entering the field of play to interfere with play, an opposing player or match official', cardType: 'red', subcategories: [] },
            { id: 't21-eng', code: 'T21', description: 'Entering the video operation room (VOR)', cardType: 'red', subcategories: [] },
            { id: 't22-eng', code: 'T22', description: 'Physical or aggressive behaviour (including biting and spitting)', cardType: 'red', subcategories: [] },
            { id: 't23-eng', code: 'T23', description: 'Receiving a second caution in the same match', cardType: 'red', subcategories: [] },
            { id: 't24-eng', code: 'T24', description: 'Using offensive, insulting or abusive language and/or gestures', cardType: 'red', subcategories: [] },
            { id: 't25-eng', code: 'T25', description: 'Using unauthorised electronic or communication equipment and/or behaving in an appropriate manner as the result of using such equipment', cardType: 'red', subcategories: [] },
            { id: 't26-eng', code: 'T26', description: 'Violent Conduct', cardType: 'red', subcategories: [] },
            { id: 't27-eng', code: 'T27', description: 'Other', cardType: 'red', subcategories: [] }
          ]
        };

        // Check if match uses Wales set
        if (matchData.misconductCode === 'Wales') {
          setLoadedMisconductSet(walesSet);
          console.log('📋 Loaded Wales misconduct set for match');
          return;
        }

        // Check if match uses England set (handle both "England" and "England with FA Codes")
        if (matchData.misconductCode === 'England with FA Codes' || matchData.misconductCode === 'England') {
          setLoadedMisconductSet(englandSet);
          console.log('📋 Loaded England misconduct set for match:', matchData.misconductCode);
          return;
        }

        console.log('⚠️ No matching misconduct set found for:', matchData.misconductCode);
      } catch (error) {
        console.error('Error loading misconduct set:', error);
      }
    }
  };

  useEffect(() => {
    fetchMatchData();
  }, [matchId]);

  // Fetch misconduct set when match data is loaded
  useEffect(() => {
    if (matchData) {
      fetchMisconductSet();
    }
  }, [matchData]);

  // Debug match data when it loads
  useEffect(() => {
    if (matchData) {
      console.log('📋 Match Data Loaded:', {
        extraTime: matchData.extraTime,
        extraTimeType: typeof matchData.extraTime,
        extraTimeLengths: matchData.extraTimeLengths,
        numberOfPeriods: matchData.numberOfPeriods,
        periodLengths: matchData.periodLengths,
        halfTime: matchData.halfTime
      });
    }
  }, [matchData]);

  // Auto-start match when synced user joins
  useEffect(() => {
    console.log('🏁 Auto-start check:', {
      autoStart,
      hasMatchData: !!matchData,
      autoKickoffTeam,
      isMatchStarted
    });

    if (autoStart && matchData && autoKickoffTeam && !isMatchStarted) {
      console.log('🏁 Auto-starting match for synced user');
      console.log('🏁 Kickoff team:', autoKickoffTeam);

      // Set the selected team and start the match
      setSelectedTeam(autoKickoffTeam);
      setKickoffTeam(autoKickoffTeam);
      setFirstKickoffTeam(autoKickoffTeam);
      setNextKickoffTeam(autoKickoffTeam === 'home' ? 'away' : 'home');

      // For synced users, start the match immediately with timer running
      setIsMatchStarted(true);
      setMainSeconds(0);
      setCurrentPeriod(1);
      setMatchPhase('period');
      setIsHalfTime(false); // Ensure timer starts running

      console.log('🏁 Auto-started match for synced user - timer should be running');

      // Log match start event (but don't broadcast since this is a synced user)
      addMatchLogEvent('period_start', {
        period: 1,
        kickoffTeam: autoKickoffTeam,
        teamName: autoKickoffTeam === 'home'
          ? (matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home')
          : (matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away')
      });
    }
  }, [autoStart, matchData, autoKickoffTeam, isMatchStarted]);

  // Listen for automatic period end events from synced referee
  useEffect(() => {
    const handleAutoPeriodEnd = (eventData: any) => {
      console.log('🔔 Auto period end received:', eventData);

      // Only handle if this is for the current match
      if (eventData.matchId === matchId) {
        console.log('🔔 Processing auto period end for current match');

        // Call the period end handler automatically
        handlePeriodEnd();
      }
    };

    const subscription = DeviceEventEmitter.addListener('autoPeriodEnd', handleAutoPeriodEnd);

    return () => {
      subscription.remove();
    };
  }, [matchId]);

  const handleTeamSelect = (team: 'home' | 'away') => {
    if (!isMatchStarted || matchPhase === 'halftime' || matchPhase === 'et-halftime') {
      setSelectedTeam(team);
      if (!firstKickoffTeam) {
        // First kickoff team selection
        setKickoffTeam(team);
        setFirstKickoffTeam(team);
        // Set next kickoff team for second period (opposite)
        setNextKickoffTeam(team === 'home' ? 'away' : 'home');
      }
    }
  };

  const getNextKickoffTeam = (currentTeam: 'home' | 'away', periodNumber: number): 'home' | 'away' => {
    // Team alternation logic:
    // Period 1: Selected team
    // Period 2: Opposite team
    // ET Period 1: Same as Period 2 (away if home started, home if away started)
    // ET Period 2: Opposite of ET Period 1

    if (periodNumber % 2 === 1) {
      // Odd periods (1, ET1): return the current team
      return currentTeam;
    } else {
      // Even periods (2, ET2): return opposite team
      return currentTeam === 'home' ? 'away' : 'home';
    }
  };

  const handleStartMatch = async () => {
    if (!selectedTeam) return;
    setIsMatchStarted(true);
    setMainSeconds(0);
    setCurrentPeriod(1);
    setMatchPhase('period');

    // Log match start event
    addMatchLogEvent('period_start', {
      period: 1,
      kickoffTeam: selectedTeam,
      teamName: selectedTeam === 'home'
        ? (matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home')
        : (matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away')
    });

    // Broadcast match start to synced participants
    try {
      if (matchId) {
        console.log('📡 Broadcasting match start to synced users...');
        await BroadcastService.broadcastMatchStart(
          matchId,
          selectedTeam,
          1,
          matchData
        );
        console.log('✅ Match start broadcasted successfully');
      }
    } catch (error) {
      console.error('❌ Failed to broadcast match start:', error);
      // Don't block match start if broadcast fails
    }
  };

  const handleStartNextPeriod = () => {
    console.log('▶️ Starting next period:', {
      matchPhase,
      nextKickoffTeam,
      isExtraTime,
      currentETPhase,
      currentPeriod
    });

    if ((matchPhase === 'halftime' || matchPhase === 'et-halftime') && nextKickoffTeam) {
      // Log halftime end
      addMatchLogEvent('halftime_end', {
        type: matchPhase,
        duration: halfTimeSeconds
      });
      
      setSelectedTeam(nextKickoffTeam);
      setKickoffTeam(nextKickoffTeam);
      setIsHalfTime(false);
      setMainSeconds(0);
      setHalfTimeSeconds(0);

      if (matchPhase === 'halftime') {
        // Regular half-time - move to next period
        console.log('▶️ Starting regular period', currentPeriod + 1);
        setCurrentPeriod(prev => prev + 1);
        setMatchPhase('period');
        
        // Log new period start
        addMatchLogEvent('period_start', {
          period: currentPeriod + 1,
          kickoffTeam: nextKickoffTeam,
          teamName: nextKickoffTeam === 'home' 
            ? (matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home')
            : (matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away')
        });
      } else if (matchPhase === 'et-halftime') {
        // Extra time half-time - check what we just finished
        if (justFinishedETPhase === 'ET1' || (isExtraTime && currentETPhase === 'ET-HT')) {
          // We just finished ET1, now start ET2
          console.log('▶️ Starting ET Period 2 after ET half-time');
          setCurrentETPhase('ET2');
          setJustFinishedETPhase(null);
          
          // Log ET Period 2 start
          addMatchLogEvent('period_start', {
            period: 'ET2',
            kickoffTeam: nextKickoffTeam,
            teamName: nextKickoffTeam === 'home' 
              ? (matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home')
              : (matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away')
          });
        } else if (!isExtraTime) {
          // First time entering extra time (from regular time)
          console.log('▶️ Entering extra time for first time - ET1');
          setIsExtraTime(true);
          setCurrentETPhase('ET1');
          
          // Log ET Period 1 start
          addMatchLogEvent('period_start', {
            period: 'ET1',
            kickoffTeam: nextKickoffTeam,
            teamName: nextKickoffTeam === 'home' 
              ? (matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home')
              : (matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away')
          });
        }
        setMatchPhase('extratime');
      }
    }
  };

  const panGesture = Gesture.Pan()
    .enabled(!cardFlow.isActive && !goalFlow.isActive && !substitutionFlow.isActive) // Enable for both pre-match and in-match
    .onEnd((event) => {
      const gestureTranslationX = event.translationX;

      let targetView: 'center' | 'home' | 'away' = currentView;

      // Determine target view based on swipe direction and current position
      if (Math.abs(gestureTranslationX) > 100) {
        if (gestureTranslationX > 100) {
          // Swipe right
          if (currentView === 'center') {
            targetView = 'home';
          } else if (currentView === 'away') {
            targetView = 'center';
          }
        } else if (gestureTranslationX < -100) {
          // Swipe left
          if (currentView === 'center') {
            targetView = 'away';
          } else if (currentView === 'home') {
            targetView = 'center';
          }
        }
      }

      // Update current view
      setCurrentView(targetView);
    });

  const handleActionPress = (action: string, team: 'home' | 'away') => {
    console.log(`${action} pressed for ${team} team`);

    if (action === 'yellow' || action === 'red') {
      setCardFlow({
        isActive: true,
        step: 'person',
        cardType: action as 'yellow' | 'red',
        team,
        personType: null,
        selectedPlayer: null,
        selectedOfficial: null,
        selectedCode: null,
        selectedSubCode: null,
        selectedMainCode: null,
        isSecondYellow: false,
        originalYellowCard: null,
      });
    } else if (action === 'substitution') {
      handleSubstitutionPress(team);
    } else if (action === 'goal') {
      // Check if we're in penalty phase
      if (matchPhase === 'penalties') {
        // During penalties, check if team can take a penalty
        if (canTeamTakePenalty(team)) {
          setPenaltyFlow({
            isActive: true,
            step: 'outcome',
            team: team,
            round: penaltyRound,
            outcome: null,
            selectedPlayer: null,
          });
        } else {
          // Team cannot take penalty right now
          const homeCount = penaltyResults.home.length;
          const awayCount = penaltyResults.away.length;
          const teamCount = team === 'home' ? homeCount : awayCount;
          const otherTeamCount = team === 'home' ? awayCount : homeCount;
          
          if (homeCount >= 5 && awayCount >= 5) {
            // Sudden death - strict alternating
            const correctTeam = getNextPenaltyTeam();
            const correctTeamName = correctTeam === 'home'
              ? (matchData?.homeTeam?.shortName || 'Home')
              : (matchData?.awayTeam?.shortName || 'Away');
            Alert.alert('Sudden Death', `During sudden death, it's ${correctTeamName}'s turn.`);
          } else if (teamCount > otherTeamCount) {
            // Team is ahead and cannot take another until other team catches up
            const otherTeamName = team === 'home'
              ? (matchData?.awayTeam?.shortName || 'Away')
              : (matchData?.homeTeam?.shortName || 'Home');
            Alert.alert('Wait Your Turn', `${otherTeamName} must take a penalty first.`);
          } else {
            // Other reasons (like team reached 5 while other hasn't)
            Alert.alert('Cannot Take Penalty', `This team cannot take a penalty right now.`);
          }
        }
      } else {
        // Normal goal flow
        setGoalFlow({
          isActive: true,
          step: 'player',
          team,
          selectedPlayer: null,
        });
      }
    }
  };

  const resetCardFlow = () => {
    setCardFlow({
      isActive: false,
      step: 'type',
      cardType: null,
      team: null,
      personType: null,
      selectedPlayer: null,
      selectedOfficial: null,
      selectedCode: null,
      selectedSubCode: null,
      selectedMainCode: null,
      isSecondYellow: false,
      originalYellowCard: null,
    });
    
    // Return to center view after card action completes
    setCurrentView('center');
  };

  // Goal system functions
  const handleGoalPlayerSelect = (playerNumber: number) => {
    setGoalFlow(prev => ({
      ...prev,
      selectedPlayer: playerNumber,
      step: 'display',
    }));

    // Record the goal
    const currentMinute = Math.floor(mainSeconds / 60);
    const teamData = goalFlow.team === 'home' ? matchData?.homeTeam : matchData?.awayTeam;
    const player = teamData?.squad?.players?.find((p: any) => p.number === playerNumber);
    const playerName = player?.name || `Player ${playerNumber}`;

    const newGoal = {
      id: `goal-${Date.now()}-${Math.random()}`,
      team: goalFlow.team!,
      playerNumber,
      playerName,
      minute: currentMinute,
    };

    setMatchGoals(prev => [...prev, newGoal]);
    
    // Log goal event
    addMatchLogEvent('goal', {
      team: newGoal.team,
      playerNumber: newGoal.playerNumber,
      playerName: newGoal.playerName,
      minute: newGoal.minute,
      time: getFormattedMatchTime()
    });
    
    console.log('Goal recorded:', newGoal);

    // Add injury time for goals (if enabled)
    addInjuryTime('goal');

    // Auto-hide after 4 seconds
    setTimeout(() => {
      resetGoalFlow();
    }, 4000);
  };

  const resetGoalFlow = () => {
    setGoalFlow({
      isActive: false,
      step: 'player',
      team: null,
      selectedPlayer: null,
    });
    
    // Return to center view after goal action completes
    setCurrentView('center');
  };

  // Check if player already has a yellow card
  const checkForExistingYellow = (team: 'home' | 'away', playerNumber: number) => {
    return matchCards.find(card =>
      card.team === team &&
      card.playerNumber === playerNumber &&
      card.cardType === 'yellow' &&
      !card.isSecondYellow
    );
  };

  // Check if player has been red carded (and thus sent off)
  const isPlayerRedCarded = (team: 'home' | 'away', playerNumber: number) => {
    return matchCards.some(card =>
      card.team === team &&
      card.playerNumber === playerNumber &&
      card.cardType === 'red'
    );
  };

  // Check if team has fewer than 6 players on the field
  const checkRedCardWarning = (team: 'home' | 'away') => {
    const teamData = team === 'home' ? matchData?.homeTeam : matchData?.awayTeam;
    if (!teamData?.squad?.players) return false;

    // Get all players currently on the field of play (have 'STARTER' position).
    // This intentionally excludes substitutes. A player who receives a red card
    // is moved to the 'SUBSTITUTE' position, so they are automatically excluded from this count.
    const playersOnField = teamData.squad.players.filter((p: any) => p.position === 'STARTER');
    
    // Count how many of the players on the field are currently in the sin bin
    const sinBinOnFieldPlayers = sinBinPlayers.filter(sinBinPlayer => 
      sinBinPlayer.team === team && 
      playersOnField.some((player: any) => player.number === sinBinPlayer.playerNumber)
    );
    
    // Calculate the number of players actively playing
    const activePlayersCount = playersOnField.length - sinBinOnFieldPlayers.length;
    
    // The warning triggers when there are exactly 6 players. The next red card or sin bin
    // for an on-field player would reduce the count to 6, which is below the minimum required.
    const needsWarning = activePlayersCount === 6 && !redCardWarningDismissed[team];
    
    console.log(`🚨 Player Count Warning Check for ${team}:`, {
      playersOnField: playersOnField.length,
      sinBinOnFieldPlayers: sinBinOnFieldPlayers.length,
      activePlayersCount,
      threshold: 'exactly 7 active players',
      needsWarning,
      warningDismissed: redCardWarningDismissed[team],
    });
    
    return needsWarning;
  };

  // Trigger red card warning check for a specific team
  const triggerRedCardWarningCheck = (team: 'home' | 'away') => {
    const shouldShow = checkRedCardWarning(team);
    
    // Log warning event if needed
    if (shouldShow) {
      addMatchLogEvent('warning', {
        type: 'player_count_warning',
        team,
        message: `Team ${team} has only 6 players remaining on field`,
        time: getFormattedMatchTime()
      });
    }
    
    setShowRedCardWarning(prev => ({
      ...prev,
      [team]: shouldShow
    }));
  };

  // Handle red card warning actions
  const handleRedCardWarningIgnore = (team: 'home' | 'away') => {
    setRedCardWarningDismissed(prev => ({
      ...prev,
      [team]: true
    }));
    setShowRedCardWarning(prev => ({
      ...prev,
      [team]: false
    }));
    
    // Log warning dismissal
    addMatchLogEvent('warning', {
      type: 'dismissed',
      team,
      message: `Red card warning dismissed for team ${team}`,
      time: getFormattedMatchTime()
    });
    
    console.log(`🔕 Red card warning dismissed for ${team} team`);
  };

  // Handle match abandonment due to insufficient players
  const handleRedCardWarningAbandon = (team: 'home' | 'away') => {
    // Dismiss the warning modal
    setShowRedCardWarning(prev => ({
      ...prev,
      [team]: false
    }));
    
    // Add match abandonment log event to state first
    addMatchLogEvent('match_abandoned', {
      reason: `Team ${team} has insufficient players`,
      time: getFormattedMatchTime()
    });
    
    // Stop the match immediately
    setIsMatchStarted(false);
    setMatchPhase('completed');
    setIsHalfTime(false);
    setIsExtraTime(false);
    setIsInInjuryTime(false);
    setShowEndMatchButton(true); // Show the end match button
    if (matchId) AsyncStorage.setItem(`match-completed-${matchId}`, 'true');
    
    console.log(`🚨 Match abandoned: Team ${team} has insufficient players`);
    
    // Show alert to inform about abandonment
    Alert.alert(
      'Match Abandoned',
      `The match has been abandoned because ${team === 'home' ? matchData?.homeTeam?.name : matchData?.awayTeam?.name} has insufficient players.`,
      [
        {
          text: 'OK',
          onPress: () => console.log('Match abandonment acknowledged')
        }
      ]
    );
  };

  // Handle match reset - reset everything to initial state
  const handleResetMatch = () => {
    Alert.alert(
      'Reset Match',
      'This will reset the entire match. All progress will be lost and you will need to select teams again. Are you sure?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: () => {
            console.log('🔄 Resetting match to initial state');
            
            // Reset all match state
            setIsMatchStarted(false);
            setMatchPhase('period');
            setIsHalfTime(false);
            setIsExtraTime(false);
            setIsInInjuryTime(false);
            setShowEndMatchButton(false);
            setMenuVisible(false);
            
            // Reset timer state
            setMainSeconds(0);
            setHalfTimeSeconds(0);
            setCurrentPeriod(1);
            setCurrentETPhase(null);
            setJustFinishedETPhase(null);
            setInjuryTimeStarted(0);
            
            // Reset team selection
            setSelectedTeam(null);
            setKickoffTeam(null);
            setFirstKickoffTeam(null);
            setNextKickoffTeam(null);
            
            // Reset match events
            setMatchGoals([]);
            setMatchCards([]);
            setMatchSubstitutions([]);
            setMatchLog([]);
            setSinBinPlayers([]);
            
            // Reset substitution opportunities
            setSubstitutionOpportunities({
              home: [],
              away: []
            });
            
            // Reset injury time
            setInjuryTimeSeconds({
              period1: 0,
              period2: 0,
              ET1: 0,
              ET2: 0
            });
            
            // Reset warning dismissals
            setRedCardWarningDismissed({
              home: false,
              away: false
            });
            
            // Reset views
            setCurrentView('center');
            
            console.log('✅ Match reset complete');
          }
        }
      ]
    );
  };

  // Handle match abandonment - log abandonment and show end match button
  const handleAbandonMatch = () => {
    console.log('🚨 Match abandoned by referee');
    
    // Add match abandonment log event to state first
    addMatchLogEvent('match_abandoned', {
      reason: 'Match abandoned by referee',
      time: getFormattedMatchTime()
    });
    
    // Stop the match immediately
    setIsMatchStarted(false);
    setMatchPhase('completed');
    setIsHalfTime(false);
    setIsExtraTime(false);
    setIsInInjuryTime(false);
    setShowEndMatchButton(true); // Show the end match button
    if (matchId) AsyncStorage.setItem(`match-completed-${matchId}`, 'true');
    
    // Close the menu modal immediately
    setMenuVisible(false);
  };

  // Handle end match - show evaluation modal first
  const handleEndMatch = async () => {
    if (!matchId) {
      console.error('No match ID available for ending match');
      navigation.goBack();
      return;
    }

    // Show evaluation modal before ending match
    setShowEvaluationModal(true);
  };
  
  // Complete match after evaluation is done
  const completeMatchAfterEvaluation = async (evaluationAnswers?: EvaluationAnswer[]) => {
    if (!matchId) {
      console.error('No match ID available for completing match');
      return;
    }

    try {
      // Save match logs to database first
      const logsToSave = matchLog.map(event => ({
        id: event.id,
        type: event.type,
        timestamp: event.timestamp,
        realTime: event.realTime instanceof Date ? event.realTime.toISOString() : event.realTime,
        data: event.data
      }));
      
      const logResponse = await MatchService.saveMatchLogs(matchId, logsToSave);
      
      if (!logResponse.success) {
        console.error('❌ Failed to save match logs:', logResponse.error);
        Alert.alert('Error', 'Failed to save match logs. Please try again.');
        return;
      }
      
      console.log('✅ Match logs successfully saved');
      
      // Update match status to COMPLETED
      const response = await MatchService.updateMatchStatus(matchId, 'COMPLETED');
      
      if (response.success) {
        console.log('✅ Match successfully marked as completed');
        setShowEndMatchButton(false); // Hide the end match button
        setShowEvaluationModal(false); // Hide evaluation modal

        // Navigate to match results with the match data
        (navigation as any).navigate('MatchResults', { 
          matchId, 
          matchLog, 
          fromEvaluation: true 
        });
      } else {
        console.error('❌ Failed to mark match as completed:', response.error);
        Alert.alert('Error', 'Failed to end match. Please try again.');
      }
    } catch (error) {
      console.error('❌ Error ending match:', error);
      Alert.alert('Error', 'An error occurred while ending the match.');
    }
  };
  
  // Complete penalty match with evaluation
  const completePenaltyMatchAfterEvaluation = async (updatedLog: any[], evaluationAnswers?: EvaluationAnswer[]) => {
    if (!matchId) {
      console.error('No match ID available for completing penalty match');
      return;
    }

    try {
      console.log('💾 Saving penalty match logs to database...');
      
      // Save the updated logs (including penalty_end) to database first
      const logsToSave = updatedLog.map(event => ({
        id: event.id,
        type: event.type,
        timestamp: event.timestamp,
        realTime: event.realTime instanceof Date ? event.realTime.toISOString() : event.realTime,
        data: event.data
      }));
      
      const logResponse = await MatchService.saveMatchLogs(matchId, logsToSave);
      
      if (!logResponse.success) {
        console.error('❌ Failed to save match logs with penalty_end:', logResponse.error);
        Alert.alert('Error', 'Failed to save match logs. Please try again.');
        return;
      }
      
      console.log('✅ Match logs with penalty_end successfully saved to database');
      
      // Update match status to COMPLETED
      const response = await MatchService.updateMatchStatus(matchId, 'COMPLETED');
      
      if (response.success) {
        console.log('✅ Penalty match successfully marked as completed');
        setShowEvaluationModal(false);
        
        // Navigate to match results
        (navigation as any).navigate('MatchResults', { 
          matchId, 
          matchLog: updatedLog, 
          fromEvaluation: true 
        });
      } else {
        console.error('❌ Failed to mark penalty match as completed:', response.error);
        Alert.alert('Error', 'Failed to end match. Please try again.');
      }
    } catch (error) {
      console.error('❌ Error ending penalty match:', error);
      Alert.alert('Error', 'An error occurred while ending the match.');
    }
  };

  const handlePersonTypeSelect = (personType: 'player' | 'substitute' | 'official') => {
    setCardFlow(prev => ({
      ...prev,
      step: 'player',  // Always go to player selection for all person types
      personType,
    }));
  };

  const handlePlayerSelect = (playerNumber: number) => {
    // Check if this player already has a yellow card and we're giving another yellow
    const existingYellow = checkForExistingYellow(cardFlow.team!, playerNumber);
    const isSecondYellow = cardFlow.cardType === 'yellow' && !!existingYellow;

    setCardFlow(prev => ({
      ...prev,
      selectedPlayer: playerNumber,
      selectedOfficial: null, // Clear official when selecting player
      isSecondYellow,
      originalYellowCard: existingYellow || null,
      // If it's a second yellow, automatically make it a red card
      cardType: isSecondYellow ? 'red' : prev.cardType,
    }));
  };

  const handleOfficialSelect = (index: number, official: any) => {
    setCardFlow(prev => ({
      ...prev,
      selectedPlayer: index, // Use index as identifier for officials
      selectedOfficial: {
        index,
        role: official.role,
        name: official.name
      },
      step: 'code', // Go to code selection after selecting official
    }));
  };

  const handlePlayerConfirm = () => {
    setCardFlow(prev => ({
      ...prev,
      step: 'code',
    }));
  };

  const handleCodeSelect = (mainCodeObj: any) => {
    // If the code has subcategories, go to subcode selection
    if (mainCodeObj.subcategories && mainCodeObj.subcategories.length > 0) {
      setCardFlow(prev => ({
        ...prev,
        selectedMainCode: mainCodeObj,
        selectedCode: mainCodeObj.code,
        step: 'subcode',
      }));
    } else {
      // No subcategories, go directly to confirm
      setCardFlow(prev => ({
        ...prev,
        selectedMainCode: mainCodeObj,
        selectedCode: mainCodeObj.code,
        selectedSubCode: null,
        step: 'confirm',
      }));
    }
  };

  const handleSubCodeSelect = (subCode: string) => {
    setCardFlow(prev => ({
      ...prev,
      selectedSubCode: subCode,
      step: 'confirm',
    }));
  };

  const handleCardConfirm = async () => {
    if (cardFlow.selectedPlayer === null || !cardFlow.selectedCode || !cardFlow.team) return;

    const currentMinute = isMatchStarted ? Math.floor(mainSeconds / 60) : 0;

    const teamData = cardFlow.team === 'home' ? matchData?.homeTeam : matchData?.awayTeam;
    
    // Correctly get player name for players, substitutes, and officials
    const playerName = cardFlow.personType === 'official'
      ? cardFlow.selectedOfficial?.name || 'Official'
      : (teamData?.squad?.players?.find((p: any) => p.number === cardFlow.selectedPlayer)?.name || 'Unknown Player');

    const newCard = {
      id: `${Date.now()}-${Math.random()}`,
      team: cardFlow.team,
      // Correctly assign player number for players/substitutes vs officials
      playerNumber: cardFlow.personType === 'official' ? 0 : cardFlow.selectedPlayer,
      playerName,
      cardType: cardFlow.cardType as 'yellow' | 'red',
      code: cardFlow.selectedCode,
      subCode: cardFlow.selectedSubCode || undefined,
      minute: currentMinute,
      isSecondYellow: cardFlow.isSecondYellow,
      originalYellowId: cardFlow.originalYellowCard?.id,
    };

    setMatchCards(prev => [...prev, newCard]);

    // Log card event with appropriate time format (penalty round or match time)
    const logData: any = {
      cardType: newCard.cardType,
      team: newCard.team,
      playerNumber: newCard.playerNumber,
      playerName: newCard.playerName,
      code: newCard.code,
      subCode: newCard.subCode,
      minute: newCard.minute,
      isSecondYellow: newCard.isSecondYellow,
      time: getFormattedMatchTime()
    };
    
    // Add penalty round info if during penalties
    if (matchPhase === 'penalties') {
      logData.penaltyRound = penaltyRound;
    }
    
    addMatchLogEvent('card', logData);

    addInjuryTime('sanction');

    if (cardFlow.cardType === 'yellow' && cardFlow.selectedCode === 'C2' && cardFlow.personType === 'player' && matchData?.temporaryDismissals) {
      addPlayerToSinBin(cardFlow.team, cardFlow.selectedPlayer, playerName);
    }

    const isRedCard = cardFlow.cardType === 'red';
    const isPlayerOrSub = cardFlow.personType !== 'official';

    resetCardFlow();

    if (isRedCard && isPlayerOrSub) {
      let newMatchData = JSON.parse(JSON.stringify(matchData));
      const targetTeam = newCard.team === 'home' ? 'homeTeam' : 'awayTeam';
      const squadPlayers = newMatchData[targetTeam]?.squad?.players;
      
      if (squadPlayers) {
        const playerIndex = squadPlayers.findIndex((p: any) => p.number === newCard.playerNumber);
        if (playerIndex > -1) {
          squadPlayers[playerIndex].position = 'SUBSTITUTE';
        }
      }
      
      setMatchData(newMatchData);
      console.log(`🔴 Red card: Player ${newCard.playerNumber} moved to substitutes`);

      try {
        const currentTeamData = newCard.team === 'home' ? newMatchData.homeTeam : newMatchData.awayTeam;
        if (currentTeamData?.squad && matchId) {
          const starters = currentTeamData.squad.players.filter((p: any) => p.position === 'STARTER');
          const substitutes = currentTeamData.squad.players.filter((p: any) => p.position === 'SUBSTITUTE');
          const officials = currentTeamData.squad.officials || [];
          
          const squadData = SquadService.convertToApiFormat(
            matchId,
            newCard.team,
            starters,
            substitutes,
            officials
          );
          
          await SquadService.saveSquad(squadData);
          console.log(`📋 Red card: Squad data updated for ${newCard.team} team`);
        }
      } catch (error) {
        console.error('Error updating squad data after red card:', error);
      }

      // Only trigger forced substitution if NOT during penalties
      if (!isMatchStarted && matchPhase !== 'penalties') {
        console.log('🔄 Pre-match red card: Starting forced substitution...');
        setTimeout(() => {
          setSubstitutionFlow({
            isActive: true,
            step: 'player-in',
            team: newCard.team,
            selectedPlayerOut: newCard.playerNumber,
            selectedPlayerIn: null,
            currentOpportunityId: null,
          });
        }, 500);
      } else if (matchPhase === 'penalties') {
        console.log(`🔴 Red card during penalties: Player ${newCard.playerNumber} dismissed - NO substitution triggered`);
      } else if (cardFlow.personType === 'player') { // Only trigger warning for on-field players
        // Perform check immediately with the updated data to avoid stale state
        const updatedTeamData = newCard.team === 'home' ? newMatchData.homeTeam : newMatchData.awayTeam;
        if (updatedTeamData?.squad?.players) {
          const playersOnField = updatedTeamData.squad.players.filter((p: any) => p.position === 'STARTER');
          const sinBinOnFieldPlayers = sinBinPlayers.filter(sinBinPlayer => 
            sinBinPlayer.team === newCard.team && 
            playersOnField.some((player: any) => player.number === sinBinPlayer.playerNumber)
          );
          const activePlayersCount = playersOnField.length - sinBinOnFieldPlayers.length;
          const needsWarning = activePlayersCount === 6 && !redCardWarningDismissed[newCard.team];

          console.log(`🚨 Player Count Warning Check for ${newCard.team}:`, {
            playersOnField: playersOnField.length,
            sinBinOnFieldPlayers: sinBinOnFieldPlayers.length,
            activePlayersCount,
            threshold: 'exactly 6 active players',
            needsWarning,
            warningDismissed: redCardWarningDismissed[newCard.team],
          });

          if (needsWarning) {
            addMatchLogEvent('warning', {
              type: 'player_count_warning',
              team: newCard.team,
              message: `Team ${newCard.team} has only 6 players remaining on field`,
              time: getFormattedMatchTime()
            });
            setShowRedCardWarning(prev => ({ ...prev, [newCard.team]: true }));
          }
        }
      }
    }
  };

  // Substitution system functions
  const handleSubstitutionPress = (team: 'home' | 'away') => {
    // During penalties, only goalkeeper substitutions allowed (player to player)
    if (matchPhase === 'penalties') {
      setSubstitutionFlow({
        isActive: true,
        step: 'player-out',
        team,
        selectedPlayerOut: null,
        selectedPlayerIn: null,
        currentOpportunityId: null, // No opportunities during penalties
      });
      return;
    }
    
    // Normal substitution logic for regular play
    // Check if team can make substitutions
    if (!canTeamMakeSubstitution(team)) {
      return;
    }

    // Start or continue a substitution opportunity
    const activeOpportunity = getOrCreateActiveOpportunity(team);
    
    setSubstitutionFlow({
      isActive: true,
      step: 'player-out',
      team,
      selectedPlayerOut: null,
      selectedPlayerIn: null,
      currentOpportunityId: activeOpportunity.id,
    });
  };

  const canTeamMakeSubstitution = (team: 'home' | 'away'): boolean => {
    if (!matchData) return false;

    // Check max substitutions limit
    const teamSubs = matchSubstitutions.filter(sub => sub.team === team);
    let maxSubs;
    
    if (isExtraTime) {
      // In extra time: regular time allowance + extra time additional allowance
      const regularTimeAllowance = matchData.maxSubstitute || 0;
      const extraTimeAdditionalAllowance = matchData.extraTimeAdditionalSubAllowance || 0;
      maxSubs = regularTimeAllowance + extraTimeAdditionalAllowance;
      
      console.log(`🔄 Extra time substitution check for ${team}:`, {
        regularTimeAllowance,
        extraTimeAdditionalAllowance,
        totalAllowed: maxSubs,
        currentlyUsed: teamSubs.length,
        remainingAvailable: maxSubs - teamSubs.length
      });
    } else {
      // In regular time: just the regular allowance
      maxSubs = matchData.maxSubstitute || 0;
      
      console.log(`⏰ Regular time substitution check for ${team}:`, {
        regularTimeAllowance: maxSubs,
        currentlyUsed: teamSubs.length,
        remainingAvailable: maxSubs - teamSubs.length
      });
    }
    
    if (teamSubs.length >= maxSubs) {
      console.log(`❌ Team ${team} has reached max substitutions: ${teamSubs.length}/${maxSubs}`);
      return false;
    }

    // Check substitute opportunities if enabled
    if (matchData.substituteOpportunities) {
      const teamOpportunities = substitutionOpportunities[team];
      const closedOpportunities = teamOpportunities.filter(opp => opp.isClosed).length;
      let maxOpportunities;
      
      if (isExtraTime) {
        // In extra time: regular opportunities + extra time opportunities
        const regularOpportunities = matchData.substituteOpportunitiesAllowance || 0;
        const extraOpportunities = matchData.extraTimeSubOpportunitiesAllowance || 0;
        maxOpportunities = regularOpportunities + extraOpportunities;
        
        console.log(`🔄 Extra time opportunity check for ${team}:`, {
          regularOpportunities,
          extraOpportunities,
          totalAllowed: maxOpportunities,
          currentlyClosed: closedOpportunities,
          remainingAvailable: maxOpportunities - closedOpportunities
        });
      } else {
        // In regular time: just regular opportunities
        maxOpportunities = matchData.substituteOpportunitiesAllowance || 0;
        
        console.log(`⏰ Regular time opportunity check for ${team}:`, {
          regularOpportunities: maxOpportunities,
          currentlyClosed: closedOpportunities,
          remainingAvailable: maxOpportunities - closedOpportunities
        });
      }
      
      if (closedOpportunities >= maxOpportunities) {
        console.log(`❌ Team ${team} has used all substitute opportunities: ${closedOpportunities}/${maxOpportunities}`);
        return false;
      }
    }

    return true;
  };

  const getOrCreateActiveOpportunity = (team: 'home' | 'away') => {
    const teamOpportunities = substitutionOpportunities[team];
    let activeOpportunity = teamOpportunities.find(opp => opp.isActive && !opp.isClosed);
    
    if (!activeOpportunity) {
      activeOpportunity = {
        id: `${team}-opp-${Date.now()}`,
        isActive: true,
        isClosed: false,
        substitutionsInOpportunity: 0,
      };
      
      setSubstitutionOpportunities(prev => ({
        ...prev,
        [team]: [...prev[team], activeOpportunity!]
      }));
    }
    
    return activeOpportunity;
  };

  const handleSubstitutionPlayerOutSelect = (playerNumber: number) => {
    setSubstitutionFlow(prev => ({
      ...prev,
      selectedPlayerOut: playerNumber,
      step: 'player-in',
    }));
  };

  const handleSubstitutionPlayerInSelect = (playerNumber: number) => {
    setSubstitutionFlow(prev => ({
      ...prev,
      selectedPlayerIn: playerNumber,
      step: 'display',
    }));
  };

  const handleSubstitutionConfirm = async () => {
    if (!substitutionFlow.selectedPlayerOut || !substitutionFlow.selectedPlayerIn || !substitutionFlow.team) {
      return;
    }

    const teamData = substitutionFlow.team === 'home' ? matchData?.homeTeam : matchData?.awayTeam;
    const playerOut = teamData?.squad?.players?.find((p: any) => p.number === substitutionFlow.selectedPlayerOut);
    const playerIn = teamData?.squad?.players?.find((p: any) => p.number === substitutionFlow.selectedPlayerIn);

    const newSubstitution = {
      id: `sub-${Date.now()}-${Math.random()}`,
      team: substitutionFlow.team,
      playerOutNumber: substitutionFlow.selectedPlayerOut,
      playerOutName: playerOut?.name || `Player ${substitutionFlow.selectedPlayerOut}`,
      playerInNumber: substitutionFlow.selectedPlayerIn,
      playerInName: playerIn?.name || `Player ${substitutionFlow.selectedPlayerIn}`,
      minute: Math.floor(mainSeconds / 60),
      ...(substitutionFlow.currentOpportunityId && { opportunityId: substitutionFlow.currentOpportunityId }),
    };

    setMatchSubstitutions(prev => [...prev, newSubstitution]);

    // Log substitution event with appropriate time format (penalty round or match time)
    const substitutionLogData: any = {
      team: newSubstitution.team,
      playerOutNumber: newSubstitution.playerOutNumber,
      playerOutName: newSubstitution.playerOutName,
      playerInNumber: newSubstitution.playerInNumber,
      playerInName: newSubstitution.playerInName,
      minute: newSubstitution.minute,
      time: getFormattedMatchTime(),
      opportunityId: newSubstitution.opportunityId
    };
    
    // Add penalty round info if during penalties
    if (matchPhase === 'penalties') {
      substitutionLogData.penaltyRound = penaltyRound;
    }
    
    addMatchLogEvent('substitution', substitutionLogData);

    // Add injury time for substitutions (if enabled)
    addInjuryTime('substitution');

    // Update squad positions: swap player positions
    setMatchData((prevData: any) => {
      if (!prevData) return prevData;
      
      const updatedData = { ...prevData };
      const targetTeam = substitutionFlow.team === 'home' ? 'homeTeam' : 'awayTeam';
      
      if (updatedData[targetTeam]?.squad?.players) {
        const updatedPlayers = updatedData[targetTeam].squad.players.map((player: any) => {
          if (player.number === substitutionFlow.selectedPlayerOut) {
            // Player going out: change from STARTER to SUBSTITUTE
            return { ...player, position: 'SUBSTITUTE' };
          } else if (player.number === substitutionFlow.selectedPlayerIn) {
            // Player coming in: change from SUBSTITUTE to STARTER
            return { ...player, position: 'STARTER' };
          }
          return player;
        });
        
        updatedData[targetTeam] = {
          ...updatedData[targetTeam],
          squad: {
            ...updatedData[targetTeam].squad,
            players: updatedPlayers
          }
        };
      }
      
      return updatedData;
    });

    // Update the squad data in the database to sync with SquadSheetScreen
    try {
      const targetTeam = substitutionFlow.team;
      const teamData = substitutionFlow.team === 'home' ? matchData?.homeTeam : matchData?.awayTeam;
      
      if (teamData?.squad && matchId) {
        // Get the updated players with new positions
        const updatedPlayers = teamData.squad.players?.map((player: any) => {
          if (player.number === substitutionFlow.selectedPlayerOut) {
            return { ...player, position: 'SUBSTITUTE' };
          } else if (player.number === substitutionFlow.selectedPlayerIn) {
            return { ...player, position: 'STARTER' };
          }
          return player;
        }) || [];

        // Convert to SquadService format and save
        const starters = updatedPlayers.filter((p: any) => p.position === 'STARTER');
        const substitutes = updatedPlayers.filter((p: any) => p.position === 'SUBSTITUTE');
        const officials = teamData.squad.officials || [];
        
        const squadData = SquadService.convertToApiFormat(
          matchId,
          targetTeam,
          starters,
          substitutes,
          officials,
          teamData.squad.id
        );

        // Update the squad in the database
        await SquadService.saveSquad(squadData);
        console.log('✅ Squad positions updated in database for substitution');
      }
    } catch (error) {
      console.error('❌ Failed to update squad positions in database:', error);
      // Don't prevent the substitution from completing if database update fails
    }

    // Update opportunity count
    if (substitutionFlow.currentOpportunityId) {
      setSubstitutionOpportunities(prev => ({
        ...prev,
        [substitutionFlow.team!]: prev[substitutionFlow.team!].map(opp =>
          opp.id === substitutionFlow.currentOpportunityId
            ? { ...opp, substitutionsInOpportunity: opp.substitutionsInOpportunity + 1 }
            : opp
        )
      }));
    }

    console.log('Substitution confirmed:', newSubstitution);

    // Check if substitute opportunities are enabled
    if (matchData?.substituteOpportunities) {
      setSubstitutionFlow(prev => ({
        ...prev,
        step: 'opportunity-check',
      }));
    } else {
      resetSubstitutionFlow();
    }
  };

  const handleOpportunityEnd = (shouldEnd: boolean) => {
    if (shouldEnd && substitutionFlow.currentOpportunityId) {
      // Close the current opportunity
      setSubstitutionOpportunities(prev => ({
        ...prev,
        [substitutionFlow.team!]: prev[substitutionFlow.team!].map(opp =>
          opp.id === substitutionFlow.currentOpportunityId
            ? { ...opp, isActive: false, isClosed: true }
            : opp
        )
      }));
    }
    
    resetSubstitutionFlow();
  };

  const resetSubstitutionFlow = () => {
    setSubstitutionFlow({
      isActive: false,
      step: 'player-out',
      team: null,
      selectedPlayerOut: null,
      selectedPlayerIn: null,
      currentOpportunityId: null,
    });
  };

  const renderPreMatchContent = () => {
    // Handle goal flow (same as in-match)
    if (goalFlow.isActive) {
      switch (goalFlow.step) {
        case 'player':
          return renderGoalPlayerSelection();
        case 'display':
          return renderGoalDisplay();
        default:
          return renderPreMatchCenterContent();
      }
    }

    // Handle card flow (same as in-match)
    if (cardFlow.isActive) {
      switch (cardFlow.step) {
        case 'person':
          return renderPersonTypeSelection();
        case 'player':
          return renderPlayerSelection();
        case 'code':
          return renderCodeSelection();
        case 'subcode':
          return renderSubCodeSelection();
        case 'confirm':
          return renderCardConfirmation();
        default:
          return renderPreMatchCenterContent();
      }
    }

    // Handle substitution flow (special pre-match logic)
    if (substitutionFlow.isActive) {
      switch (substitutionFlow.step) {
        case 'player-out':
          return renderSubstitutionPlayerOutSelection();
        case 'player-in':
          return renderPreMatchSubstitutionPlayerInSelection(); // Special pre-match version
        case 'display':
          return renderSubstitutionDisplay();
        default:
          return renderPreMatchCenterContent();
      }
    }

    // Handle view switching for action buttons
    if (currentView === 'home') {
      return renderPreMatchActionButtons('home');
    } else if (currentView === 'away') {
      return renderPreMatchActionButtons('away');
    } else {
      return renderPreMatchCenterContent();
    }
  };

  const renderPreMatchCenterContent = () => {
    return (
      <>
        <Text style={styles.instructionText}>Select the team that will kick off first</Text>
        {renderMatchCard()}

        <TouchableOpacity
          style={[
            styles.startButton,
            {
              backgroundColor: selectedTeam ? theme.primary : theme.textSecondary,
              opacity: selectedTeam ? 1 : 0.5,
            },
          ]}
          onPress={handleStartMatch}
          disabled={!selectedTeam}
        >
          <Text style={styles.startButtonText}>
            {selectedTeam ? `Start Period 1 - ${selectedTeam === 'home' ? 'Home' : 'Away'} Kickoff` : 'Select Kickoff Team First'}
          </Text>
        </TouchableOpacity>

        {/* Swipe indicator for pre-match actions */}
        <View style={styles.swipeIndicator}>
          <Text style={styles.swipeIndicatorText}>
            ← {matchData?.homeTeam?.shortName || 'Home'}   • Swipe for Actions •   {matchData?.awayTeam?.shortName || 'Away'} →
          </Text>
        </View>
      </>
    );
  };

  const renderPreMatchActionButtons = (team: 'home' | 'away') => {
    const teamName = team === 'home'
      ? (matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home')
      : (matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away');

    return (
      <View style={styles.actionButtonsContainer}>
        <Text style={styles.teamActionTitle}>{teamName} - Pre-Match Actions</Text>
        
        <View style={styles.actionButtonsGrid}>
          <TouchableOpacity
            style={[styles.actionButton, styles.yellowCardButton]}
            onPress={() => handleActionPress('yellow', team)}
          >
            <View style={styles.yellowCard} />
            <Text style={styles.actionButtonText}>Yellow</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.redCardButton]}
            onPress={() => handleActionPress('red', team)}
          >
            <View style={styles.redCard} />
            <Text style={styles.actionButtonText}>Red</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.subButton]}
            onPress={() => handleActionPress('substitution', team)}
          >
            <SubstitutionIcon size={40} />
            <Text style={styles.actionButtonText}>Sub</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.goalButton]}
            onPress={() => handleActionPress('goal', team)}
          >
            <SoccerBallIcon color={theme.text} size={32} />
            <Text style={styles.actionButtonText}>Goal</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderPreMatchSubstitutionPlayerInSelection = () => {
    const teamName = substitutionFlow.team === 'home'
      ? (matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home')
      : (matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away');

    // Get substitute players for selection
    const teamData = substitutionFlow.team === 'home' ? matchData?.homeTeam : matchData?.awayTeam;
    let playerNumbers = [];

    if (teamData?.squad?.players) {
      // Get only substitute players (position === 'SUBSTITUTE')
      playerNumbers = teamData.squad.players
        ?.filter((p: any) => p.position === 'SUBSTITUTE')
        ?.map((p: any) => p.number) || [];
    }

    return (
      <View style={styles.cardFlowContainer}>
        <Text style={styles.cardFlowTitle}>Pre-Match Substitution - {teamName}</Text>
        <Text style={styles.cardFlowSubtitle}>
          Red-carded player #{substitutionFlow.selectedPlayerOut} must be replaced
        </Text>
        <Text style={styles.cardFlowSubtitle}>Select Substitute Player</Text>

        <View style={styles.playerGrid}>
          {playerNumbers.map((number: any) => {
            const isRedCarded = isPlayerRedCarded(substitutionFlow.team!, number);
            
            return (
              <TouchableOpacity
                key={number}
                style={[
                  styles.playerButton,
                  substitutionFlow.selectedPlayerIn === number && styles.playerButtonSelected,
                  isRedCarded && styles.playerButtonRedCarded
                ]}
                onPress={() => !isRedCarded && handleSubstitutionPlayerInSelect(number)}
                disabled={isRedCarded}
              >
                <Text style={[
                  styles.playerButtonText,
                  substitutionFlow.selectedPlayerIn === number && styles.playerButtonTextSelected,
                  isRedCarded && styles.playerButtonTextRedCarded
                ]}>
                  {number}
                </Text>
                {isRedCarded && (
                  <View style={styles.redCardIndicator} />
                )}
              </TouchableOpacity>
            );
          })}

          <TouchableOpacity
            style={[
              styles.playerButton,
              styles.okButton,
              !substitutionFlow.selectedPlayerIn && styles.okButtonDisabled
            ]}
            onPress={handlePreMatchSubstitutionConfirm}
            disabled={!substitutionFlow.selectedPlayerIn}
          >
            <Text style={[
              styles.playerButtonText,
              styles.okButtonText,
              !substitutionFlow.selectedPlayerIn && styles.okButtonTextDisabled
            ]}>
              OK
            </Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={styles.cardFlowBackButton}
          onPress={resetSubstitutionFlow}
        >
          <Text style={styles.cardFlowBackText}>Cancel</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const handlePreMatchSubstitutionConfirm = async () => {
    if (!substitutionFlow.selectedPlayerIn || !substitutionFlow.selectedPlayerOut || !substitutionFlow.team) return;

    const currentMinute = 0; // Pre-match substitutions show 0 minutes
    
    // Get player names from squad data
    const teamData = substitutionFlow.team === 'home' ? matchData?.homeTeam : matchData?.awayTeam;
    const playerOut = teamData?.squad?.players?.find((p: any) => p.number === substitutionFlow.selectedPlayerOut);
    const playerIn = teamData?.squad?.players?.find((p: any) => p.number === substitutionFlow.selectedPlayerIn);
    const playerOutName = playerOut?.name || `Player ${substitutionFlow.selectedPlayerOut}`;
    const playerInName = playerIn?.name || `Player ${substitutionFlow.selectedPlayerIn}`;

    // Record the substitution (counts towards max subs but no opportunity tracking)
    const newSubstitution = {
      id: `${Date.now()}-${Math.random()}`,
      team: substitutionFlow.team,
      playerOutNumber: substitutionFlow.selectedPlayerOut,
      playerOutName,
      playerInNumber: substitutionFlow.selectedPlayerIn,
      playerInName,
      minute: currentMinute,
      // No opportunityId for pre-match substitutions
    };

    setMatchSubstitutions(prev => [...prev, newSubstitution]);
    
    // Log pre-match substitution event
    addMatchLogEvent('substitution', {
      team: newSubstitution.team,
      playerOutNumber: newSubstitution.playerOutNumber,
      playerOutName: newSubstitution.playerOutName,
      playerInNumber: newSubstitution.playerInNumber,
      playerInName: newSubstitution.playerInName,
      minute: newSubstitution.minute,
      time: "0'",
      isPreMatch: true
    });
    
    console.log('✅ Pre-match substitution recorded (counts towards max subs):', newSubstitution);

    // Update match data to move substitute to starter and red-carded player to substitute
    setMatchData((prevData: any) => {
      if (!prevData) return prevData;
      
      const updatedData = { ...prevData };
      const targetTeam = substitutionFlow.team === 'home' ? 'homeTeam' : 'awayTeam';
      
      if (updatedData[targetTeam]?.squad?.players) {
        const updatedPlayers = updatedData[targetTeam].squad.players.map((player: any) => {
          if (player.number === substitutionFlow.selectedPlayerIn) {
            // Substitute becomes starter
            return { ...player, position: 'STARTER' };
          }
          if (player.number === substitutionFlow.selectedPlayerOut) {
            // Red-carded player remains substitute
            return { ...player, position: 'SUBSTITUTE' };
          }
          return player;
        });
        
        updatedData[targetTeam] = {
          ...updatedData[targetTeam],
          squad: {
            ...updatedData[targetTeam].squad,
            players: updatedPlayers
          }
        };
      }
      
      return updatedData;
    });

    // Save to database
    try {
      const targetTeam = substitutionFlow.team;
      const teamData = substitutionFlow.team === 'home' ? matchData?.homeTeam : matchData?.awayTeam;
      
      if (teamData?.squad && matchId) {
        const updatedPlayers = teamData.squad.players?.map((player: any) => {
          if (player.number === substitutionFlow.selectedPlayerIn) {
            return { ...player, position: 'STARTER' };
          }
          if (player.number === substitutionFlow.selectedPlayerOut) {
            return { ...player, position: 'SUBSTITUTE' };
          }
          return player;
        }) || [];

        const starters = updatedPlayers.filter((p: any) => p.position === 'STARTER');
        const substitutes = updatedPlayers.filter((p: any) => p.position === 'SUBSTITUTE');
        const officials = teamData.squad.officials || [];
        
        const squadData = SquadService.convertToApiFormat(
          matchId,
          targetTeam,
          starters,
          substitutes,
          officials
        );
        
        await SquadService.saveSquad(squadData);
        console.log(`📋 Pre-match substitution: Squad data updated for ${targetTeam} team`);
      }
    } catch (error) {
      console.error('Error updating squad data after pre-match substitution:', error);
    }

    console.log(`🔄 Pre-match substitution: Player ${substitutionFlow.selectedPlayerOut} → ${substitutionFlow.selectedPlayerIn}`);
    
    resetSubstitutionFlow();
    setCurrentView('center'); // Return to center view
  };

  const renderHalfTimeContent = () => {
    const isETHalfTime = matchPhase === 'et-halftime';
    let nextPeriodText;

    if (isETHalfTime) {
      if (justFinishedETPhase === 'ET1') {
        nextPeriodText = 'ET Period 2';
      } else {
        nextPeriodText = 'ET Period 1';
      }
    } else {
      nextPeriodText = `Period ${currentPeriod + 1}`;
    }

    const halfTimeLength = matchData?.halfTime || 15;
    const remainingHalfTimeSeconds = Math.max(0, (halfTimeLength * 60) - halfTimeSeconds);

    return (
      <>
        <TouchableOpacity style={styles.syncButton}>
          <Text style={styles.syncButtonText}>Sync Status</Text>
        </TouchableOpacity>

        {renderMatchCard()}

        <View style={styles.timerContainer}>
          <Text style={styles.matchPeriodText}>
            {isETHalfTime ? 'Extra Time Half-Time' : 'Half-Time'} ({halfTimeLength} min)
          </Text>
          <View style={styles.mainTimerBox}>
            <Text style={styles.mainTimerText}>{formatTime(halfTimeSeconds)}</Text>
          </View>
          <Text style={styles.remainingTimeText}>
            {formatTime(remainingHalfTimeSeconds)} remaining
          </Text>
        </View>

        <TouchableOpacity
          style={[
            styles.startButton,
            {
              backgroundColor: nextKickoffTeam ? theme.primary : theme.textSecondary,
              opacity: nextKickoffTeam ? 1 : 0.5,
            },
          ]}
          onPress={handleStartNextPeriod}
          disabled={!nextKickoffTeam}
        >
          <Text style={styles.startButtonText}>
            {nextKickoffTeam 
              ? `Start ${nextPeriodText} - ${nextKickoffTeam === 'home' ? 'Home' : 'Away'} Kickoff`
              : 'Determining Next Kickoff...'
            }
          </Text>
        </TouchableOpacity>
      </>
    );
  };

  const renderMatchCompletedContent = () => {
    const totalRegularPeriods = matchData?.numberOfPeriods || 2;
    const hasExtraTime = matchData?.extraTime === true || matchData?.extraTime === 'Yes';
    
    // Calculate final score
    const homeGoals = matchGoals.filter(goal => goal.team === 'home').length;
    const awayGoals = matchGoals.filter(goal => goal.team === 'away').length;
    
    // Determine winning team
    let winningTeam = '';
    let finalScore = `${homeGoals} - ${awayGoals}`;
    
    if (homeGoals > awayGoals) {
      winningTeam = matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home';
    } else if (awayGoals > homeGoals) {
      winningTeam = matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away';
    } else {
      winningTeam = 'Draw';
      finalScore = `${homeGoals} - ${awayGoals} (Draw)`;
    }

    let completionText = `${totalRegularPeriods} periods played`;
    if (isExtraTime || currentETPhase) {
      completionText += ' + Extra Time';
    }

    return (
      <>
        <Text style={styles.instructionText}>Match Completed</Text>
        {renderMatchCard()}

        <View style={styles.timerContainer}>
          <Text style={styles.matchPeriodText}>Full Time</Text>
          <View style={styles.completedContainer}>
            <Text style={styles.completedText}>Match Ended</Text>
            <Text style={styles.completedSubtext}>
              {winningTeam !== 'Draw' ? `${winningTeam} won ${homeGoals}-${awayGoals}` : `Match ended ${homeGoals}-${awayGoals} (Draw)`}
            </Text>
            <Text style={styles.completedExtraText}>
              {completionText}
            </Text>
          </View>
        </View>

        {(showEndMatchButton || matchPhase === 'completed') && (
          <TouchableOpacity
            style={[styles.startButton, { backgroundColor: theme.primary }]}
            onPress={handleEndMatch}
          >
            <Text style={styles.startButtonText}>End Match</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={[styles.startButton, { backgroundColor: theme.textSecondary }]}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.startButtonText}>Return to Match Details</Text>
        </TouchableOpacity>
      </>
    );
  };

  const renderPersonTypeSelection = () => {
    const teamName = cardFlow.team === 'home'
      ? (matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home')
      : (matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away');

    return (
      <View style={styles.cardFlowContainer}>
        <Text style={styles.cardFlowTitle}>
          {cardFlow.cardType === 'yellow' ? 'Yellow Card' : 'Red Card'}
        </Text>
        <Text style={styles.cardFlowTeam}>{teamName}</Text>

        <View style={styles.personTypeGrid}>
          <TouchableOpacity
            style={styles.personTypeButton}
            onPress={() => handlePersonTypeSelect('player')}
          >
            <Text style={styles.personTypeText}>Player</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.personTypeButton}
            onPress={() => handlePersonTypeSelect('substitute')}
          >
            <Text style={styles.personTypeText}>Substitute</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.personTypeButton}
            onPress={() => handlePersonTypeSelect('official')}
          >
            <Text style={styles.personTypeText}>Team Official</Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity style={styles.cardFlowBackButton} onPress={resetCardFlow}>
          <Text style={styles.cardFlowBackText}>Cancel</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderPlayerSelection = () => {
    const teamName = cardFlow.team === 'home'
      ? (matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home')
      : (matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away');

    // Handle team officials selection
    if (cardFlow.personType === 'official') {
      const teamData = cardFlow.team === 'home' ? matchData?.homeTeam : matchData?.awayTeam;
      const teamOfficials = teamData?.squad?.officials || [];

      return (
        <View style={styles.cardFlowContainer}>
          <Text style={styles.cardFlowSubtitle}>Team Officials</Text>
          <Text style={styles.cardFlowTeam}>{teamName}</Text>

          <ScrollView style={styles.codeList} showsVerticalScrollIndicator={false}>
            {teamOfficials.length > 0 ? (
              teamOfficials.map((official: any, index: number) => (
                <TouchableOpacity
                  key={official.id || index}
                  style={[
                    styles.codeButton,
                    cardFlow.selectedPlayer === index && styles.codeButtonSelected
                  ]}
                  onPress={() => handleOfficialSelect(index, official)}
                >
                  <Text style={styles.codeButtonText}>
                    {official.role}
                  </Text>
                  <Text style={styles.codeButtonSubtext}>
                    {official.name}
                  </Text>
                </TouchableOpacity>
              ))
            ) : (
              <View style={styles.emptyCodesContainer}>
                <Text style={styles.emptyCodesText}>
                  No team officials available
                </Text>
              </View>
            )}
          </ScrollView>

          <TouchableOpacity
            style={styles.cardFlowBackButton}
            onPress={() => setCardFlow(prev => ({ ...prev, step: 'person' }))}
          >
            <Text style={styles.cardFlowBackText}>Back</Text>
          </TouchableOpacity>
        </View>
      );
    }

    // Handle player/substitute selection
    const isSubstitute = cardFlow.personType === 'substitute';
    const title = isSubstitute ? 'Substitute' : 'Starting Line Up';

    // Get real player numbers from squad data
    const teamData = cardFlow.team === 'home' ? matchData?.homeTeam : matchData?.awayTeam;
    let playerNumbers = [];

    if (teamData?.squad?.players) {
      // Use SquadService data structure
      if (isSubstitute) {
        // Get substitute players (position === 'SUBSTITUTE')
        playerNumbers = teamData.squad.players
          ?.filter((p: any) => p.position === 'SUBSTITUTE')
          ?.map((p: any) => p.number) || [];
      } else {
        // Get starting lineup players (position === 'STARTER')
        playerNumbers = teamData.squad.players
          ?.filter((p: any) => p.position === 'STARTER')
          ?.map((p: any) => p.number) || [];
      }
    }

    // Fallback to default numbers if no squad data
    if (playerNumbers.length === 0) {
      console.log('⚠️ No squad data found, using fallback numbers');
      playerNumbers = isSubstitute
        ? Array.from({ length: 11 }, (_, i) => i + 12)
        : Array.from({ length: 11 }, (_, i) => i + 1);
    }

    return (
      <View style={styles.cardFlowContainer}>
        <Text style={styles.cardFlowSubtitle}>{title}</Text>
        <Text style={styles.cardFlowTeam}>{teamName}</Text>

        <View style={styles.playerGrid}>
          {playerNumbers.map((number: any) => {
            const hasYellow = checkForExistingYellow(cardFlow.team!, number);
            const willBeSecondYellow = cardFlow.cardType === 'yellow' && hasYellow;
            const isRedCarded = isPlayerRedCarded(cardFlow.team!, number);

            return (
              <TouchableOpacity
                key={number}
                style={[
                  styles.playerButton,
                  cardFlow.selectedPlayer === number && styles.playerButtonSelected,
                  hasYellow && styles.playerButtonWithYellow,
                  willBeSecondYellow && styles.playerButtonSecondYellow,
                  isRedCarded && styles.playerButtonRedCarded
                ]}
                onPress={() => !isRedCarded && handlePlayerSelect(number)}
                disabled={isRedCarded}
              >
                <Text style={[
                  styles.playerButtonText,
                  cardFlow.selectedPlayer === number && styles.playerButtonTextSelected,
                  isRedCarded && styles.playerButtonTextRedCarded
                ]}>
                  {number}
                </Text>
                {hasYellow && !isRedCarded && (
                  <View style={styles.yellowCardIndicator} />
                )}
                {isRedCarded && (
                  <View style={styles.redCardIndicator} />
                )}
                {willBeSecondYellow && cardFlow.selectedPlayer === number && (
                  <Text style={styles.secondYellowWarning}>2nd Yellow = Red</Text>
                )}
              </TouchableOpacity>
            );
          })}

          <TouchableOpacity
            style={[
              styles.playerButton,
              styles.okButton,
              !cardFlow.selectedPlayer && styles.okButtonDisabled
            ]}
            onPress={handlePlayerConfirm}
            disabled={!cardFlow.selectedPlayer}
          >
            <Text style={[
              styles.playerButtonText,
              styles.okButtonText,
              !cardFlow.selectedPlayer && styles.okButtonTextDisabled
            ]}>
              OK
            </Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={styles.cardFlowBackButton}
          onPress={() => setCardFlow(prev => ({ ...prev, step: 'person' }))}
        >
          <Text style={styles.cardFlowBackText}>Back</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderCodeSelection = () => {
    const teamName = cardFlow.team === 'home'
      ? (matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home')
      : (matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away');

    // Get misconduct codes from the match's misconduct set
    let misconductCodes = [];
    if (activeMisconductSet) {
      if (cardFlow.personType === 'official') {
        // Team officials codes - try different property names
        misconductCodes = cardFlow.cardType === 'yellow'
          ? (activeMisconductSet.officialsYellowCodes || activeMisconductSet.teamOfficialsYellowCodes || [])
          : (activeMisconductSet.officialsRedCodes || activeMisconductSet.teamOfficialsRedCodes || []);
      } else {
        // Player codes (both players and substitutes use same codes) - try different property names
        misconductCodes = cardFlow.cardType === 'yellow'
          ? (activeMisconductSet.playersYellowCodes || activeMisconductSet.playerYellowCodes || [])
          : (activeMisconductSet.playersRedCodes || activeMisconductSet.playerRedCodes || []);
      }
    }

    return (
      <View style={styles.cardFlowContainer}>
        <Text style={styles.cardFlowTitle}>Select Misconduct Code</Text>
        <Text style={styles.cardFlowTeam}>{teamName}</Text>

        <ScrollView style={styles.codeList} showsVerticalScrollIndicator={false}>
          {misconductCodes.length > 0 ? (
            misconductCodes.map((item: any) => (
              <View key={item.id}>
                <TouchableOpacity
                  style={styles.codeButton}
                  onPress={() => handleCodeSelect(item)}
                >
                  <Text style={styles.codeButtonText}>
                    {item.code} - {item.description}
                  </Text>
                  {item.subcategories && item.subcategories.length > 0 && (
                    <Text style={styles.codeSubcategoryHint}>
                      {item.subcategories.length} subcategories →
                    </Text>
                  )}
                </TouchableOpacity>
              </View>
            ))
          ) : (
            <View style={styles.emptyCodesContainer}>
              <Text style={styles.emptyCodesText}>
                No {cardFlow.cardType} card codes available for {cardFlow.personType === 'official' ? 'team officials' : 'players'}
              </Text>
            </View>
          )}
        </ScrollView>

        <TouchableOpacity
          style={styles.cardFlowBackButton}
          onPress={() => setCardFlow(prev => ({
            ...prev,
            step: 'player'  // Always go back to player selection (which handles officials too)
          }))}
        >
          <Text style={styles.cardFlowBackText}>Back</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderSubCodeSelection = () => {
    const teamName = cardFlow.team === 'home'
      ? (matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home')
      : (matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away');

    const subcategories = cardFlow.selectedMainCode?.subcategories || [];

    return (
      <View style={styles.cardFlowContainer}>
        <Text style={styles.cardFlowTitle}>Select Subcategory</Text>
        <Text style={styles.cardFlowTeam}>{teamName}</Text>
        <Text style={styles.selectedMainCode}>
          {cardFlow.selectedCode} - {cardFlow.selectedMainCode?.description}
        </Text>

        <ScrollView style={styles.codeList} showsVerticalScrollIndicator={false}>
          {subcategories.map((subItem: any) => (
            <TouchableOpacity
              key={subItem.id}
              style={styles.codeButton}
              onPress={() => handleSubCodeSelect(subItem.code)}
            >
              <Text style={styles.codeButtonText}>
                {subItem.code} - {subItem.description}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        <TouchableOpacity
          style={styles.cardFlowBackButton}
          onPress={() => setCardFlow(prev => ({ ...prev, step: 'code' }))}
        >
          <Text style={styles.cardFlowBackText}>Back</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderCardConfirmation = () => {
    const teamName = cardFlow.team === 'home'
      ? (matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home')
      : (matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away');

    const currentTime = formatTime(mainSeconds);

    // Get person name based on type
    let personDisplayName = '';
    if (cardFlow.personType === 'official' && cardFlow.selectedOfficial) {
      personDisplayName = `${cardFlow.selectedOfficial.role} - ${cardFlow.selectedOfficial.name}`;
    } else if (cardFlow.selectedPlayer) {
      // Get real player name from squad data
      let playerName = `Player ${cardFlow.selectedPlayer}`;
      const teamData = cardFlow.team === 'home' ? matchData?.homeTeam : matchData?.awayTeam;
      const player = teamData?.squad?.players?.find((p: any) => p.number === cardFlow.selectedPlayer);
      if (player) {
        playerName = player.name;
      }
      personDisplayName = `${cardFlow.selectedPlayer}. ${playerName}`;
    }

    return (
      <View style={styles.cardFlowContainer}>
        <Text style={styles.confirmationTeam}>{teamName}</Text>

        {cardFlow.isSecondYellow && cardFlow.originalYellowCard ? (
          // Show both yellow cards when it's a second yellow
          <View style={styles.doubleCardContainer}>
            <View style={styles.cardDisplay}>
              <View style={styles.yellowCardDisplay}>
                <Text style={styles.cardCode}>
                  {cardFlow.originalYellowCard.code}
                  {cardFlow.originalYellowCard.subCode && (
                    <Text style={styles.cardSubCode}>{'\n'}{cardFlow.originalYellowCard.subCode}</Text>
                  )}
                </Text>
              </View>
              <Text style={styles.cardTime}>{cardFlow.originalYellowCard.minute}' MINS</Text>
            </View>

            <View style={styles.cardDisplay}>
              <View style={styles.yellowCardDisplay}>
                <Text style={styles.cardCode}>
                  {cardFlow.selectedCode}
                  {cardFlow.selectedSubCode && (
                    <Text style={styles.cardSubCode}>{'\n'}{cardFlow.selectedSubCode}</Text>
                  )}
                </Text>
              </View>
              <Text style={styles.cardTime}>{formatTime(mainSeconds)} MINS</Text>
            </View>

            <View style={styles.redCardDisplay}>
              <Text style={styles.cardCode}>2nd Yellow</Text>
            </View>
          </View>
        ) : (
          // Show single card
          <View style={[
            styles.cardDisplay,
            cardFlow.cardType === 'yellow' ? styles.yellowCardDisplay : styles.redCardDisplay
          ]}>
            <Text style={styles.cardCode}>
              {cardFlow.selectedCode}
              {cardFlow.selectedSubCode && (
                <Text style={styles.cardSubCode}>{'\n'}{cardFlow.selectedSubCode}</Text>
              )}
            </Text>
          </View>
        )}

        {!cardFlow.isSecondYellow && (
          <Text style={styles.cardTime}>{currentTime} MINS</Text>
        )}

        {(cardFlow.selectedPlayer || cardFlow.selectedOfficial) && (
          <Text style={styles.cardPlayer}>
            {personDisplayName}
          </Text>
        )}

        <TouchableOpacity style={styles.confirmButton} onPress={handleCardConfirm}>
          <Text style={styles.confirmButtonText}>
            {cardFlow.isSecondYellow ? 'Confirm Second Yellow (Red Card)' : 'Confirm Card'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.cardFlowBackButton}
          onPress={() => setCardFlow(prev => ({ ...prev, step: 'code' }))}
        >
          <Text style={styles.cardFlowBackText}>Back</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderActionButtons = (team: 'home' | 'away') => {
    const teamName = team === 'home'
      ? (matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home')
      : (matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away');

    return (
      <View style={styles.actionButtonsContainer}>
        <Text style={styles.teamActionTitle}>{teamName}</Text>
        <View style={styles.actionButtonsGrid}>
          <TouchableOpacity
            style={[styles.actionButton, styles.yellowCardButton]}
            onPress={() => handleActionPress('yellow', team)}
          >
            <View style={styles.yellowCard} />
            <Text style={styles.actionButtonText}>Yellow</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.redCardButton]}
            onPress={() => handleActionPress('red', team)}
          >
            <View style={styles.redCard} />
            <Text style={styles.actionButtonText}>Red</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.subButton]}
            onPress={() => handleActionPress('substitution', team)}
          >
            <SubstitutionIcon size={40} />
            <Text style={styles.actionButtonText}>Sub</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.goalButton]}
            onPress={() => handleActionPress('goal', team)}
          >
            <SoccerBallIcon color={theme.text} size={32} />
            <Text style={styles.actionButtonText}>Goal</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // Substitution rendering functions
  const renderSubstitutionPlayerOutSelection = () => {
    const teamName = substitutionFlow.team === 'home'
      ? (matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home')
      : (matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away');

    const teamData = substitutionFlow.team === 'home' ? matchData?.homeTeam : matchData?.awayTeam;
    
    // During penalties, show all STARTER players (player-to-player substitution only)
    if (matchPhase === 'penalties') {
      const starterPlayers = teamData?.squad?.players?.filter((p: any) => p.position === 'STARTER') || [];
      let playerNumbers = starterPlayers.map((p: any) => p.number);
      
      return (
        <View style={styles.cardFlowContainer}>
          <Text style={styles.cardFlowTitle}>Goalkeeper Substitution - {teamName}</Text>
          <Text style={styles.cardFlowSubtitle}>Select player to substitute OUT (Red)</Text>

          <View style={styles.playerGrid}>
            {playerNumbers.map((number: any) => {
              const isSelected = substitutionFlow.selectedPlayerOut === number;
              
              return (
                <TouchableOpacity
                  key={number}
                  style={[
                    styles.playerButton,
                    isSelected && { backgroundColor: '#FF4444' } // Red for selected out
                  ]}
                  onPress={() => handleSubstitutionPlayerOutSelect(number)}
                >
                  <Text style={[
                    styles.playerButtonText,
                    isSelected && { color: 'white' }
                  ]}>
                    {number}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>

          <TouchableOpacity style={styles.cardFlowBackButton} onPress={resetSubstitutionFlow}>
            <Text style={styles.cardFlowBackText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      );
    }
    
    // Normal substitution logic for regular play
    // Debug current squad positions
    const allPlayers = teamData?.squad?.players || [];
    const starterPlayers = allPlayers.filter((p: any) => p.position === 'STARTER');
    const substitutePlayers = allPlayers.filter((p: any) => p.position === 'SUBSTITUTE');
    
    // Get starting lineup players (those who can be substituted out)
    let playerNumbers = starterPlayers.map((p: any) => p.number);
    
    console.log('👥 Squad Positions Debug:', {
      team: substitutionFlow.team,
      allPlayers: allPlayers.map((p: any) => `${p.number}(${p.position})`),
      starters: starterPlayers.map((p: any) => p.number),
      substitutes: substitutePlayers.map((p: any) => p.number),
      availableToSubOut: playerNumbers
    });
    
    // Get previously substituted players for this team
    const previouslySubstitutedPlayers = matchSubstitutions
      .filter(sub => sub.team === substitutionFlow.team)
      .map(sub => sub.playerOutNumber);

    // Debug logging for rollOnRollOff
    console.log('🔍 RollOnRollOff Debug:', {
      matchDataRollOnRollOff: matchData?.rollOnRollOff,
      matchDataType: typeof matchData?.rollOnRollOff,
      matchSubstitutions,
      previouslySubstitutedPlayers,
      teamType: substitutionFlow.team,
      availableStarterNumbers: playerNumbers
    });

    // Show clear test scenario
    if (matchSubstitutions.length > 0) {
      console.log('🧪 TEST SCENARIO:', {
        rollOnRollOff: matchData?.rollOnRollOff,
        message: matchData?.rollOnRollOff ? 
          `Players ${previouslySubstitutedPlayers.join(', ')} should be DISABLED (greyed out)` :
          'All players should be ENABLED (rollOnRollOff is OFF)',
        currentStartersAvailable: playerNumbers
      });
    }

    return (
      <View style={styles.cardFlowContainer}>
        <Text style={styles.cardFlowTitle}>Substitution - {teamName}</Text>
        <Text style={styles.cardFlowSubtitle}>Select player to be substituted OUT</Text>

        <View style={styles.playerGrid}>
          {playerNumbers.map((number: any) => {
            // Check if this player was previously substituted out
            const wasSubstitutedOut = previouslySubstitutedPlayers.includes(number);
            // If rollOnRollOff is enabled (true), disable previously substituted players
            // Handle different possible values: true, "true", "Yes", "yes" etc.
            const rollOnRollOffEnabled = matchData?.rollOnRollOff === true || 
                                        matchData?.rollOnRollOff === 'true' || 
                                        matchData?.rollOnRollOff === 'Yes' || 
                                        matchData?.rollOnRollOff === 'yes';
            const isDisabledByRollOff = !rollOnRollOffEnabled && wasSubstitutedOut;
            const isRedCarded = isPlayerRedCarded(substitutionFlow.team!, number);
            const isDisabled = isDisabledByRollOff || isRedCarded;
            
            // Only log for disabled players to reduce noise
            if (isDisabled) {
              console.log(`🚫 Player ${number} DISABLED:`, {
                rollOnRollOff: matchData?.rollOnRollOff,
                rollOnRollOffEnabled,
                wasSubstitutedOut,
                isRedCarded,
                previouslySubstitutedPlayers
              });
            } else if (previouslySubstitutedPlayers.includes(number)) {
              console.log(`⚠️ Player ${number} should be disabled but isn't:`, {
                rollOnRollOff: matchData?.rollOnRollOff,
                rollOnRollOffEnabled,
                wasSubstitutedOut,
                isRedCarded,
                previouslySubstitutedPlayers,
                expectedDisabled: true,
                actualDisabled: isDisabled
              });
            }
            
            return (
              <TouchableOpacity
                key={number}
                style={[
                  styles.playerButton,
                  substitutionFlow.selectedPlayerOut === number && styles.playerButtonSelected,
                  isDisabled && styles.playerButtonDisabled,
                  isRedCarded && styles.playerButtonRedCarded
                ]}
                onPress={isDisabled ? undefined : () => handleSubstitutionPlayerOutSelect(number)}
                disabled={isDisabled}
              >
                <Text style={[
                  styles.playerButtonText,
                  substitutionFlow.selectedPlayerOut === number && styles.playerButtonTextSelected,
                  isDisabled && styles.playerButtonTextDisabled,
                  isRedCarded && styles.playerButtonTextRedCarded
                ]}>
                  {number}
                </Text>
                {isRedCarded && (
                  <View style={styles.redCardIndicator} />
                )}
              </TouchableOpacity>
            );
          })}
        </View>

        <TouchableOpacity style={styles.cardFlowBackButton} onPress={resetSubstitutionFlow}>
          <Text style={styles.cardFlowBackText}>Cancel</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderSubstitutionPlayerInSelection = () => {
    const teamName = substitutionFlow.team === 'home'
      ? (matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home')
      : (matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away');

    const teamData = substitutionFlow.team === 'home' ? matchData?.homeTeam : matchData?.awayTeam;
    
    // During penalties, show all STARTER players (player-to-player substitution only)
    if (matchPhase === 'penalties') {
      const starterPlayers = teamData?.squad?.players?.filter((p: any) => p.position === 'STARTER') || [];
      let playerNumbers = starterPlayers.map((p: any) => p.number);
      
      return (
        <View style={styles.cardFlowContainer}>
          <Text style={styles.cardFlowTitle}>Goalkeeper Substitution - {teamName}</Text>
          <Text style={styles.cardFlowSubtitle}>Select player to substitute IN (Green)</Text>

          <View style={styles.playerGrid}>
            {playerNumbers.map((number: any) => {
              const isSelectedOut = substitutionFlow.selectedPlayerOut === number;
              const isSelectedIn = substitutionFlow.selectedPlayerIn === number;
              const isDisabled = isSelectedOut; // Can't select the same player for both
              
              return (
                <TouchableOpacity
                  key={number}
                  style={[
                    styles.playerButton,
                    isSelectedOut && { backgroundColor: '#FF4444' }, // Red for already selected out
                    isSelectedIn && { backgroundColor: '#44AA44' }, // Green for selected in
                    isDisabled && styles.playerButtonDisabled
                  ]}
                  onPress={isDisabled ? undefined : () => handleSubstitutionPlayerInSelect(number)}
                  disabled={isDisabled}
                >
                  <Text style={[
                    styles.playerButtonText,
                    (isSelectedOut || isSelectedIn) && { color: 'white' },
                    isDisabled && styles.playerButtonTextDisabled
                  ]}>
                    {number}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>

          <TouchableOpacity
            style={styles.cardFlowBackButton}
            onPress={() => setSubstitutionFlow(prev => ({ ...prev, step: 'player-out' }))}
          >
            <Text style={styles.cardFlowBackText}>Back</Text>
          </TouchableOpacity>
        </View>
      );
    }
    
    // Normal substitution logic for regular play
    // Get substitute players (those who can come in)
    let playerNumbers = teamData?.squad?.players
      ?.filter((p: any) => p.position === 'SUBSTITUTE')
      ?.map((p: any) => p.number) || [];
    
    // Get previously substituted players for this team (players who came IN)
    const previouslySubstitutedInPlayers = matchSubstitutions
      .filter(sub => sub.team === substitutionFlow.team)
      .map(sub => sub.playerInNumber);
    
    // IMPORTANT: Also get players who went OUT (they should be disabled if rollOnRollOff is true)
    const previouslySubstitutedOutPlayers = matchSubstitutions
      .filter(sub => sub.team === substitutionFlow.team)
      .map(sub => sub.playerOutNumber);

    return (
      <View style={styles.cardFlowContainer}>
        <Text style={styles.cardFlowTitle}>Substitution - {teamName}</Text>
        <Text style={styles.cardFlowSubtitle}>Select substitute player to come IN</Text>

        <View style={styles.playerGrid}>
          {playerNumbers.map((number: any) => {
            // Check if this player was previously substituted in OR out
            const wasSubstitutedIn = previouslySubstitutedInPlayers.includes(number);
            const wasSubstitutedOut = previouslySubstitutedOutPlayers.includes(number);
            // If rollOnRollOff is enabled (true), disable previously substituted players
            // Handle different possible values: true, "true", "Yes", "yes" etc.
            const rollOnRollOffEnabled = matchData?.rollOnRollOff === true || 
                                        matchData?.rollOnRollOff === 'true' || 
                                        matchData?.rollOnRollOff === 'Yes' || 
                                        matchData?.rollOnRollOff === 'yes';
            const isDisabledByRollOff = !rollOnRollOffEnabled && (wasSubstitutedIn || wasSubstitutedOut);
            const isRedCarded = isPlayerRedCarded(substitutionFlow.team!, number);
            const isDisabled = isDisabledByRollOff || isRedCarded;
            
            // Only log for disabled players to reduce noise
            if (isDisabled) {
              console.log(`🚫 Substitute Player ${number} DISABLED:`, {
                rollOnRollOff: matchData?.rollOnRollOff,
                rollOnRollOffEnabled,
                wasSubstitutedIn,
                wasSubstitutedOut,
                isRedCarded,
                previouslySubstitutedInPlayers,
                previouslySubstitutedOutPlayers
              });
            } else if (previouslySubstitutedInPlayers.includes(number) || previouslySubstitutedOutPlayers.includes(number)) {
              console.log(`⚠️ Substitute Player ${number} should be disabled but isn't:`, {
                rollOnRollOff: matchData?.rollOnRollOff,
                rollOnRollOffEnabled,
                wasSubstitutedIn,
                wasSubstitutedOut,
                isRedCarded,
                previouslySubstitutedInPlayers,
                previouslySubstitutedOutPlayers,
                expectedDisabled: true,
                actualDisabled: isDisabled
              });
            }
            
            return (
              <TouchableOpacity
                key={number}
                style={[
                  styles.playerButton,
                  substitutionFlow.selectedPlayerIn === number && styles.playerButtonSelected,
                  isDisabled && styles.playerButtonDisabled,
                  isRedCarded && styles.playerButtonRedCarded
                ]}
                onPress={isDisabled ? undefined : () => handleSubstitutionPlayerInSelect(number)}
                disabled={isDisabled}
              >
                <Text style={[
                  styles.playerButtonText,
                  substitutionFlow.selectedPlayerIn === number && styles.playerButtonTextSelected,
                  isDisabled && styles.playerButtonTextDisabled,
                  isRedCarded && styles.playerButtonTextRedCarded
                ]}>
                  {number}
                </Text>
                {isRedCarded && (
                  <View style={styles.redCardIndicator} />
                )}
              </TouchableOpacity>
            );
          })}
        </View>

        <TouchableOpacity 
          style={styles.cardFlowBackButton} 
          onPress={() => setSubstitutionFlow(prev => ({ ...prev, step: 'player-out' }))}
        >
          <Text style={styles.cardFlowBackText}>Back</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderSubstitutionDisplay = () => {
    const teamName = substitutionFlow.team === 'home'
      ? (matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home')
      : (matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away');

    const teamData = substitutionFlow.team === 'home' ? matchData?.homeTeam : matchData?.awayTeam;
    const playerOut = teamData?.squad?.players?.find((p: any) => p.number === substitutionFlow.selectedPlayerOut);
    const playerIn = teamData?.squad?.players?.find((p: any) => p.number === substitutionFlow.selectedPlayerIn);

    const currentTime = Math.floor(mainSeconds / 60);

    return (
      <View style={styles.cardFlowContainer}>
        <Text style={styles.cardFlowTitle}>Substitution - {teamName}</Text>
        <Text style={styles.cardTime}>{currentTime}' MINS</Text>

        <View style={styles.substitutionDisplayContainer}>
          {/* Player OUT with proper substitution out arrow */}
          <View style={styles.substitutionPlayerContainer}>
            <View style={styles.substitutionArrow}>
              <Svg width={40} height={40} viewBox="0 0 32 32">
                {/* Red circle with down arrow (Sub Out) */}
                <Circle cx="16" cy="16" r="15" fill="#DC3545" />
                <Path
                  d="M16 10V22 M13 19L16 22L19 19"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  fill="none"
                />
              </Svg>
            </View>
            <Text style={[styles.substitutionPlayerText, { color: theme.red }]}>
              {substitutionFlow.selectedPlayerOut}. {playerOut?.name || `Player ${substitutionFlow.selectedPlayerOut}`}
            </Text>
          </View>

          {/* Player IN with proper substitution in arrow */}
          <View style={styles.substitutionPlayerContainer}>
            <View style={styles.substitutionArrow}>
              <Svg width={40} height={40} viewBox="0 0 32 32">
                {/* Green circle with up arrow (Sub In) */}
                <Circle cx="16" cy="16" r="15" fill="#28A745" />
                <Path
                  d="M16 22V10 M13 13L16 10L19 13"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  fill="none"
                />
              </Svg>
            </View>
            <Text style={[styles.substitutionPlayerText, { color: theme.primary }]}>
              {substitutionFlow.selectedPlayerIn}. {playerIn?.name || `Player ${substitutionFlow.selectedPlayerIn}`}
            </Text>
          </View>
        </View>

        <TouchableOpacity 
          style={styles.confirmButton} 
          onPress={!isMatchStarted ? handlePreMatchSubstitutionConfirm : handleSubstitutionConfirm}
        >
          <Text style={styles.confirmButtonText}>Confirm Substitution</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.cardFlowBackButton} 
          onPress={() => setSubstitutionFlow(prev => ({ ...prev, step: 'player-in' }))}
        >
          <Text style={styles.cardFlowBackText}>Back</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderSubstitutionOpportunityCheck = () => {
    const teamName = substitutionFlow.team === 'home'
      ? (matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home')
      : (matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away');

    return (
      <View style={styles.cardFlowContainer}>
        <Text style={styles.cardFlowTitle}>Substitution Opportunity - {teamName}</Text>
        <Text style={styles.cardFlowSubtitle}>Is this substitution opportunity ended?</Text>

        <View style={styles.opportunityButtonsContainer}>
          <TouchableOpacity 
            style={[styles.opportunityButton, styles.opportunityEndButton]}
            onPress={() => handleOpportunityEnd(true)}
          >
            <Text style={styles.opportunityButtonText}>Yes - End Opportunity</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.opportunityButton, styles.opportunityContinueButton]}
            onPress={() => handleOpportunityEnd(false)}
          >
            <Text style={styles.opportunityButtonText}>No - Continue</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // Goal system rendering functions
  const renderGoalPlayerSelection = () => {
    const teamName = goalFlow.team === 'home'
      ? (matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home')
      : (matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away');

    // Get only starting lineup players (same logic as card system)
    const teamData = goalFlow.team === 'home' ? matchData?.homeTeam : matchData?.awayTeam;
    let playerNumbers = [];

    if (teamData?.squad?.players) {
      // Get only starting lineup players (position === 'STARTER')
      playerNumbers = teamData.squad.players
        ?.filter((p: any) => p.position === 'STARTER')
        ?.map((p: any) => p.number) || [];
    }

    // Fallback to default numbers if no squad data
    if (playerNumbers.length === 0) {
      console.log('⚠️ No squad data found for goal, using fallback numbers');
      playerNumbers = Array.from({ length: 11 }, (_, i) => i + 1); // Only starting 11
    }

    return (
      <View style={styles.cardFlowContainer}>
        <Text style={styles.cardFlowTitle}>Goal - {teamName}</Text>
        <Text style={styles.cardFlowSubtitle}>Select Player</Text>

        <View style={styles.playerGrid}>
          {playerNumbers.map((number: any) => {
            const isRedCarded = isPlayerRedCarded(goalFlow.team!, number);
            
            return (
              <TouchableOpacity
                key={number}
                style={[
                  styles.playerButton,
                  isRedCarded && styles.playerButtonRedCarded
                ]}
                onPress={() => !isRedCarded && handleGoalPlayerSelect(number)}
                disabled={isRedCarded}
              >
                <Text style={[
                  styles.playerButtonText,
                  isRedCarded && styles.playerButtonTextRedCarded
                ]}>
                  {number}
                </Text>
                {isRedCarded && (
                  <View style={styles.redCardIndicator} />
                )}
              </TouchableOpacity>
            );
          })}
        </View>

        <TouchableOpacity 
          style={styles.cardFlowBackButton} 
          onPress={resetGoalFlow}
        >
          <Text style={styles.cardFlowBackText}>Back</Text>
        </TouchableOpacity>
      </View>
    );
  };

  // Red card warning modal
  const renderRedCardWarningModal = (team: 'home' | 'away') => {
    const teamName = team === 'home'
      ? (matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home')
      : (matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away');

    return (
      <Modal
        animationType="fade"
        transparent={true}
        visible={showRedCardWarning[team]}
        onRequestClose={() => handleRedCardWarningIgnore(team)}
      >
        <View style={styles.redCardWarningOverlay}>
          <View style={styles.redCardWarningContainer}>
            <Text style={styles.redCardWarningTitle}>⚠️ Warning</Text>
            <Text style={styles.redCardWarningText}>
              Warning: Team {teamName} has 6 players remaining on the field
            </Text>
            <Text style={styles.redCardWarningSubtext}>
              The match may need to be abandoned if a team cannot field the minimum number of players (7).
            </Text>
            
            <View style={styles.redCardWarningButtons}>
              <TouchableOpacity
                style={[styles.redCardWarningButton, styles.redCardWarningIgnoreButton]}
                onPress={() => handleRedCardWarningIgnore(team)}
              >
                <Text style={styles.redCardWarningButtonText}>Ignore</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.redCardWarningButton, styles.redCardWarningAbandonButton]}
                onPress={() => handleRedCardWarningAbandon(team)}
              >
                <Text style={styles.redCardWarningButtonText}>Abandon</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  const renderGoalDisplay = () => {
    const teamName = goalFlow.team === 'home'
      ? (matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home')
      : (matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away');

    const teamData = goalFlow.team === 'home' ? matchData?.homeTeam : matchData?.awayTeam;
    const player = teamData?.squad?.players?.find((p: any) => p.number === goalFlow.selectedPlayer);
    const playerName = player?.name || `Player ${goalFlow.selectedPlayer}`;

    const currentTime = Math.floor(mainSeconds / 60);

    return (
      <View style={styles.cardFlowContainer}>
        <Text style={styles.cardFlowTitle}>Goal - {teamName}</Text>
        <Text style={styles.cardTime}>{currentTime}' MINS</Text>

        <View style={styles.goalDisplayContainer}>
          {/* Soccer Ball Icon */}
          <View style={styles.goalIconContainer}>
            <SoccerBallIcon color={theme.text} size={80} />
          </View>

          {/* Goal Time */}
          <Text style={styles.goalTimeText}>{currentTime}'</Text>
          
          {/* Player Info */}
          <Text style={styles.goalPlayerText}>
            {goalFlow.selectedPlayer}. {playerName}
          </Text>
        </View>
      </View>
    );
  };

  const renderCenterContent = () => {
    let periodLength;
    let periodDisplayText;

    if (isExtraTime) {
      // Extra time periods
      const etLengths = matchData?.extraTimeLengths || [15, 15];
      periodLength = currentETPhase === 'ET1' ? etLengths[0] : etLengths[1];
      const baseText = `${currentETPhase === 'ET1' ? 'ET Period 1' : 'ET Period 2'} (${periodLength} min)`;
      periodDisplayText = isInInjuryTime ? `${baseText} - Injury Time` : baseText;
    } else {
      // Regular periods
      periodLength = matchData?.periodLengths?.[currentPeriod - 1] || 45;
      const baseText = `Period ${currentPeriod} (${periodLength} min)`;
      periodDisplayText = isInInjuryTime ? `${baseText} - Injury Time` : baseText;
    }

    const periodLengthInSeconds = periodLength * 60;
    let remainingSeconds;
    let displayTime;
    
    if (isInInjuryTime) {
      // During injury time, show injury time countdown
      const currentInjuryTime = getCurrentInjuryTime();
      remainingSeconds = Math.max(0, currentInjuryTime - mainSeconds);
      displayTime = mainSeconds; // Show injury time elapsed from 0
    } else {
      // During regular time, show regular period time
      remainingSeconds = Math.max(0, periodLengthInSeconds - mainSeconds);
      displayTime = mainSeconds; // Show regular period time
    }

    return (
      <>
        <TouchableOpacity style={styles.syncButton}>
          <Text style={styles.syncButtonText}>Sync Status</Text>
        </TouchableOpacity>

        {renderMatchCard()}

        {matchPhase === 'penalties' ? (
          // Show penalty info below match card
          <>
            {renderPenaltyScoreDisplay()}
          </>
        ) : (
          // Show normal timer
          <View style={styles.timerContainer}>
            <Text style={styles.matchPeriodText}>
              {periodDisplayText}
            </Text>
            <View style={[
              styles.mainTimerBox,
              isCurrentlyInInjuryTime() && styles.injuryTimeMainTimerBox
            ]}>
              <Text style={[
                styles.mainTimerText,
                isCurrentlyInInjuryTime() && styles.injuryTimeMainTimerText
              ]}>
                {formatTime(displayTime)}
              </Text>
            </View>
            <View style={[
              styles.stoppageTimerBox,
              matchData?.injuryTimeAllowance && getCurrentInjuryTime() > 0 && styles.injuryTimeBox
            ]}>
              <Text style={[
                styles.stoppageTimerText,
                matchData?.injuryTimeAllowance && getCurrentInjuryTime() > 0 && styles.injuryTimeText
              ]}>
                +{formatTime(matchData?.injuryTimeAllowance ? getCurrentInjuryTime() : stoppageSeconds)}
              </Text>
            </View>
            
            <Text style={styles.remainingTimeText}>
              {formatTime(remainingSeconds)} remaining
            </Text>
          </View>
        )}

        {/* Sin Bin Display - Absolute Positioned */}
        {sinBinPlayers.length > 0 && (
          <View style={styles.sinBinAbsoluteContainer}>
            {/* Home Team Sin Bin - Left Side */}
            <View style={styles.sinBinTeamLeft}>
              {sinBinPlayers
                .filter(player => player.team === 'home')
                .map((player) => (
                  <View key={player.id} style={styles.sinBinItem}>
                    <Text style={styles.sinBinText}>
                      ({player.playerNumber}) {formatTime(player.remainingTime)}
                    </Text>
                  </View>
                ))
              }
            </View>
            
            {/* Away Team Sin Bin - Right Side */}
            <View style={styles.sinBinTeamRight}>
              {sinBinPlayers
                .filter(player => player.team === 'away')
                .map((player) => (
                  <View key={player.id} style={styles.sinBinItem}>
                    <Text style={styles.sinBinText}>
                      ({player.playerNumber}) {formatTime(player.remainingTime)}
                    </Text>
                  </View>
                ))
              }
            </View>
          </View>
        )}

        <View style={styles.swipeIndicator}>
          <Text style={styles.swipeIndicatorText}>
            ← {matchData?.homeTeam?.shortName || 'Home'}   • Swipe •   {matchData?.awayTeam?.shortName || 'Away'} →
          </Text>
        </View>
      </>
    );
  };

  // Penalty flow functions  
  const handlePenaltyOutcome = (outcome: 'goal' | 'miss' | 'save') => {
    setPenaltyFlow(prev => ({
      ...prev,
      outcome,
      step: 'player'
    }));
  };
  
  const handlePenaltyPlayer = (playerNumber: number) => {
    if (!penaltyFlow.team || !penaltyFlow.outcome) return;
    
    const teamData = penaltyFlow.team === 'home' ? matchData?.homeTeam : matchData?.awayTeam;
    const player = teamData?.squad?.players?.find((p: any) => p.number === playerNumber);
    const playerName = player?.name || `Player ${playerNumber}`;
    
    // Add result to penalty results
    setPenaltyResults(prev => ({
      ...prev,
      [penaltyFlow.team!]: [
        ...prev[penaltyFlow.team!],
        {
          player: playerNumber,
          playerName,
          outcome: penaltyFlow.outcome!
        }
      ]
    }));
    
    // Log the penalty shot
    addMatchLogEvent('penalty_shot', {
      team: penaltyFlow.team,
      player: playerNumber,
      playerName,
      outcome: penaltyFlow.outcome,
      round: penaltyRound
    });
    
    setPenaltyFlow(prev => ({
      ...prev,
      selectedPlayer: playerNumber,
      step: 'display'
    }));
  };
  
  const advancePenaltyTurn = () => {
    const winner = checkPenaltyWinner();
    
    if (winner !== 'continue') {
      // Show winner notification - but DON'T log penalty_end yet
      const winnerTeam = winner === 'home_wins' ? 'home' : 'away';
      const { home, away } = calculatePenaltyScore();
      
      // Show alert with winner
      const winnerName = winnerTeam === 'home' 
        ? (matchData?.homeTeam?.shortName || 'Home')
        : (matchData?.awayTeam?.shortName || 'Away');
      
      Alert.alert(
        'Penalty Shootout Complete!',
        `${winnerName} wins ${home}-${away} on penalties`,
        [
          { 
            text: 'End Match', 
            onPress: async () => {
              // Create the penalty_end event
              const penaltyEndEvent = {
                id: `log-${Date.now()}-${Math.random()}`,
                type: 'penalty_end' as const,
                timestamp: mainSeconds,
                realTime: new Date(),
                data: {
                  winner: winnerTeam,
                  homeScore: home,
                  awayScore: away,
                  totalRounds: penaltyRound,
                  finalScore: `${home}-${away}`,
                  winnerName: winnerTeam === 'home' 
                    ? (matchData?.homeTeam?.shortName || 'Home')
                    : (matchData?.awayTeam?.shortName || 'Away'),
                  time: getFormattedMatchTime()
                }
              };
              
              // Create updated log with penalty_end event
              const updatedLog = [...matchLog, penaltyEndEvent];
              console.log(`📋 Match Log: penalty_end`, penaltyEndEvent);
              console.log(`📋 Updated match log with penalty_end:`, updatedLog);
              
              // Update the state
              setMatchLog(updatedLog);
              
              // Show evaluation modal after penalty completion
              setTimeout(() => {
                console.log('🕰️ Showing evaluation modal after penalties...');
                
                // Reset penalty flow first
                setPenaltyFlow({
                  isActive: false,
                  step: 'outcome',
                  team: null,
                  round: 1,
                  outcome: null,
                  selectedPlayer: null,
                });
                
                // Set match as completed and exit penalty mode
                setMatchPhase('completed');
                setIsPenalties(false);
                setShowEndMatchButton(false);
                
                // Store the updated log for completion after evaluation
                setPendingMatchLog(updatedLog);
                
                // Show evaluation modal
                setShowEvaluationModal(true);
              }, 500); // Wait for state update
            }
          },
          {
            text: 'Continue',
            style: 'cancel',
            onPress: () => {
              // Continue penalties in sudden death mode
              // NO penalty_end event logged here - match continues
              const nextTeam = getNextPenaltyTeam();
              setCurrentPenaltyTeam(nextTeam);
              
              // Reset penalty flow to continue
              setPenaltyFlow({
                isActive: false,
                step: 'outcome',
                team: null,
                round: penaltyRound,
                outcome: null,
                selectedPlayer: null,
              });
              
              // Return to center view
              setCurrentView('center');
            }
          }
        ]
      );
      
      return;
    }
    
    // Update suggested team based on new penalty counts
    const nextSuggestedTeam = getNextPenaltyTeam();
    setCurrentPenaltyTeam(nextSuggestedTeam);
    
    // Update round based on total penalties taken
    const totalPenalties = penaltyResults.home.length + penaltyResults.away.length;
    const newRound = Math.max(1, Math.ceil((totalPenalties + 1) / 2));
    setPenaltyRound(newRound);
    
    setPenaltyFlow({
      isActive: false,
      step: 'outcome',
      team: null,
      round: newRound,
      outcome: null,
      selectedPlayer: null,
    });
    
    // Return to center view after penalty action completes
    setCurrentView('center');
  };
  
  // Penalty UI components
  const renderPenaltyScoreDisplay = () => {
    const { home, away } = calculatePenaltyScore();
    
    return (
      <View style={styles.penaltyScoreContainer}>
        {/* Home Team Penalties */}
        <View style={styles.penaltyTeamContainer}>
          <Text style={styles.penaltyTeamName}>
            {matchData?.homeTeam?.shortName || 'Home'}
          </Text>
          <View style={styles.penaltyRoundsContainer}>
            {(() => {
              const maxRounds = Math.max(5, penaltyResults.home.length, penaltyResults.away.length, penaltyRound);
              return Array.from({length: maxRounds}, (_, i) => i + 1).map(round => {
                const result = penaltyResults.home[round - 1];
                return (
                  <View key={round} style={styles.penaltyRoundBox}>
                    <Text style={styles.penaltyRoundNumber}>{round}</Text>
                    <View style={styles.penaltyOutcomeBox}>
                      {result ? (
                        result.outcome === 'goal' ? (
                          <Text style={styles.penaltyGoalIcon}>⚽</Text>
                        ) : result.outcome === 'save' ? (
                          <GoalkeeperIcon size={16} color="#FF9800" />
                        ) : (
                          <Text style={styles.penaltyMissIcon}>❌</Text>
                        )
                      ) : (
                        <View style={styles.penaltyEmptyBox} />
                      )}
                    </View>
                  </View>
                );
              });
            })()
            }
          </View>
          <Text style={styles.penaltyTeamScore}>{home}</Text>
        </View>
        
        {/* Away Team Penalties */}
        <View style={styles.penaltyTeamContainer}>
          <Text style={styles.penaltyTeamName}>
            {matchData?.awayTeam?.shortName || 'Away'}
          </Text>
          <View style={styles.penaltyRoundsContainer}>
            {(() => {
              const maxRounds = Math.max(5, penaltyResults.home.length, penaltyResults.away.length, penaltyRound);
              return Array.from({length: maxRounds}, (_, i) => i + 1).map(round => {
                const result = penaltyResults.away[round - 1];
                return (
                  <View key={round} style={styles.penaltyRoundBox}>
                    <Text style={styles.penaltyRoundNumber}>{round}</Text>
                    <View style={styles.penaltyOutcomeBox}>
                      {result ? (
                        result.outcome === 'goal' ? (
                          <Text style={styles.penaltyGoalIcon}>⚽</Text>
                        ) : result.outcome === 'save' ? (
                          <GoalkeeperIcon size={16} color="#FF9800" />
                        ) : (
                          <Text style={styles.penaltyMissIcon}>❌</Text>
                        )
                      ) : (
                        <View style={styles.penaltyEmptyBox} />
                      )}
                    </View>
                  </View>
                );
              });
            })()
            }
          </View>
          <Text style={styles.penaltyTeamScore}>{away}</Text>
        </View>
      </View>
    );
  };
  
  
  const renderPenaltyOutcomeSelection = () => {
    const currentTeamName = penaltyFlow.team === 'home'
      ? (matchData?.homeTeam?.shortName || 'Home')
      : (matchData?.awayTeam?.shortName || 'Away');
      
    return (
      <View style={styles.penaltySelectionContainer}>
        <Text style={styles.penaltySelectionTitle}>
          {currentTeamName} - Round {penaltyFlow.round}
        </Text>
        <Text style={styles.penaltySelectionSubtitle}>
          Select penalty outcome:
        </Text>
        
        <View style={styles.penaltyOutcomeButtons}>
          <TouchableOpacity
            style={[styles.penaltyOutcomeButton, styles.penaltyGoalButton]}
            onPress={() => handlePenaltyOutcome('goal')}
          >
            <Text style={styles.penaltyOutcomeIcon}>⚽</Text>
            <Text style={styles.penaltyOutcomeText}>Goal</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.penaltyOutcomeButton, styles.penaltyMissButton]}
            onPress={() => handlePenaltyOutcome('miss')}
          >
            <Text style={styles.penaltyOutcomeIcon}>❌</Text>
            <Text style={styles.penaltyOutcomeText}>Miss</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.penaltyOutcomeButton, styles.penaltySaveButton]}
            onPress={() => handlePenaltyOutcome('save')}
          >
            <GoalkeeperIcon size={32} color="#FF9800" />
            <Text style={styles.penaltyOutcomeText}>Save</Text>
          </TouchableOpacity>
        </View>
        
        <TouchableOpacity
          style={styles.penaltyCancelButton}
          onPress={() => setPenaltyFlow(prev => ({ ...prev, isActive: false }))}
        >
          <Text style={styles.penaltyCancelButtonText}>Cancel</Text>
        </TouchableOpacity>
      </View>
    );
  };
  
  const renderPenaltyPlayerSelection = () => {
    if (!penaltyFlow.team) return null;
    
    const availablePlayers = getAvailablePenaltyPlayers(penaltyFlow.team);
    const currentTeamName = penaltyFlow.team === 'home'
      ? (matchData?.homeTeam?.shortName || 'Home')
      : (matchData?.awayTeam?.shortName || 'Away');
    
    const outcomeText = penaltyFlow.outcome === 'goal' ? 'Goal' : 
                       penaltyFlow.outcome === 'miss' ? 'Miss' : 'Save';
    
    return (
      <View style={styles.cardFlowContainer}>
        <Text style={styles.cardFlowTitle}>{currentTeamName} - {outcomeText}</Text>
        <Text style={styles.cardFlowSubtitle}>Select the player who took the penalty</Text>
        
        <View style={styles.playerGrid}>
          {availablePlayers.map((playerNumber: any) => {
            return (
              <TouchableOpacity
                key={playerNumber}
                style={styles.playerButton}
                onPress={() => handlePenaltyPlayer(playerNumber)}
              >
                <Text style={styles.playerButtonText}>
                  {playerNumber}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
        
        <TouchableOpacity
          style={styles.cardFlowBackButton}
          onPress={() => setPenaltyFlow(prev => ({ ...prev, step: 'outcome' }))}
        >
          <Text style={styles.cardFlowBackText}>Back</Text>
        </TouchableOpacity>
      </View>
    );
  };
  
  const renderPenaltyDisplay = () => {
    if (!penaltyFlow.team || !penaltyFlow.outcome || !penaltyFlow.selectedPlayer) return null;
    
    const teamData = penaltyFlow.team === 'home' ? matchData?.homeTeam : matchData?.awayTeam;
    const player = teamData?.squad?.players?.find((p: any) => p.number === penaltyFlow.selectedPlayer);
    const playerName = player?.name || `Player ${penaltyFlow.selectedPlayer}`;
    const currentTeamName = penaltyFlow.team === 'home'
      ? (matchData?.homeTeam?.shortName || 'Home')
      : (matchData?.awayTeam?.shortName || 'Away');
    
    const outcomeText = penaltyFlow.outcome === 'goal' ? 'Goal!' : 
                       penaltyFlow.outcome === 'miss' ? 'Miss!' : 'Saved!';
    const outcomeColor = penaltyFlow.outcome === 'goal' ? '#4CAF50' : '#F44336';
    
    return (
      <View style={styles.penaltyDisplayContainer}>
        <View style={[styles.penaltyDisplayOutcome, { borderColor: outcomeColor }]}>
          {penaltyFlow.outcome === 'goal' ? (
            <Text style={[styles.penaltyDisplayIcon, { color: outcomeColor }]}>⚽</Text>
          ) : penaltyFlow.outcome === 'save' ? (
            <GoalkeeperIcon size={48} color={outcomeColor} />
          ) : (
            <Text style={[styles.penaltyDisplayIcon, { color: outcomeColor }]}>❌</Text>
          )}
          <Text style={[styles.penaltyDisplayText, { color: outcomeColor }]}>{outcomeText}</Text>
        </View>
        
        <View style={styles.penaltyDisplayInfo}>
          <Text style={styles.penaltyDisplayTeam}>{currentTeamName}</Text>
          <Text style={styles.penaltyDisplayPlayer}>#{penaltyFlow.selectedPlayer} {playerName}</Text>
          <Text style={styles.penaltyDisplayRound}>Round {penaltyFlow.round}</Text>
        </View>
        
        <TouchableOpacity
          style={styles.penaltyContinueButton}
          onPress={advancePenaltyTurn}
        >
          <Text style={styles.penaltyContinueButtonText}>Continue</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderInMatchContent = () => {
    // Handle penalty flow - but only for the selection screens, not main content
    if (matchPhase === 'penalties' && penaltyFlow.isActive) {
      switch (penaltyFlow.step) {
        case 'outcome':
          return renderPenaltyOutcomeSelection();
        case 'player':
          return renderPenaltyPlayerSelection();
        case 'display':
          return renderPenaltyDisplay();
        default:
          // Fall through to normal content with penalty timer
          break;
      }
    }
    
    // Handle goal flow
    if (goalFlow.isActive) {
      switch (goalFlow.step) {
        case 'player':
          return renderGoalPlayerSelection();
        case 'display':
          return renderGoalDisplay();
        default:
          return renderCenterContent();
      }
    }

    // Handle card flow
    if (cardFlow.isActive) {
      switch (cardFlow.step) {
        case 'person':
          return renderPersonTypeSelection();
        case 'player':
          return renderPlayerSelection();
        case 'code':
          return renderCodeSelection();
        case 'subcode':
          return renderSubCodeSelection();
        case 'confirm':
          return renderCardConfirmation();
        default:
          return renderCenterContent();
      }
    }

    // Handle substitution flow
    if (substitutionFlow.isActive) {
      switch (substitutionFlow.step) {
        case 'player-out':
          return renderSubstitutionPlayerOutSelection();
        case 'player-in':
          return renderSubstitutionPlayerInSelection();
        case 'display':
          return renderSubstitutionDisplay();
        case 'opportunity-check':
          return renderSubstitutionOpportunityCheck();
        default:
          return renderCenterContent();
      }
    }

    // Handle different match phases
    if (matchPhase === 'halftime' || matchPhase === 'et-halftime') {
      return renderHalfTimeContent();
    }

    if (matchPhase === 'completed') {
      return renderMatchCompletedContent();
    }
    
    if (matchPhase === 'penalties') {
      // During penalties, use normal swipe view but with penalty content in center
      if (currentView === 'home') {
        return renderActionButtons('home');
      } else if (currentView === 'away') {
        return renderActionButtons('away');
      } else {
        return renderCenterContent(); // This will show penalty UI instead of timer
      }
    }

    // Normal match play or extra time - handle view switching
    if (currentView === 'home') {
      return renderActionButtons('home');
    } else if (currentView === 'away') {
      return renderActionButtons('away');
    } else {
      return renderCenterContent();
    }
  };

  const renderMatchCard = () => (
    <View style={styles.matchCard}>
      <View style={styles.teamsContainer}>
        {/* Home Team */}
        <View style={styles.teamColumn}>
          <StripedTshirt
            baseColor={matchData?.homeTeam?.kit?.base || "#DA291C"}
            stripeColor={matchData?.homeTeam?.kit?.stripe || "#000"}
            size={64}
            isSelected={selectedTeam === 'home'}
            onPress={isMatchStarted ? undefined : () => handleTeamSelect('home')}
          />
          <Text style={styles.teamName}>
            {matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home'}
          </Text>
        </View>

        {/* VS Separator or Score */}
        <View style={styles.separatorContainer}>
          {isMatchStarted || matchPhase === 'penalties' ? (
            <View style={styles.scoreContainer}>
              <Text style={styles.scoreText}>
                {matchGoals.filter(goal => goal.team === 'home').length} - {matchGoals.filter(goal => goal.team === 'away').length}
              </Text>
            </View>
          ) : (
            <Text style={styles.vsText}>VS</Text>
          )}
        </View>

        {/* Away Team */}
        <View style={styles.teamColumn}>
          <StripedTshirt
            baseColor={matchData?.awayTeam?.kit?.base || "#004193"}
            stripeColor={matchData?.awayTeam?.kit?.stripe || "#E30613"}
            size={64}
            isSelected={selectedTeam === 'away'}
            onPress={isMatchStarted ? undefined : () => handleTeamSelect('away')}
          />
          <Text style={styles.teamName}>
            {matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away'}
          </Text>
        </View>
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <Header title="Start Match" showBackButton={true} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.primary} />
          <Text style={styles.loadingText}>Loading match...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !matchData) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <Header title="Start Match" showBackButton={true} />
        <View style={styles.container}>
          <View style={styles.card}>
            <Text style={styles.cardTitle}>Error</Text>
            <Text style={styles.cardText}>{error || 'No match data available'}</Text>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <Header
        title="Start Match"
        showBackButton={true}
        rightComponent={
          (isMatchStarted || matchPhase === 'penalties') ? (
            <TouchableOpacity onPress={() => setMenuVisible(true)}>
              <Text style={styles.menuButtonText}>{matchPhase === 'penalties' ? 'End Match' : 'Menu'}</Text>
            </TouchableOpacity>
          ) : undefined
        }
      />

      <Modal
        animationType="fade"
        transparent={true}
        visible={isMenuVisible}
        onRequestClose={() => setMenuVisible(false)}
      >
        <TouchableOpacity style={styles.modalOverlay} activeOpacity={1} onPressOut={() => setMenuVisible(false)}>
          <View style={styles.modalContent}>
            <TouchableOpacity style={styles.modalItem} onPress={handleAbandonMatch}>
              <Text style={styles.modalText}>Abandon Match</Text>
            </TouchableOpacity>
            <View style={styles.modalSeparator} />
            <TouchableOpacity style={styles.modalItem} onPress={handleResetMatch}>
              <Text style={styles.modalText}>Reset Match</Text>
            </TouchableOpacity>
            <View style={styles.modalSeparator} />
            <TouchableOpacity style={styles.modalItem} onPress={() => setMenuVisible(false)}>
              <Text style={styles.modalText}>Go Back</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>

      <GestureDetector gesture={panGesture}>
        <View style={styles.container}>
          {(isMatchStarted || matchPhase === 'completed' || matchPhase === 'penalties') ? renderInMatchContent() : renderPreMatchContent()}
        </View>
      </GestureDetector>

      {/* Red card warning modals - show for both pre-match and in-match */}
      {renderRedCardWarningModal('home')}
      {renderRedCardWarningModal('away')}
      
      {/* Evaluation Modal */}
      <MatchEvaluationModal
        visible={showEvaluationModal}
        matchId={matchId || ''}
        onComplete={(answers) => {
          console.log('📝 Evaluation completed with answers:', answers);
          
          // Check if we have pending penalty logs or regular match
          if (pendingMatchLog.length > 0) {
            // Complete penalty match with pending logs
            completePenaltyMatchAfterEvaluation(pendingMatchLog, answers);
            setPendingMatchLog([]); // Clear pending logs
          } else {
            // Complete regular match
            completeMatchAfterEvaluation(answers);
          }
        }}
        onClose={() => setShowEvaluationModal(false)}
      />
    </SafeAreaView>
  );
};

const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: theme.card,
  },
  container: {
    flex: 1,
    backgroundColor: theme.background,
    padding: 16,
  },

  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.background,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.textSecondary,
  },
  instructionText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.text,
    textAlign: 'center',
    marginBottom: 24,
  },
  syncButton: {
    alignSelf: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: theme.card,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: theme.border,
  },
  syncButtonText: {
    color: theme.text,
    fontWeight: '600',
  },
  matchCard: {
    backgroundColor: theme.card,
    borderRadius: 16,
    padding: 24,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.05,
    shadowRadius: 4,
    elevation: isDarkMode ? 0 : 2,
  },
  teamsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  teamColumn: {
    alignItems: 'center',
    flex: 1,
  },
  teamName: {
    marginTop: 12,
    fontSize: 14,
    fontWeight: '600',
    color: theme.text,
    textAlign: 'center',
    maxWidth: 100,
  },
  separatorContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
  },
  vsText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.textSecondary,
  },
  scoreContainer: {
    alignItems: 'center',
  },
  scoreText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.text,
  },
  penaltyScoreSubText: {
    fontSize: 12,
    color: theme.textSecondary,
    fontStyle: 'italic',
    marginTop: 4,
  },
  startButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 'auto',
    marginBottom: 40,
  },
  startButtonText: {
    color: theme.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  menuButtonText: {
    color: theme.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  // Timer styles
  timerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  matchPeriodText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.text,
    marginBottom: 6,
  },
  mainTimerBox: {
    backgroundColor: theme.card,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: theme.primary,
    marginBottom: 8,
  },
  mainTimerText: {
    fontSize: 72,
    fontWeight: 'bold',
    color: theme.text,
    fontVariant: ['tabular-nums'],
  },
  injuryTimeMainTimerBox: {
    borderColor: '#FF4444',
  },
  injuryTimeMainTimerText: {
    color: '#FF4444',
  },
  stoppageTimerBox: {
    backgroundColor: theme.card,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: theme.primary,
  },
  stoppageTimerText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.text,
    fontVariant: ['tabular-nums'],
  },
  injuryTimeBox: {
    borderColor: '#FF4444',
  },
  injuryTimeText: {
    color: '#FF4444',
  },
  sinBinAbsoluteContainer: {
    position: 'absolute',
    top: '67%',
    left: 0,
    right: 0,
    zIndex: 10,
    pointerEvents: 'none',
  },
  sinBinTeamLeft: {
    position: 'absolute',
    left: 20,
    alignItems: 'center',
  },
  sinBinTeamRight: {
    position: 'absolute',
    right: 20,
    alignItems: 'center',
  },
  sinBinTeamsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    width: '100%',
    marginTop: 16,
    paddingHorizontal: 20,
  },
  sinBinTeamSide: {
    alignItems: 'center',
    flex: 1,
    paddingHorizontal: 10,
  },
  sinBinContainer: {
    marginTop: 16,
    alignItems: 'center',
    width: '100%',
  },
  sinBinItem: {
    backgroundColor: '#FFD700',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    marginBottom: 6,
    borderWidth: 1,
    borderColor: '#FFA500',
    pointerEvents: 'auto',
  },
  sinBinText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000',
    fontVariant: ['tabular-nums'],
  },
  swipeIndicator: {
    alignItems: 'center',
    paddingVertical: 10,
  },
  swipeIndicatorText: {
    fontSize: 14,
    color: theme.textSecondary,
    opacity: 0.7,
    marginBottom: 30,
  },

  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    alignItems: 'flex-end',
    paddingTop: 50,
    paddingRight: 10,
  },
  modalContent: {
    backgroundColor: theme.card,
    borderRadius: 8,
    padding: 8,
    width: 180,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  modalItem: {
    paddingVertical: 12,
    paddingHorizontal: 8,
  },
  modalText: {
    fontSize: 16,
    color: theme.text,
  },
  modalSeparator: {
    height: 1,
    backgroundColor: theme.border,
    marginHorizontal: 8,
  },
  // Card styles for error state
  card: {
    backgroundColor: theme.card,
    borderRadius: 16,
    padding: 16,
    margin: 16,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.text,
    marginBottom: 16,
  },
  cardText: {
    fontSize: 14,
    color: theme.textSecondary,
  },
  // Action buttons styles
  actionButtonsContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  teamActionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.text,
    marginBottom: 40,
    textAlign: 'center',
  },
  actionButtonsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    width: '100%',
    maxWidth: 300,
    alignSelf: 'center',
  },
  actionButton: {
    width: '45%',
    aspectRatio: 1,
    backgroundColor: theme.card,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: theme.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: isDarkMode ? 0 : 0.1,
    shadowRadius: 4,
    elevation: isDarkMode ? 0 : 3,
  },
  yellowCardButton: {
    borderColor: '#FFD700',
  },
  redCardButton: {
    borderColor: '#FF0000',
  },
  subButton: {
    borderColor: theme.primary,
  },
  goalButton: {
    borderColor: theme.primary,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.text,
    marginTop: 8,
    textAlign: 'center',
  },
  yellowCard: {
    width: 24,
    height: 32,
    backgroundColor: '#FFD700',
    borderRadius: 4,
  },
  redCard: {
    width: 24,
    height: 32,
    backgroundColor: '#FF0000',
    borderRadius: 4,
  },

  // New styles for enhanced timer and match phases
  remainingTimeText: {
    fontSize: 14,
    color: theme.textSecondary,
    textAlign: 'center',
    marginBottom: 80,
    fontWeight: '500',
    
  },
  halfTimeSubtext: {
    fontSize: 16,
    color: theme.textSecondary,
    textAlign: 'center',
    marginTop: 12,
    fontWeight: '500',
  },
  completedContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  completedText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: theme.primary,
    marginBottom: 8,
  },
  completedSubtext: {
    fontSize: 16,
    color: theme.textSecondary,
    fontWeight: '500',
  },
  completedExtraText: {
    fontSize: 14,
    color: theme.textSecondary,
    fontWeight: '400',
    marginTop: 4,
    fontStyle: 'italic',
  },

  // Card flow styles
  cardFlowContainer: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
  },
  cardFlowTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.text,
    marginBottom: 8,
  },
  cardFlowSubtitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.text,
    marginBottom: 8,
  },
  cardFlowTeam: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.primary,
    marginBottom: 40,
  },
  personTypeGrid: {
    width: '100%',
    maxWidth: 300,
  },
  personTypeButton: {
    backgroundColor: theme.card,
    borderRadius: 12,
    paddingVertical: 20,
    paddingHorizontal: 24,
    marginBottom: 16,
    borderWidth: 2,
    borderColor: theme.primary,
    alignItems: 'center',
  },
  personTypeText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.text,
  },
  playerGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    width: '100%',
    maxWidth: 300,
  },
  playerButton: {
    width: '22%',
    aspectRatio: 1,
    backgroundColor: theme.card,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: theme.border,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  playerButtonSelected: {
    backgroundColor: theme.primary,
    borderColor: theme.primary,
  },
  playerButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.text,
  },
  playerButtonTextSelected: {
    color: theme.white,
  },
  playerButtonDisabled: {
    backgroundColor: theme.card,
    borderColor: theme.textSecondary,
    opacity: 0.5,
  },
  playerButtonTextDisabled: {
    color: theme.textSecondary,
    opacity: 0.7,
  },
  playerSelectionScroll: {
    flex: 1,
    width: '100%',
    maxHeight: 400,
    marginBottom: 20,
  },
  okButton: {
    backgroundColor: theme.primary,
    borderColor: theme.primary,
  },
  okButtonDisabled: {
    backgroundColor: theme.textSecondary,
    borderColor: theme.textSecondary,
    opacity: 0.5,
  },
  okButtonText: {
    color: theme.white,
  },
  okButtonTextDisabled: {
    color: theme.white,
    opacity: 0.7,
  },
  codeList: {
    flex: 1,
    width: '100%',
    maxWidth: 350,
    alignSelf: 'center',
  },
  codeButton: {
    backgroundColor: theme.card,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 20,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: theme.primary,
  },
  codeButtonSelected: {
    borderColor: theme.primary,
    backgroundColor: theme.primary + '20',
  },
  codeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.text,
  },
  codeButtonSubtext: {
    fontSize: 14,
    color: theme.textSecondary,
    fontStyle: 'italic',
    marginTop: 4,
  },
  cardFlowBackButton: {
    marginTop: 'auto',
    paddingVertical: 12,
    paddingHorizontal: 24,
    marginBottom: 20,
    borderRadius: 8,
    backgroundColor: theme.card,
  },
  cardFlowBackText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.red,
  },
  confirmationTeam: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.primary,
    marginBottom: 40,
  },
  cardDisplay: {
    width: 120,
    height: 160,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  yellowCardDisplay: {
    backgroundColor: '#FFD700',
  },
  redCardDisplay: {
    backgroundColor: '#FF0000',
  },
  cardCode: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000',
    textAlign: 'center',
  },
  cardSubCode: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000',
  },
  cardTime: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.primary,
    marginBottom: 12,
  },
  cardPlayer: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.text,
    marginBottom: 40,
  },
  confirmButton: {
    backgroundColor: theme.primary,
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    marginBottom: 20,
  },
  confirmButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.white,
  },
  emptyCodesContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.card,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.border,
  },
  emptyCodesText: {
    fontSize: 16,
    color: theme.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  codeSubcategoryHint: {
    fontSize: 12,
    color: theme.primary,
    marginTop: 4,
    fontStyle: 'italic',
  },
  selectedMainCode: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.text,
    textAlign: 'center',
    marginBottom: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: theme.card,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.primary,
  },
  doubleCardContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    flexWrap: 'wrap',
  },
  playerButtonWithYellow: {
    borderColor: '#FFD700',
    borderWidth: 3,
  },
  playerButtonSecondYellow: {
    borderColor: '#FF0000',
    backgroundColor: '#FFE6E6',
  },
  yellowCardIndicator: {
    position: 'absolute',
    top: 2,
    right: 2,
    width: 8,
    height: 10,
    backgroundColor: '#FFD700',
    borderRadius: 1,
  },
  secondYellowWarning: {
    position: 'absolute',
    bottom: -16,
    fontSize: 10,
    color: '#FF0000',
    fontWeight: 'bold',
    textAlign: 'center',
    width: '100%',
  },
  playerButtonRedCarded: {
    backgroundColor: '#FFE6E6',
    borderColor: '#FF0000',
    opacity: 0.6,
  },
  playerButtonTextRedCarded: {
    color: '#FF0000',
    opacity: 0.8,
  },
  redCardIndicator: {
    position: 'absolute',
    top: 2,
    right: 2,
    width: 8,
    height: 10,
    backgroundColor: '#FF0000',
    borderRadius: 1,
  },

  // Substitution flow styles
  substitutionDisplayContainer: {
    width: '100%',
    maxWidth: 300,
    marginBottom: 40,
  },
  substitutionPlayerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    paddingHorizontal: 16,
  },
  substitutionArrow: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  substitutionArrowText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  substitutionPlayerText: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
  },
  opportunityButtonsContainer: {
    width: '100%',
    maxWidth: 300,
    marginTop: 40,
  },
  opportunityButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginBottom: 16,
    alignItems: 'center',
    borderWidth: 2,
  },
  opportunityEndButton: {
    backgroundColor: theme.card,
    borderColor: theme.red,
  },
  opportunityContinueButton: {
    backgroundColor: theme.card,
    borderColor: theme.primary,
  },
  opportunityButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.text,
  },

  // Goal flow styles
  goalDisplayContainer: {
    width: '100%',
    maxWidth: 300,
    alignItems: 'center',
    marginBottom: 40,
  },
  goalIconContainer: {
    marginBottom: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  goalTimeText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: theme.primary,
    marginBottom: 16,
  },
  goalPlayerText: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.text,
    textAlign: 'center',
  },
  playersGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    width: '100%',
    maxWidth: 300,
    alignSelf: 'center',
  },
  playerNumberText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.text,
  },
  playerNameText: {
    fontSize: 12,
    color: theme.textSecondary,
    textAlign: 'center',
    marginTop: 4,
  },
  scrollContainer: {
    flex: 1,
    width: '100%',
    maxHeight: 400,
    marginBottom: 20,
  },

  // Red card warning modal styles
  redCardWarningOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  redCardWarningContainer: {
    backgroundColor: theme.card,
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  redCardWarningTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.red,
    marginBottom: 16,
    textAlign: 'center',
  },
  redCardWarningText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.text,
    textAlign: 'center',
    marginBottom: 12,
  },
  redCardWarningSubtext: {
    fontSize: 14,
    color: theme.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  redCardWarningButtons: {
    flexDirection: 'row',
    gap: 12,
    width: '100%',
  },
  redCardWarningButton: {
    flex: 1,
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  redCardWarningIgnoreButton: {
    backgroundColor: theme.card,
    borderWidth: 2,
    borderColor: theme.primary,
  },
  redCardWarningAbandonButton: {
    backgroundColor: theme.red,
    borderWidth: 2,
    borderColor: theme.red,
  },
  redCardWarningButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.text,
  },

  // Penalty styles
  penaltyScoreContainer: {
    backgroundColor: theme.card,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  penaltyTeamContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  penaltyTeamName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.text,
    marginBottom: 12,
  },
  penaltyRoundsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  penaltyRoundBox: {
    alignItems: 'center',
    marginHorizontal: 4,
  },
  penaltyRoundNumber: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.textSecondary,
    marginBottom: 4,
  },
  penaltyOutcomeBox: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 16,
    backgroundColor: theme.background,
    borderWidth: 1,
    borderColor: theme.border,
  },
  penaltyEmptyBox: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: theme.border,
    opacity: 0.3,
  },
  penaltyGoalIcon: {
    fontSize: 16,
    color: '#4CAF50',
  },
  penaltyMissIcon: {
    fontSize: 14,
    color: '#F44336',
  },
  penaltyTeamScore: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.primary,
  },
  
  // Penalty action styles
  penaltyActionContainer: {
    alignItems: 'center',
    padding: 40,
    backgroundColor: theme.card,
    borderRadius: 16,
    marginBottom: 20,
  },
  penaltyTurnTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.text,
    marginBottom: 8,
  },
  penaltyTurnSubtitle: {
    fontSize: 16,
    color: theme.textSecondary,
    textAlign: 'center',
    marginBottom: 30,
  },
  penaltyStartButton: {
    backgroundColor: theme.primary,
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
  },
  penaltyStartButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.white,
  },
  
  // Penalty selection styles
  penaltySelectionContainer: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
  },
  penaltySelectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  penaltySelectionSubtitle: {
    fontSize: 16,
    color: theme.textSecondary,
    marginBottom: 30,
    textAlign: 'center',
  },
  
  // Penalty outcome buttons
  penaltyOutcomeButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    maxWidth: 350,
    marginBottom: 40,
  },
  penaltyOutcomeButton: {
    alignItems: 'center',
    padding: 20,
    borderRadius: 16,
    borderWidth: 2,
    minWidth: 80,
    backgroundColor: theme.card,
  },
  penaltyGoalButton: {
    borderColor: '#4CAF50',
  },
  penaltyMissButton: {
    borderColor: '#F44336',
  },
  penaltySaveButton: {
    borderColor: '#FF9800',
  },
  penaltyOutcomeIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  penaltyOutcomeText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.text,
  },
  
  // Penalty player selection
  penaltyPlayersList: {
    width: '100%',
    maxWidth: 350,
    maxHeight: 400,
    marginBottom: 20,
  },
  penaltyPlayerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: theme.card,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: theme.border,
  },
  penaltyPlayerNumber: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  penaltyPlayerNumberText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.white,
  },
  penaltyPlayerInfo: {
    flex: 1,
  },
  penaltyPlayerName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.text,
    marginBottom: 4,
  },
  penaltyPlayerPosition: {
    fontSize: 12,
    color: theme.textSecondary,
  },
  
  // Penalty display styles
  penaltyDisplayContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  penaltyDisplayOutcome: {
    alignItems: 'center',
    padding: 40,
    borderRadius: 20,
    borderWidth: 3,
    marginBottom: 40,
    backgroundColor: theme.card,
  },
  penaltyDisplayIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  penaltyDisplayText: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  penaltyDisplayInfo: {
    alignItems: 'center',
    marginBottom: 40,
  },
  penaltyDisplayTeam: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.primary,
    marginBottom: 8,
  },
  penaltyDisplayPlayer: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.text,
    marginBottom: 8,
  },
  penaltyDisplayRound: {
    fontSize: 16,
    color: theme.textSecondary,
  },
  
  // Penalty control buttons
  penaltyCancelButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    backgroundColor: theme.card,
    borderWidth: 1,
    borderColor: theme.border,
  },
  penaltyCancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.textSecondary,
  },
  penaltyContinueButton: {
    backgroundColor: theme.primary,
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
  },
  penaltyContinueButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.white,
  },
});

export default StartMatchScreen;