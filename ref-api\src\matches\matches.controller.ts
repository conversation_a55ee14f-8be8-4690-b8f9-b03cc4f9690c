import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpException,
  HttpStatus,
  UseGuards,
  Request,
  Put
} from '@nestjs/common';
import { MatchesService } from './matches.service';
import { CreateMatchDto } from './dto/create-match.dto';
import { UpdateMatchDto } from './dto/update-match.dto';
import { CreateSquadDto } from './dto/create-squad.dto';
import { UpdateSquadDto } from './dto/update-squad.dto';
import { MatchStatus } from '@prisma/client';
import { FirebaseAuthGuard } from '../auth/firebase-auth.guard';

@Controller('matches')
export class MatchesController {
  constructor(private readonly matchesService: MatchesService) { }

  @UseGuards(FirebaseAuthGuard)
  @Post()
  async create(@Body() createMatchDto: CreateMatchDto, @Request() req: any) {
    try {
      // Get the authenticated user's ID
      const refereeId = req.user.id;

      const match = await this.matchesService.create(createMatchDto, refereeId);

      return {
        success: true,
        data: match,
        message: 'Match created successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to create match'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @UseGuards(FirebaseAuthGuard)
  @Get()
  async findAll(@Request() req: any) {
    try {
      // Get matches for the authenticated user only
      const refereeId = req.user.id;
      const matches = await this.matchesService.findAll(refereeId);

      return {
        success: true,
        data: matches,
        message: 'Matches retrieved successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to retrieve matches'
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @UseGuards(FirebaseAuthGuard)
  @Get('upcoming')
  async findUpcoming(@Request() req: any) {
    try {
      // Get upcoming matches for the authenticated user only
      const refereeId = req.user.id;
      const matches = await this.matchesService.findUpcoming(refereeId);

      return {
        success: true,
        data: matches,
        message: 'Upcoming matches retrieved successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to retrieve upcoming matches'
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @UseGuards(FirebaseAuthGuard)
  @Get('previous')
  async findPrevious(@Request() req: any) {
    try {
      // Get previous matches for the authenticated user only
      const refereeId = req.user.id;
      const matches = await this.matchesService.findPrevious(refereeId);

      return {
        success: true,
        data: matches,
        message: 'Previous matches retrieved successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to retrieve previous matches'
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Filter options endpoints - MUST be before :id routes
  @UseGuards(FirebaseAuthGuard)
  @Get('filters/options')
  async getFilterOptions(@Request() req: any) {
    try {
      const refereeId = req.user.id;
      const filterOptions = await this.matchesService.getFilterOptions(refereeId);

      return {
        success: true,
        data: filterOptions,
        message: 'Filter options retrieved successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to retrieve filter options'
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Filtered matches endpoint
  @UseGuards(FirebaseAuthGuard)
  @Post('filters/search')
  async getFilteredMatches(
    @Body() filters: any,
    @Request() req: any
  ) {
    try {
      const refereeId = req.user.id;
      const filteredMatches = await this.matchesService.getFilteredMatches(refereeId, filters);

      return {
        success: true,
        data: filteredMatches,
        message: 'Filtered matches retrieved successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to retrieve filtered matches'
        },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @UseGuards(FirebaseAuthGuard)
  @Get(':id')
  async findOne(@Param('id') id: string, @Request() req: any) {
    try {
      const match = await this.matchesService.findOne(id);

      // Check if user is the referee (match creator) or a synced participant
      const isReferee = match.refereeId === req.user.id;
      const isParticipant = await this.matchesService.isUserParticipant(id, req.user.id);

      if (!isReferee && !isParticipant) {
        throw new HttpException(
          {
            success: false,
            error: 'Unauthorized',
            message: 'You can only access your own matches'
          },
          HttpStatus.FORBIDDEN
        );
      }

      return {
        success: true,
        data: match,
        message: 'Match retrieved successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to retrieve match'
        },
        error.status || HttpStatus.NOT_FOUND
      );
    }
  }

  @UseGuards(FirebaseAuthGuard)
  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateMatchDto: UpdateMatchDto, @Request() req: any) {
    try {
      const match = await this.matchesService.findOne(id);
      
      // Ensure user can only update their own matches
      if (match.refereeId !== req.user.id) {
        throw new HttpException(
          {
            success: false,
            error: 'Unauthorized',
            message: 'You can only update your own matches'
          },
          HttpStatus.FORBIDDEN
        );
      }

      const updatedMatch = await this.matchesService.update(id, updateMatchDto);

      return {
        success: true,
        data: updatedMatch,
        message: 'Match updated successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to update match'
        },
        error.status || HttpStatus.BAD_REQUEST
      );
    }
  }

  @UseGuards(FirebaseAuthGuard)
  @Patch(':id/status')
  async updateStatus(
    @Param('id') id: string,
    @Body() body: { status: MatchStatus },
    @Request() req: any
  ) {
    try {
      const match = await this.matchesService.findOne(id);
      
      // Ensure user can only update their own matches
      if (match.refereeId !== req.user.id) {
        throw new HttpException(
          {
            success: false,
            error: 'Unauthorized',
            message: 'You can only update your own matches'
          },
          HttpStatus.FORBIDDEN
        );
      }

      const updatedMatch = await this.matchesService.updateStatus(id, body.status);

      return {
        success: true,
        data: updatedMatch,
        message: 'Match status updated successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to update match status'
        },
        error.status || HttpStatus.BAD_REQUEST
      );
    }
  }

  @UseGuards(FirebaseAuthGuard)
  @Delete(':id')
  async remove(@Param('id') id: string, @Request() req: any) {
    try {
      const match = await this.matchesService.findOne(id);
      
      // Ensure user can only delete their own matches
      if (match.refereeId !== req.user.id) {
        throw new HttpException(
          {
            success: false,
            error: 'Unauthorized',
            message: 'You can only delete your own matches'
          },
          HttpStatus.FORBIDDEN
        );
      }

      const deletedMatch = await this.matchesService.remove(id);

      return {
        success: true,
        data: deletedMatch,
        message: 'Match deleted successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to delete match'
        },
        error.status || HttpStatus.BAD_REQUEST
      );
    }
  }

  // Squad endpoints
  @Post(':id/squad')
  async createSquad(@Param('id') matchId: string, @Body() createSquadDto: CreateSquadDto) {
    try {
      const squad = await this.matchesService.createSquad(matchId, createSquadDto);

      return {
        success: true,
        data: squad,
        message: 'Squad created successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to create squad'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Get(':id/squad/:teamType')
  async getSquad(@Param('id') matchId: string, @Param('teamType') teamType: string) {
    try {
      const squad = await this.matchesService.getSquad(matchId, teamType.toUpperCase());

      return {
        success: true,
        data: squad,
        message: 'Squad retrieved successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to retrieve squad'
        },
        HttpStatus.NOT_FOUND
      );
    }
  }

  @Put(':id/squad/:squadId')
  async updateSquad(
    @Param('id') matchId: string,
    @Param('squadId') squadId: string,
    @Body() updateSquadDto: UpdateSquadDto
  ) {
    try {
      const squad = await this.matchesService.updateSquad(matchId, squadId, updateSquadDto);

      return {
        success: true,
        data: squad,
        message: 'Squad updated successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to update squad'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Delete(':id/squad/:squadId')
  async removeSquad(@Param('id') matchId: string, @Param('squadId') squadId: string) {
    try {
      await this.matchesService.removeSquad(matchId, squadId);

      return {
        success: true,
        message: 'Squad deleted successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to delete squad'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  // Squad officials endpoints
  @Post(':id/squad/:teamType/officials')
  async addSquadOfficial(
    @Param('id') matchId: string,
    @Param('teamType') teamType: string,
    @Body() officialData: { role: string; name: string }
  ) {
    try {
      const official = await this.matchesService.addSquadOfficial(matchId, teamType.toUpperCase(), officialData);

      return {
        success: true,
        data: official,
        message: 'Squad official added successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to add squad official'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Patch('squads/:squadId/officials/:officialId')
  async updateSquadOfficial(
    @Param('squadId') squadId: string,
    @Param('officialId') officialId: string,
    @Body() updates: { name?: string; role?: string }
  ) {
    try {
      const official = await this.matchesService.updateSquadOfficial(squadId, officialId, updates);

      return {
        success: true,
        data: official,
        message: 'Squad official updated successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to update squad official'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Delete('/squads/:squadId/officials/:officialId')
  async removeSquadOfficial(
    @Param('squadId') squadId: string,
    @Param('officialId') officialId: string
  ) {
    try {
      await this.matchesService.removeSquadOfficial(squadId, officialId);

      return {
        success: true,
        message: 'Squad official removed successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to remove squad official'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }


  // Match logs endpoints
  @UseGuards(FirebaseAuthGuard)
  @Post(':id/logs')
  async saveMatchLogs(
    @Param('id') id: string,
    @Body() body: { logs: any[] },
    @Request() req: any
  ) {
    try {
      const match = await this.matchesService.findOne(id);
      
      // Ensure user can only update their own matches
      if (match.refereeId !== req.user.id) {
        throw new HttpException(
          {
            success: false,
            error: 'Unauthorized',
            message: 'You can only update your own matches'
          },
          HttpStatus.FORBIDDEN
        );
      }

      const savedLogs = await this.matchesService.saveMatchLogs(id, body.logs);

      return {
        success: true,
        data: savedLogs,
        message: 'Match logs saved successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to save match logs'
        },
        error.status || HttpStatus.BAD_REQUEST
      );
    }
  }

  @UseGuards(FirebaseAuthGuard)
  @Get(':id/logs')
  async getMatchLogs(@Param('id') id: string, @Request() req: any) {
    try {
      const match = await this.matchesService.findOne(id);

      // Check if user is the referee (match creator) or a synced participant
      const isReferee = match.refereeId === req.user.id;
      const isParticipant = await this.matchesService.isUserParticipant(id, req.user.id);

      if (!isReferee && !isParticipant) {
        throw new HttpException(
          {
            success: false,
            error: 'Unauthorized',
            message: 'You can only access your own matches'
          },
          HttpStatus.FORBIDDEN
        );
      }

      const logs = await this.matchesService.getMatchLogs(id);

      return {
        success: true,
        data: logs,
        message: 'Match logs retrieved successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to retrieve match logs'
        },
        error.status || HttpStatus.NOT_FOUND
      );
    }
  }
}
