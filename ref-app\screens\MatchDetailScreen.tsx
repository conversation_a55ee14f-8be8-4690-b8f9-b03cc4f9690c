import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  TextInput,
  Dimensions,
  Platform,
  UIManager,
  Modal,
  Alert,
  KeyboardAvoidingView,
  Keyboard,
  TouchableWithoutFeedback, // Import this
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import Svg, { Path, Defs, ClipPath, G, Rect, Circle, Text as SvgText } from 'react-native-svg';
import Header from '../components/Header';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import MatchService from '../api/matchService';
import SquadService from '../api/squadService';
import BroadcastService, { MatchParticipant } from '../api/broadcastService';
import { CaptainIcon, GoalkeeperIcon } from '../components/icons';
import SyncManagementModal from '../components/SyncManagementModal';

// --- Unchanged Setup ---
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}
const { width: SCREEN_WIDTH } = Dimensions.get('window');
const TABS = [{ key: 'overview', label: 'Overview' }, { key: 'team', label: 'Team' }, { key: 'match', label: 'Match' }, { key: 'stats', label: 'Stats' }, { key: 'earnings', label: 'Earnings' }, { key: 'evaluate', label: 'Evaluate' }, { key: 'notes', label: 'Notes' },];

// --- DYNAMIC ICONS ---
const StripedTshirt: React.FC<{ baseColor: string; stripeColor: string; size?: number; }> = ({ baseColor, stripeColor, size = 28 }) => (
  <Svg width={size} height={size} viewBox="-1 0 19 19">
    <Defs><ClipPath id="matchDetailTshirtClip"><Path d="m15.867 7.593-1.534.967a.544.544 0 0 1-.698-.118l-.762-.957v7.256a.476.476 0 0 1-.475.475h-7.79a.476.476 0 0 1-.475-.475V7.477l-.769.965a.544.544 0 0 1-.697.118l-1.535-.967a.387.387 0 0 1-.083-.607l2.245-2.492a2.814 2.814 0 0 1 2.092-.932h.935a2.374 2.374 0 0 0 4.364 0h.934a2.816 2.816 0 0 1 2.093.933l2.24 2.49a.388.388 0 0 1-.085.608z" /></ClipPath></Defs>
    <Path d="m15.867 7.593-1.534.967a.544.544 0 0 1-.698-.118l-.762-.957v7.256a.476.476 0 0 1-.475.475h-7.79a.476.476 0 0 1-.475-.475V7.477l-.769.965a.544.544 0 0 1-.697.118l-1.535-.967a.387.387 0 0 1-.083-.607l2.245-2.492a2.814 2.814 0 0 1 2.092-.932h.935a2.374 2.374 0 0 0 4.364 0h.934a2.816 2.816 0 0 1 2.093.933l2.24 2.49a.388.388 0 0 1-.085.608z" fill={baseColor} />
    <G clipPath="url(#matchDetailTshirtClip)">{[3.5, 6, 8.5, 11, 13.5].map(x => (<Rect key={x} x={x} y="3" width="1.2" height="16" fill={stripeColor} />))}</G>
  </Svg>
);
const ShortsIcon = ({ color, size = 20 }: { color: string, size?: number }) => (<Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Path fill={color} d="M22,2H2A1,1,0,0,0,1,3V21a1,1,0,0,0,1,1H8a1,1,0,0,0,.868-.5L12,16.016l3.132,5.48A1,1,0,0,0,16,22h6a1,1,0,0,0,1-1V3A1,1,0,0,0,22,2Zm-1,9.9A5.013,5.013,0,0,1,17.1,8H21ZM21,6H13V4h8ZM3,4h8V6H3ZM3,8H6.9A5.013,5.013,0,0,1,3,11.9ZM16.58,20l-3.712-6.5a1.04,1.04,0,0,0-1.736,0L7.42,20H3V13.92A7,7,0,0,0,8.92,8H11V9a1,1,0,0,0,2,0V8h2.08A7,7,0,0,0,21,13.92V20Z" /></Svg>);
const RefereeIcon = ({ color }: { color: string }) => (<Svg height="32" width="32" viewBox="2 2 24 24"><Path d="M12.375 21.375C10.5 21.375 8.90625 20.7188 7.59375 19.4062C6.28125 18.0938 5.625 16.5 5.625 14.625C5.625 14.4187 5.63437 14.2125 5.65312 14.0062C5.67188 13.8 5.7 13.5938 5.7375 13.3875C5.64375 13.425 5.53125 13.4531 5.4 13.4719C5.26875 13.4906 5.15625 13.5 5.0625 13.5C4.275 13.5 3.60938 13.2281 3.06562 12.6844C2.52187 12.1406 2.25 11.475 2.25 10.6875C2.25 9.9 2.50781 9.23438 3.02344 8.69062C3.53906 8.14687 4.19062 7.875 4.97812 7.875C5.59687 7.875 6.15469 8.04844 6.65156 8.39531C7.14844 8.74219 7.5 9.1875 7.70625 9.73125C8.325 9.16875 9.03281 8.71875 9.82969 8.38125C10.6266 8.04375 11.475 7.875 12.375 7.875H24.75V12.375H19.125V14.625C19.125 16.5 18.4688 18.0938 17.1562 19.4062C15.8438 20.7188 14.25 21.375 12.375 21.375ZM5.0625 11.8125C5.38125 11.8125 5.64844 11.7047 5.86406 11.4891C6.07969 11.2734 6.1875 11.0062 6.1875 10.6875C6.1875 10.3687 6.07969 10.1016 5.86406 9.88594C5.64844 9.67031 5.38125 9.5625 5.0625 9.5625C4.74375 9.5625 4.47656 9.67031 4.26094 9.88594C4.04531 10.1016 3.9375 10.3687 3.9375 10.6875C3.9375 11.0062 4.04531 11.2734 4.26094 11.4891C4.47656 11.7047 4.74375 11.8125 5.0625 11.8125ZM12.375 18.5625C13.4625 18.5625 14.3906 18.1781 15.1594 17.4094C15.9281 16.6406 16.3125 15.7125 16.3125 14.625C16.3125 13.5375 15.9281 12.6094 15.1594 11.8406C14.3906 11.0719 13.4625 10.6875 12.375 10.6875C11.2875 10.6875 10.3594 11.0719 9.59062 11.8406C8.82187 12.6094 8.4375 13.5375 8.4375 14.625C8.4375 15.7125 8.82187 16.6406 9.59062 17.4094C10.3594 18.1781 11.2875 18.5625 12.375 18.5625Z" fill={color} /></Svg>);
const AssistantIcon = ({ color }: { color: string }) => (<Svg height="28" width="28" viewBox="0 0 24 24"><Path d="M9 6H11V4H9V6ZM13 6V4H15V6H13ZM9 14V12H11V14H9ZM17 10V8H19V10H17ZM17 14V12H19V14H17ZM13 14V12H15V14H13ZM17 6V4H19V6H17ZM11 8V6H13V8H11ZM5 20V4H7V6H9V8H7V10H9V12H7V20H5ZM15 12V10H17V12H15ZM11 12V10H13V12H11ZM9 10V8H11V10H9ZM13 10V8H15V10H13ZM15 8V6H17V8H15Z" fill={color} /></Svg>);
const FourthOfficialIcon = ({ color }: { color: string }) => (
  <Svg width="24" height="24" viewBox="0 0 24 24">
    <Rect x="2" y="2" width="20" height="20" rx="4" stroke={color} strokeWidth="1.5" fill="none" />
    <SvgText x="12" y="16" textAnchor="middle" fontSize="12" fontWeight="600" fill={color}>4th</SvgText>
  </Svg>
);
const VarIcon = ({ color, size = 24 }: { color: string; size?: number }) => (<Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7z" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" /><Path d="M12 15a3 3 0 100-6 3 3 0 000 6z" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" /></Svg>);
const UserIcon = ({ color, size = 20 }: { color: string; size?: number }) => (<Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Path d="M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /><Circle cx="12" cy="7" r="4" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /></Svg>);

// --- Helper Components ---
const CountdownItem = ({ value, label }: { value: string; label: string }) => (<View style={getStyles(useTheme().theme, useTheme().isDarkMode).countdownItem}><Text style={getStyles(useTheme().theme, useTheme().isDarkMode).countdownValue}>{value}</Text><Text style={getStyles(useTheme().theme, useTheme().isDarkMode).countdownLabel}>{label}</Text></View>);
const DetailRow = ({ label, value }: { label: string; value: string }) => (<View style={getStyles(useTheme().theme, useTheme().isDarkMode).detailRow}><Text style={getStyles(useTheme().theme, useTheme().isDarkMode).detailLabel}>{label}</Text><View style={getStyles(useTheme().theme, useTheme().isDarkMode).valueContainer}><Text style={getStyles(useTheme().theme, useTheme().isDarkMode).detailValue}>{value}</Text></View></View>);
const OfficialRow = ({ icon, name, onSync, label, status, statusColor }: { icon: React.ReactNode; name: string; onSync?: () => void; label?: string; status?: string; statusColor?: string }) => {
  const { theme } = useTheme();
  const styles = getStyles(theme, useTheme().isDarkMode);
  return (
    <View style={styles.officialRow}>
      <View style={styles.officialIconContainer}>
        {icon}
        {label && <Text style={styles.officialIconLabel}>{label}</Text>}
      </View>
      <View style={styles.officialInfo}>
        <Text style={styles.officialName}>{name}</Text>
        {status && (
          <View style={[styles.statusBadge, { backgroundColor: statusColor || theme.textSecondary }]}>
            <Text style={styles.statusText}>{status}</Text>
          </View>
        )}
      </View>
      {onSync && (
        <TouchableOpacity style={styles.syncButton} onPress={onSync}>
          <Text style={styles.syncButtonText}>Sync now</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

// --- Content Components ---
const OverviewContent = ({ styles, matchData, matchId, onDataUpdate, onOpenSyncModal, participants = [], user }: { styles: any; matchData: any; matchId: string; onDataUpdate: () => void; onOpenSyncModal?: () => void; participants?: MatchParticipant[]; user?: any }) => {
  const { theme } = useTheme();
  const [homeOfficials, setHomeOfficials] = useState<any[]>([]);
  const [awayOfficials, setAwayOfficials] = useState<any[]>([]);
  const [countdown, setCountdown] = useState({ days: 0, hours: 0, minutes: 0 });
  const [editingOfficial, setEditingOfficial] = useState<{ squadId: string; officialId: string; name: string; role: string; editingField: 'name' | 'role' } | null>(null);
  const overviewScrollRef = useRef<ScrollView>(null);

  // Load team officials from database
  const loadTeamOfficials = async () => {
    if (!matchId) return;

    try {
      const homeResponse = await SquadService.getSquadByMatch(matchId, 'home');
      const awayResponse = await SquadService.getSquadByMatch(matchId, 'away');

      if (homeResponse.success && homeResponse.data?.officials) {
        setHomeOfficials(homeResponse.data.officials);
      } else {
        setHomeOfficials([]);
      }

      if (awayResponse.success && awayResponse.data?.officials) {
        setAwayOfficials(awayResponse.data.officials);
      } else {
        setAwayOfficials([]);
      }
    } catch (error) {
      console.error('Error loading team officials:', error);
      setHomeOfficials([]);
      setAwayOfficials([]);
    }
  };

  // Add new team official
  const addTeamOfficial = async (teamType: 'home' | 'away') => {
    if (!matchId) return;

    try {
      const response = await SquadService.addSquadOfficial(matchId, teamType, {
        role: 'Manager',
        name: 'New Official'
      });

      if (response.success && response.data) {
        // Add to local state instead of reloading everything
        const newOfficial = {
          ...response.data,
          squadId: (response.data as any).squadId || ''
        };

        if (teamType === 'home') {
          setHomeOfficials(prev => [...prev, newOfficial]);
        } else {
          setAwayOfficials(prev => [...prev, newOfficial]);
        }
      } else {
        Alert.alert('Error', response.error || 'Failed to add official');
      }
    } catch (error) {
      console.error('Error adding team official:', error);
      Alert.alert('Error', 'Failed to add official');
    }
  };

  // Update team official
  const updateOfficial = async (squadId: string, officialId: string, updates: { name?: string; role?: string }) => {
    const trimmedUpdates = {
      ...(updates.name && { name: updates.name.trim() }),
      ...(updates.role && { role: updates.role.trim() })
    };
    
    if (Object.keys(trimmedUpdates).length === 0) {
      setEditingOfficial(null);
      return;
    }
    
    try {
      const response = await SquadService.updateSquadOfficial(squadId, officialId, trimmedUpdates);
      
      if (response.success) {
        // Update local state instead of reloading everything
        setHomeOfficials(prev => prev.map(official => 
          official.id === officialId ? { ...official, ...trimmedUpdates } : official
        ));
        setAwayOfficials(prev => prev.map(official => 
          official.id === officialId ? { ...official, ...trimmedUpdates } : official
        ));
        setEditingOfficial(null);
      } else {
        Alert.alert('Error', response.error || 'Failed to update official');
        setEditingOfficial(null);
      }
    } catch (error) {
      console.error('Error updating team official:', error);
      Alert.alert('Error', 'Failed to update official');
      setEditingOfficial(null);
    }
  };

  // Load officials when component mounts
  useEffect(() => {
    loadTeamOfficials();
  }, [matchId]);



  const TeamOfficialRow = React.memo(({ kit, official, squadId, onEdit, rowIndex }: { kit: React.ReactNode; official: any; squadId: string; onEdit: (squadId: string, officialId: string, name: string, role: string, field: 'name' | 'role') => void; rowIndex: number }) => {
    const isEditingName = editingOfficial?.squadId === squadId && editingOfficial?.officialId === official.id && editingOfficial?.editingField === 'name';
    const isEditingRole = editingOfficial?.squadId === squadId && editingOfficial?.officialId === official.id && editingOfficial?.editingField === 'role';
    const rowRef = useRef<View>(null);
    
    const handleSave = () => {
      if (!editingOfficial) return;
      
      const updates: { name?: string; role?: string } = {};
      if (editingOfficial.editingField === 'name') {
        updates.name = editingOfficial.name;
      } else {
        updates.role = editingOfficial.role;
      }
      
      updateOfficial(squadId, official.id, updates);
    };

    const handleEdit = (field: 'name' | 'role') => {
      onEdit(squadId, official.id, official.name, official.role, field);
      
      // Scroll to bring the field into view
      setTimeout(() => {
        if (rowRef.current && overviewScrollRef.current) {
          rowRef.current.measureInWindow((x, y, width, height) => {
            const screenHeight = Dimensions.get('window').height;
            const keyboardHeight = 300;
            const targetY = y - (screenHeight - keyboardHeight) / 2;
            
            overviewScrollRef.current?.scrollTo({
              y: Math.max(0, targetY),
              animated: true
            });
          });
        }
      }, 100);
    };
    
    return (
      <View ref={rowRef} style={styles.teamOfficialRow}>
        <View style={styles.teamOfficialKitContainer}>{kit}</View>
        <View style={styles.teamOfficialInfoContainer}>
          {isEditingName ? (
            <TextInput
              key={`name-${official.id}`}
              style={styles.editingInput}
              value={editingOfficial?.name || ''}
              onChangeText={(text) => setEditingOfficial(prev => prev ? { ...prev, name: text } : null)}
              onBlur={handleSave}
              onSubmitEditing={handleSave}
              autoFocus
              returnKeyType="done"
              blurOnSubmit={true}
              placeholder="Official name"
            />
          ) : (
            <TouchableOpacity onPress={() => handleEdit('name')}>
              <Text style={styles.teamOfficialManagerName}>{official.name}</Text>
            </TouchableOpacity>
          )}
          
          {isEditingRole ? (
            <TextInput
              key={`role-${official.id}`}
              style={[styles.editingInput, { marginTop: 4 }]}
              value={editingOfficial?.role || ''}
              onChangeText={(text) => setEditingOfficial(prev => prev ? { ...prev, role: text } : null)}
              onBlur={handleSave}
              onSubmitEditing={handleSave}
              autoFocus
              returnKeyType="done"
              blurOnSubmit={true}
              placeholder="Official role"
            />
          ) : (
            <TouchableOpacity onPress={() => handleEdit('role')}>
              <Text style={styles.teamOfficialRole}>{official.role}</Text>
            </TouchableOpacity>
          )}
        </View>
        <TouchableOpacity style={styles.syncButton} onPress={() => {}}>
          <Text style={styles.syncButtonText}>Sync now</Text>
        </TouchableOpacity>
      </View>
    );
  });

  const calculateCountdown = () => {
    if (!matchData?.matchDate) {
      setCountdown({ days: 0, hours: 0, minutes: 0 });
      return;
    }

    try {
      const matchDate = new Date(matchData.matchDate);
      const now = new Date();
      const timeDiff = matchDate.getTime() - now.getTime();

      if (timeDiff <= 0) {
        setCountdown({ days: 0, hours: 0, minutes: 0 });
        return;
      }

      const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

      setCountdown({ days, hours, minutes });
    } catch (error) {
      console.error('Error calculating countdown:', error);
      setCountdown({ days: 0, hours: 0, minutes: 0 });
    }
  };

  useEffect(() => {
    calculateCountdown();
    const interval = setInterval(calculateCountdown, 60000);
    return () => clearInterval(interval);
  }, [matchData?.matchDate]);

  const formatMatchDateTime = () => {
    if (!matchData?.matchDate) return 'TBD';
    const date = new Date(matchData.matchDate);
    return date.toLocaleDateString('en-GB', {
      weekday: 'short',
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }) + '   ' + date.toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <View>
      <Text style={styles.countdownTitle}>The Match Kicks off in</Text>
      <View style={styles.countdownContainer}><CountdownItem value={countdown.days.toString()} label="Days" /><View style={styles.countdownSeparator} /><CountdownItem value={countdown.hours.toString()} label="Hours" /><View style={styles.countdownSeparator} /><CountdownItem value={countdown.minutes.toString()} label="Mins" /></View>
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Match details</Text>
        <DetailRow label="Kick-off" value={formatMatchDateTime()} />
        <DetailRow label="Competition" value={matchData?.competition || 'TBD'} />
        <DetailRow label="Venue" value={matchData?.venue || 'TBD'} />
        <DetailRow label="Official Role" value={matchData?.officialRole || 'Referee'} />
      </View>
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Match Officials</Text>

        {/* Show the referee (match creator) */}
        <OfficialRow
          icon={<RefereeIcon color={theme.primary} />}
          name={
            matchData?.referee?.id === user?.uid
              ? `You (Referee)`
              : `${matchData?.referee?.name || 'Referee'}`
          }
          status={matchData?.referee?.id === user?.uid ? undefined : 'Match Creator'}
          onSync={matchData?.referee?.id === user?.uid ? onOpenSyncModal : undefined}
        />

        {/* Show synced participants */}
        {participants.map((participant) => {
          const getOfficialIcon = (role: string) => {
            switch (role.toLowerCase()) {
              case 'ar1':
                return <AssistantIcon color={theme.primary} />;
              case 'ar2':
                return <AssistantIcon color={theme.primary} />;
              case '4th':
                return <FourthOfficialIcon color={theme.text} />;
              default:
                return <RefereeIcon color={theme.primary} />;
            }
          };

          const getRoleDisplayName = (role: string) => {
            switch (role) {
              case 'AR1': return 'Ar1';
              case 'AR2': return 'Assistant Referee 2';
              case '4th': return '4th Official';
              default: return role;
            }
          };

          const statusColor = participant.status === 'joined' ? '#4CAF50' : '#FF9800';
          const statusText = participant.status === 'joined' ? 'Active' : 'Pending';

          return (
            <OfficialRow
              key={participant.id}
              icon={getOfficialIcon(participant.role)}
              name={participant.user.displayName || participant.user.name}
              label={getRoleDisplayName(participant.role)}
              status={statusText}
              statusColor={statusColor}
              onSync={onOpenSyncModal}
            />
          );
        })}

        {/* Show original match officials if any (excluding referee) */}
        {matchData?.matchOfficials && matchData.matchOfficials.length > 0 && (
          matchData.matchOfficials
            .filter((official: any) => official.role?.toLowerCase() !== 'referee')
            .map((official: any, index: number) => {
              const getOfficialIcon = (role: string) => {
                switch (role.toLowerCase()) {
                  case 'ar1':
                  case 'ar 1':
                    return <AssistantIcon color={theme.primary} />;
                  case 'ar2':
                  case 'ar 2':
                    return <AssistantIcon color={theme.primary} />;
                  case '4th':
                  case 'fourth':
                    return <FourthOfficialIcon color={theme.text} />;
                  case 'var':
                    return <VarIcon color={theme.primary} />;
                  default:
                    return <RefereeIcon color={theme.primary} />;
                }
              };

              const role = official.role || '';
              const roleLowerCase = role.toLowerCase();
              const isFourthOfficial = roleLowerCase.includes('4th') || roleLowerCase.includes('fourth');
              const label = !isFourthOfficial ? role : undefined;

              return (
                <OfficialRow
                  key={`original-${index}`}
                  icon={getOfficialIcon(role)}
                  name={official.name || '-----'}
                  label={label}
                  onSync={official.name !== '-----' ? onOpenSyncModal : undefined}
                />
              );
            })
        )}
      </View>
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Team Officials</Text>
        <View style={styles.teamSectionWrapper}>
          <View style={styles.teamSectionHeader}>
            <View style={styles.teamTag}>
              <Text style={styles.teamTagText}>{matchData?.homeTeam?.shortName || matchData?.homeTeam?.name || 'Home'} Team</Text>
            </View>
            <TouchableOpacity onPress={() => addTeamOfficial('home')} style={styles.addButton}>
              <UserIcon color={theme.primary} size={20} />
            </TouchableOpacity>
          </View>
          {homeOfficials.length > 0 ? homeOfficials.map((official, index) => (
            <TeamOfficialRow
              key={official.id}
              kit={
                <>
                  <StripedTshirt
                    baseColor={matchData?.homeTeam?.kit?.base || "#DA291C"}
                    stripeColor={matchData?.homeTeam?.kit?.stripe || "#000"}
                    size={28}
                  />
                  <ShortsIcon color={matchData?.homeTeam?.kit?.shorts || "#000"} size={16} />
                </>
              }
              official={official}
              squadId={official.squadId}
              onEdit={(squadId, officialId, name, role, field) => setEditingOfficial({ squadId, officialId, name, role, editingField: field })}
              rowIndex={index}
            />
          )) : (
            <View style={styles.emptyStateContainer}>
              <Text style={styles.emptyStateText}>No team officials added yet</Text>
              <Text style={styles.emptyStateSubtext}>Tap the + button to add officials</Text>
            </View>
          )}
        </View>
        <View style={styles.teamSectionWrapper}>
          <View style={styles.teamSectionHeader}>
            <View style={styles.teamTag}>
              <Text style={styles.teamTagText}>{matchData?.awayTeam?.shortName || matchData?.awayTeam?.name || 'Away'} Team</Text>
            </View>
            <TouchableOpacity onPress={() => addTeamOfficial('away')} style={styles.addButton}>
              <UserIcon color={theme.primary} size={20} />
            </TouchableOpacity>
          </View>
          {awayOfficials.length > 0 ? awayOfficials.map((official, index) => (
            <TeamOfficialRow
              key={official.id}
              kit={
                <>
                  <StripedTshirt
                    baseColor={matchData?.awayTeam?.kit?.base || "#004193"}
                    stripeColor={matchData?.awayTeam?.kit?.stripe || "#E30613"}
                    size={28}
                  />
                  <ShortsIcon color={matchData?.awayTeam?.kit?.shorts || "#000"} size={16} />
                </>
              }
              official={official}
              squadId={official.squadId}
              onEdit={(squadId, officialId, name, role, field) => setEditingOfficial({ squadId, officialId, name, role, editingField: field })}
              rowIndex={homeOfficials.length + index}
            />
          )) : (
            <View style={styles.emptyStateContainer}>
              <Text style={styles.emptyStateText}>No team officials added yet</Text>
              <Text style={styles.emptyStateSubtext}>Tap the + button to add officials</Text>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};
const EarningInputRow = ({ label, value, setValue, placeholder, keyboardType = 'default', theme, styles, scrollViewRef, index }: {
  label: string;
  value: string;
  setValue: (text: string) => void;
  placeholder: string;
  keyboardType?: any;
  theme: any;
  styles: any;
  scrollViewRef?: React.RefObject<ScrollView | null>;
  index?: number;
}) => {
  const inputRef = useRef<TextInput>(null);

  const handleFocus = () => {
    // Delay to ensure keyboard is showing
    setTimeout(() => {
      if (scrollViewRef?.current && index !== undefined) {
        // Get screen dimensions
        const screenHeight = Dimensions.get('window').height;
        const keyboardHeight = Platform.OS === 'ios' ? 300 : 280;
        const headerHeight = 120; // Header + tabs height
        const availableHeight = screenHeight - keyboardHeight - headerHeight;

        // Calculate input position
        const inputHeight = 80;
        const inputPosition = index * inputHeight;
        const buffer = 50; // Extra space above the input

        // Simple approach: scroll the field up above the keyboard
        // For Travel Mileage (index 3): scroll up to bring it above keyboard
        const scrollOffset = index * 60; // Each field needs about 60px scroll up

        // Always scroll to bring the field into view above keyboard
        scrollViewRef.current.scrollTo({
          y: scrollOffset,
          animated: true
        });
      }
    }, 300);
  };

  return (
    <View style={styles.inputRow}>
      <Text style={styles.inputLabel}>{label}</Text>
      <TextInput
        ref={inputRef}
        style={styles.inputValue}
        value={value}
        onChangeText={setValue}
        placeholder={placeholder}
        placeholderTextColor={theme.textSecondary}
        keyboardType={keyboardType}
        multiline={false}
        returnKeyType="done"
        autoCorrect={false}
        onFocus={handleFocus}
      />
    </View>
  );
};

// --- FIXED COMPONENT ---
const EarningsContent = ({ styles, matchData, matchId, onDataUpdate }: { styles: any; matchData: any; matchId: string; onDataUpdate: () => void }) => {
  const { theme } = useTheme();
  const [fees, setFees] = useState(matchData?.earnings?.fees || '');
  const [expenses, setExpenses] = useState(matchData?.earnings?.expenses || '');
  const [travelTime, setTravelTime] = useState(matchData?.earnings?.travelTime || '');
  const [travelMileage, setTravelMileage] = useState(matchData?.earnings?.mileage || '');
  const [isSaving, setIsSaving] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    setFees(matchData?.earnings?.fees || '');
    setExpenses(matchData?.earnings?.expenses || '');
    setTravelTime(matchData?.earnings?.travelTime || '');
    setTravelMileage(matchData?.earnings?.mileage || '');
  }, [matchData]);


  const handleSaveEarnings = async () => {
    if (!matchId) return;

    try {
      setIsSaving(true);
      const updateData = {
        fees,
        expenses,
        mileage: travelMileage,
        travelTime,
      };

      const response = await MatchService.updateMatch(matchId, updateData);

      if (response.success) {
        Alert.alert('Success', 'Earnings saved successfully');
        onDataUpdate();
      } else {
        Alert.alert('Error', response.error || 'Failed to save earnings');
      }
    } catch (error) {
      console.error('Error saving earnings:', error);
      Alert.alert('Error', 'Failed to save earnings');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0} // Offset for the header
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <ScrollView
          ref={scrollViewRef}
          style={styles.earningsContainer}
          contentContainerStyle={{ paddingBottom: 40 }} // Remove large static padding
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <EarningInputRow label="Fees" value={fees} setValue={setFees} placeholder="$0" keyboardType="default" theme={theme} styles={styles} scrollViewRef={scrollViewRef} index={0} />
          <EarningInputRow label="Expenses" value={expenses} setValue={setExpenses} placeholder="$0" keyboardType="default" theme={theme} styles={styles} scrollViewRef={scrollViewRef} index={1} />
          <EarningInputRow label="Travel Time" value={travelTime} setValue={setTravelTime} placeholder="0h 0m" keyboardType="default" theme={theme} styles={styles} scrollViewRef={scrollViewRef} index={2} />
          <EarningInputRow label="Travel Mileage" value={travelMileage} setValue={setTravelMileage} placeholder="0 KM" keyboardType="default" theme={theme} styles={styles} scrollViewRef={scrollViewRef} index={3} />
          <TouchableOpacity style={styles.inputRow}>
            <Text style={styles.inputLabel}>Attach Receipt <Text style={{ fontWeight: 'normal' }}>(optional)</Text></Text>
            <MaterialIcons name="attachment" size={22} color={theme.textSecondary} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.primaryButton, { marginTop: 8 }, isSaving && { opacity: 0.6 }]}
            onPress={handleSaveEarnings}
            disabled={isSaving}
          >
            <Text style={styles.primaryButtonText}>
              {isSaving ? 'Saving...' : 'Save Earnings'}
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};
const NotesContent = ({ styles, matchData, matchId, onDataUpdate }: { styles: any; matchData: any; matchId: string; onDataUpdate: () => void }) => {
  const { theme } = useTheme();
  const [note, setNote] = useState(matchData?.notes || '');
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    setNote(matchData?.notes || '');
  }, [matchData]);

  const handleSaveNotes = async () => {
    if (!matchId) return;

    try {
      setIsSaving(true);

      const response = await MatchService.updateMatch(matchId, { notes: note });

      if (response.success) {
        Alert.alert('Success', 'Notes saved successfully');
        onDataUpdate();
      } else {
        Alert.alert('Error', response.error || 'Failed to save notes');
      }
    } catch (error) {
      console.error('Error saving notes:', error);
      Alert.alert('Error', 'Failed to save notes');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <View style={styles.notesContainer}>
      <View style={styles.notesCard}>
        <TextInput
          style={styles.notesTextInput}
          multiline
          placeholder="Add your notes here..."
          placeholderTextColor={theme.textSecondary}
          value={note}
          onChangeText={setNote}
        />
      </View>
      <TouchableOpacity
        style={[styles.primaryButton, { marginTop: 16 }, isSaving && { opacity: 0.6 }]}
        onPress={handleSaveNotes}
        disabled={isSaving}
      >
        <Text style={styles.primaryButtonText}>
          {isSaving ? 'Saving...' : 'Save Notes'}
        </Text>
      </TouchableOpacity>
    </View>
  );
};




// --- MAIN COMPONENT ---
const MatchDetailScreen = ({ route }: { route: any }) => {
  const navigation = useNavigation();
  const { theme, isDarkMode } = useTheme();
  const { user } = useAuth();
  const styles = getStyles(theme, isDarkMode);

  const matchId = route?.params?.matchId;

  const [matchData, setMatchData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [homeSquad, setHomeSquad] = useState<any>(null);
  const [awaySquad, setAwaySquad] = useState<any>(null);
  const [squadValidation, setSquadValidation] = useState({ home: false, away: false });

  // Load squad data for validation
  const [squadValidationLoading, setSquadValidationLoading] = useState(false);

  const loadSquadValidation = async () => {
    if (!matchId || squadValidationLoading) return;

    try {
      setSquadValidationLoading(true);
      const [homeResponse, awayResponse] = await Promise.all([
        SquadService.getSquadByMatch(matchId, 'home'),
        SquadService.getSquadByMatch(matchId, 'away')
      ]);

      const homeValid = !!(homeResponse.success && homeResponse.data?.players && homeResponse.data.players.length > 0);
      const awayValid = !!(awayResponse.success && awayResponse.data?.players && awayResponse.data.players.length > 0);

      setSquadValidation({ home: homeValid, away: awayValid });
      setHomeSquad(homeResponse.data);
      setAwaySquad(awayResponse.data);
    } catch (error) {
      console.error('Error loading squad validation:', error);
      setSquadValidation({ home: false, away: false });
    } finally {
      setSquadValidationLoading(false);
    }
  };

  // Load squad validation when component mounts
  useEffect(() => {
    loadSquadValidation();
  }, [matchId]);

  const [homeSquadData, setHomeSquadData] = useState<any>(null);
  const [awaySquadData, setAwaySquadData] = useState<any>(null);
  const [squadLoading, setSquadLoading] = useState(false);

  const [showDropdown, setShowDropdown] = useState(false);

  const [activeTab, setActiveTab] = useState('overview');
  const [activeTeamView, setActiveTeamView] = useState('Home');
  const scrollViewRef = useRef<ScrollView>(null);
  const [tabLayouts, setTabLayouts] = useState<Record<number, { x: number, width: number }>>({});
  const activeTabIndex = TABS.findIndex(tab => tab.key === activeTab);

  const [fetchingMatchData, setFetchingMatchData] = useState(false);
  const [showSyncModal, setShowSyncModal] = useState(false);
  const [participants, setParticipants] = useState<MatchParticipant[]>([]);

  const handleOpenSyncModal = () => {
    setShowSyncModal(true);
  };

  // Load match participants
  const loadParticipants = async () => {
    if (!matchId) return;

    try {
      const response = await BroadcastService.getMatchParticipants(matchId);
      if (response.success && response.data) {
        setParticipants(response.data);
      }
    } catch (error) {
      console.error('❌ Failed to load participants:', error);
    }
  };



  const fetchMatchData = async () => {
    if (!matchId) {
      setError('No match ID provided');
      setLoading(false);
      return;
    }

    // Prevent duplicate calls if already fetching
    if (fetchingMatchData) return;

    try {
      setFetchingMatchData(true);
      setLoading(true);
      const response = await MatchService.getMatchById(matchId);

      if (response.success && response.data) {
        const formattedData = MatchService.convertApiResponseToDetailFormat(response.data);
        setMatchData(formattedData);
        setError(null);
      } else {
        setError(response.error || 'Failed to fetch match data');
      }
    } catch (err: any) {
      console.error('Error fetching match data:', err);
      setError('Failed to load match details');
    } finally {
      setLoading(false);
      setFetchingMatchData(false);
    }
  };

  useEffect(() => {
    fetchMatchData();
    loadParticipants();
  }, [matchId]);

  // Load squad data when component mounts
  useEffect(() => {
    if (matchId) {
      fetchSquadData('home');
      fetchSquadData('away');
    }
  }, [matchId]);

  const fetchSquadData = async (teamType: 'home' | 'away') => {
    if (!matchId) return;

    try {
      setSquadLoading(true);
      const response = await SquadService.getSquadByMatch(matchId, teamType);

      if (response.success && response.data) {
        const squadData = SquadService.convertFromApiFormat(response.data);
        if (teamType === 'home') {
          setHomeSquadData(squadData);
        } else {
          setAwaySquadData(squadData);
        }
      } else {
        console.log(`No squad data found for ${teamType} team - this is expected for new matches`);
      }
    } catch (error) {
      console.log(`No squad data found for ${teamType} team - this is expected for new matches`);
    } finally {
      setSquadLoading(false);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      if (matchId) {
        // Only reload squad data on focus, as match data doesn't change frequently
        // and fetchMatchData is already called in useEffect
        loadSquadValidation();
        // Also fetch the actual squad data for display
        fetchSquadData('home');
        fetchSquadData('away');
      }
    }, [matchId])
  );

  useEffect(() => { if (tabLayouts[activeTabIndex]) { const { x, width } = tabLayouts[activeTabIndex]; const scrollToX = x - SCREEN_WIDTH / 2 + width / 2; scrollViewRef.current?.scrollTo({ x: scrollToX, animated: true }); } }, [activeTabIndex, tabLayouts]);

  const handleEditMatch = () => {
    setShowDropdown(false);
    
    // Serialize the matchData to avoid navigation warnings
    const serializedMatchData = {
      ...matchData,
      matchDate: matchData?.matchDate ? matchData.matchDate.toISOString() : null,
    };
    
    (navigation as any).navigate('CreateMatch', {
      editMode: true,
      matchData: serializedMatchData,
      matchId: matchId
    });
  };

  const handleEditHomeTeam = () => {
    setShowDropdown(false);
    handleNavigateToSquadSheet('home');
  };

  const handleEditAwayTeam = () => {
    setShowDropdown(false);
    handleNavigateToSquadSheet('away');
  };

  const handleMarkAsEnded = async () => {
    setShowDropdown(false);

    Alert.alert(
      'Mark Match as Ended',
      'Are you sure you want to mark this match as completed? This will move it to previous matches.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Mark as Ended',
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await MatchService.updateMatchStatus(matchId, 'COMPLETED');
              if (response.success) {
                Alert.alert('Success', 'Match marked as completed', [
                  {
                    text: 'OK',
                    onPress: () => {
                      navigation.goBack();
                    }
                  }
                ]);
              } else {
                Alert.alert('Error', response.error || 'Failed to update match status');
              }
            } catch (error) {
              console.error('Error updating match status:', error);
              Alert.alert('Error', 'Failed to update match status');
            }
          }
        }
      ]
    );
  };

  const handleDuplicateMatch = () => {
    setShowDropdown(false);
    const duplicateData = { ...matchData };
    delete duplicateData.id;
    
    // Serialize the matchData to avoid navigation warnings
    const serializedDuplicateData = {
      ...duplicateData,
      matchDate: duplicateData?.matchDate ? duplicateData.matchDate.toISOString() : null,
    };
    
    (navigation as any).navigate('CreateMatch', {
      duplicateMode: true,
      matchData: serializedDuplicateData
    });
  };

  const handleDeleteMatch = () => {
    setShowDropdown(false);

    Alert.alert(
      'Delete Match',
      'Are you sure you want to delete this match? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await MatchService.deleteMatch(matchId);
              if (response.success) {
                Alert.alert('Success', 'Match deleted successfully', [
                  { text: 'OK', onPress: () => navigation.goBack() }
                ]);
              } else {
                Alert.alert('Error', response.error || 'Failed to delete match');
              }
            } catch (error) {
              console.error('Error deleting match:', error);
              Alert.alert('Error', 'Failed to delete match');
            }
          }
        }
      ]
    );
  };

  const handleNavigateToSquadSheet = (teamType: 'home' | 'away') => {
    const teamData = teamType === 'home' ? matchData?.homeTeam : matchData?.awayTeam;
    const teamName = `${teamData?.name || teamType} Squad Sheet`;

    (navigation as any).navigate('SquadSheet', {
      teamData: teamData,
      teamName: teamName,
      teamType: teamType,
      matchId: matchId,
      matchData: matchData  // Add matchData to pass teamSize and other template settings
    });
  };

  const TeamDisplay = ({ kit, name }: { kit: { base: string; stripe: string, shorts: string }, name: string }) => (<View style={styles.teamDisplayContainer}><StripedTshirt baseColor={kit.base} stripeColor={kit.stripe} size={48} /><ShortsIcon color={kit.shorts} size={26} /><Text style={[styles.teamName, { color: kit.base }]}>{name}</Text></View>);
  const IconBoxButton = ({ iconName }: { iconName: React.ComponentProps<typeof MaterialIcons>['name'] }) => (<TouchableOpacity style={styles.iconButton}><MaterialIcons name={iconName} size={28} color={theme.primary} /></TouchableOpacity>);

  const SquadDisplay = ({ squadData, teamType }: { squadData: any; teamType: 'home' | 'away' }) => {
    if (!squadData) return null;

    const { starters, substitutes, officials } = squadData;

    const renderPlayerRow = (player: { id: string; number: number; name: string; isCaptain: boolean; isGoalkeeper: boolean }, listType: string) => (
      <View key={player.id} style={styles.squadPlayerRow}>
        <Text style={styles.squadPlayerNumber}>{player.number}</Text>
        <Text style={styles.squadPlayerName}>{player.name}</Text>
        <View style={styles.squadPlayerIcons}>
          {player.isCaptain && (
            <View style={{ marginLeft: 10, transform: [{ translateY: 4 }] }}>
              <CaptainIcon size={38} />
            </View>
          )}
          {player.isGoalkeeper && (
            <View style={{ marginLeft: 15 }}>
              <GoalkeeperIcon size={23} color={theme.text} />
            </View>
          )}
        </View>
      </View>
    );

    const renderOfficialRow = (official: { id: string; role: string; name: string }) => (
      <View key={official.id} style={styles.squadOfficialRow}>
        <Text style={styles.squadOfficialRole}>{official.role}</Text>
        <Text style={styles.squadOfficialName}>{official.name}</Text>
      </View>
    );

    return (
      <ScrollView style={styles.squadDisplayContainer}>
        <View style={styles.squadDisplayCard}>
          <View style={styles.squadSectionHeaderFull}>
            <Text style={styles.squadSectionTitleFull}>Starting line up</Text>
          </View>
          {starters.map((player: any) => renderPlayerRow(player, 'starters'))}
        </View>
        <View style={styles.squadDisplayCard}>
          <View style={styles.squadSectionHeaderFull}>
            <Text style={styles.squadSectionTitleFull}>Substitutes</Text>
          </View>
          {substitutes.map((player: any) => renderPlayerRow(player, 'substitutes'))}
        </View>
        <View style={styles.squadDisplayCard}>
          <View style={styles.squadSectionHeaderFull}>
            <Text style={styles.squadSectionTitleFull}>Team Officials</Text>
          </View>
          {officials.map((official: any) => renderOfficialRow(official))}
        </View>
      </ScrollView>
    );
  };

  const renderContent = () => {
    if (activeTab === 'earnings') { return <EarningsContent styles={styles} matchData={matchData} matchId={matchId} onDataUpdate={fetchMatchData} />; }
    if (activeTab === 'notes') { return <NotesContent styles={styles} matchData={matchData} matchId={matchId} onDataUpdate={fetchMatchData} />; }
    if (activeTab === 'overview') { return <OverviewContent styles={styles} matchData={matchData} matchId={matchId} onDataUpdate={fetchMatchData} onOpenSyncModal={handleOpenSyncModal} participants={participants} user={user} />; }
    if (activeTab === 'team') {
      return (
        <>
          <View style={styles.homeAwayToggle}>
            <TouchableOpacity
              style={[styles.toggleButton, activeTeamView === 'Home' && styles.activeToggleButton]}
              onPress={() => setActiveTeamView('Home')}
            >
              <Text style={[styles.toggleButtonText, activeTeamView === 'Home' && styles.activeToggleButtonText]}>HOME</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.toggleButton, activeTeamView === 'Away' && styles.activeToggleButton]}
              onPress={() => setActiveTeamView('Away')}
            >
              <Text style={[styles.toggleButtonText, activeTeamView === 'Away' && styles.activeToggleButtonText]}>AWAY</Text>
            </TouchableOpacity>
          </View>
          {activeTeamView === 'Home' && (
            <>
              {homeSquadData && (homeSquadData.starters?.length > 0 || homeSquadData.substitutes?.length > 0 || homeSquadData.officials?.length > 0) ? (
                <SquadDisplay squadData={homeSquadData} teamType="home" />
              ) : (
                <>
                  <View style={styles.card}>
                    <Text style={styles.cardTitle}>Add Squad Sheet</Text>
                    <View style={styles.iconButtonsRow}>
                      <IconBoxButton iconName="file-upload" />
                      <IconBoxButton iconName="image" />
                      <IconBoxButton iconName="photo-camera" />
                    </View>
                    <TouchableOpacity
                      style={[styles.primaryButton, styles.edgeInset]}
                      onPress={() => handleNavigateToSquadSheet('home')}
                    >
                      <Text style={styles.primaryButtonText}>Manually</Text>
                    </TouchableOpacity>
                  </View>
                  <View style={styles.card}>
                    <Text style={styles.cardTitle}>Add Squad Sheet By Email Request</Text>
                    <TextInput
                      style={styles.emailInput}
                      placeholder="Email..."
                      placeholderTextColor={theme.textSecondary}
                      keyboardType="email-address"
                      autoCapitalize="none"
                    />
                    <TouchableOpacity style={[styles.primaryButton, styles.edgeInset]}>
                      <Text style={styles.primaryButtonText}>Request Squad Sheet</Text>
                    </TouchableOpacity>
                  </View>
                </>
              )}
            </>
          )}
          {activeTeamView === 'Away' && (
            <>
              {awaySquadData && (awaySquadData.starters?.length > 0 || awaySquadData.substitutes?.length > 0 || awaySquadData.officials?.length > 0) ? (
                <SquadDisplay squadData={awaySquadData} teamType="away" />
              ) : (
                <>
                  <View style={styles.card}>
                    <Text style={styles.cardTitle}>Add Squad Sheet</Text>
                    <View style={styles.iconButtonsRow}>
                      <IconBoxButton iconName="file-upload" />
                      <IconBoxButton iconName="image" />
                      <IconBoxButton iconName="photo-camera" />
                    </View>
                    <TouchableOpacity
                      style={[styles.primaryButton, styles.edgeInset]}
                      onPress={() => handleNavigateToSquadSheet('away')}
                    >
                      <Text style={styles.primaryButtonText}>Manually</Text>
                    </TouchableOpacity>
                  </View>
                  <View style={styles.card}>
                    <Text style={styles.cardTitle}>Add Squad Sheet By Email Request</Text>
                    <TextInput
                      style={styles.emailInput}
                      placeholder="Email..."
                      placeholderTextColor={theme.textSecondary}
                      keyboardType="email-address"
                      autoCapitalize="none"
                    />
                    <TouchableOpacity style={[styles.primaryButton, styles.edgeInset]}>
                      <Text style={styles.primaryButtonText}>Request Squad Sheet</Text>
                    </TouchableOpacity>
                  </View>
                </>
              )}
            </>
          )}
        </>
      );
    }
    if (activeTab === 'match') {
      const canStartMatch = squadValidation.home && squadValidation.away;
      
      return (
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Match Control</Text>
          
          {!canStartMatch && (
            <View style={styles.warningContainer}>
              <MaterialIcons name="warning" size={20} color="#FF9800" />
              <Text style={styles.warningText}>
                Complete both squad sheets to start the match
              </Text>
            </View>
          )}
          
          <TouchableOpacity
            style={[
              styles.primaryButton, 
              styles.edgeInset,
              { 
                backgroundColor: canStartMatch ? theme.primary : theme.textSecondary,
                opacity: canStartMatch ? 1 : 0.6 
              }
            ]}
            onPress={() => {
              if (canStartMatch) {
                // Serialize the matchData to avoid navigation warnings
                const serializedMatchData = {
                  ...matchData,
                  matchDate: matchData?.matchDate ? matchData.matchDate.toISOString() : null,
                  // Remove any other non-serializable properties if they exist
                };
                
                (navigation as any).navigate('StartMatch', { 
                  matchId,
                  matchData: serializedMatchData
                });
              }
            }}
            disabled={!canStartMatch}
          >
            <Text style={styles.primaryButtonText}>
              {canStartMatch ? 'Start Match' : 'Complete Squad Sheets First'}
            </Text>
          </TouchableOpacity>
        </View>
      );
    }
    return <View style={styles.card}><Text style={styles.cardTitle}>{TABS.find(t => t.key === activeTab)?.label} Content</Text></View>;
  };

  const DropdownMenu = () => (
    <Modal
      visible={showDropdown}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setShowDropdown(false)}
    >
      <TouchableOpacity
        style={styles.dropdownOverlay}
        activeOpacity={1}
        onPress={() => setShowDropdown(false)}
      >
        <View style={styles.dropdownContainer}>
          <TouchableOpacity style={styles.dropdownItem} onPress={handleEditMatch}>
            <Text style={styles.dropdownItemText}>Edit Match</Text>
            <MaterialIcons name="chevron-right" size={20} color={theme.text} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.dropdownItem} onPress={handleEditHomeTeam}>
            <Text style={styles.dropdownItemText}>Edit Home Team</Text>
            <MaterialIcons name="chevron-right" size={20} color={theme.text} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.dropdownItem} onPress={handleEditAwayTeam}>
            <Text style={styles.dropdownItemText}>Edit Away Team</Text>
            <MaterialIcons name="chevron-right" size={20} color={theme.text} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.dropdownItem} onPress={handleMarkAsEnded}>
            <Text style={styles.dropdownItemText}>Mark as Ended</Text>
            <MaterialIcons name="chevron-right" size={20} color={theme.text} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.dropdownItem} onPress={handleDuplicateMatch}>
            <Text style={styles.dropdownItemText}>Duplicate Match</Text>
            <MaterialIcons name="chevron-right" size={20} color={theme.text} />
          </TouchableOpacity>
          <TouchableOpacity style={[styles.dropdownItem, styles.dropdownItemLast]} onPress={handleDeleteMatch}>
            <Text style={[styles.dropdownItemText, styles.deleteText]}>Delete Match</Text>
            <MaterialIcons name="chevron-right" size={20} color="#FF3B30" />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <Header
        title="Match Details"
        showBackButton={true}
        rightComponent={
          <TouchableOpacity onPress={() => setShowDropdown(true)}>
            <MaterialIcons name="add" size={24} color={theme.primary} />
          </TouchableOpacity>
        }
      />
      <DropdownMenu />
      <ScrollView style={styles.container} contentContainerStyle={styles.scrollContent} nestedScrollEnabled={true}>
        {loading ? (
          <View style={styles.card}>
            <Text style={styles.cardTitle}>Loading match details...</Text>
          </View>
        ) : error ? (
          <View style={styles.card}>
            <Text style={styles.cardTitle}>Error: {error}</Text>
          </View>
        ) : matchData ? (
          <>
            <View style={styles.card}>
              <Text style={styles.leagueTitle}>{matchData.competition}</Text>
              <View style={styles.separator} />
              <View style={styles.teamsRow}>
                <TeamDisplay
                  kit={matchData.homeTeam.kit}
                  name={matchData.homeTeam.shortName || matchData.homeTeam.name}
                />
                <Text style={styles.vsText}>-</Text>
                <TeamDisplay
                  kit={matchData.awayTeam.kit}
                  name={matchData.awayTeam.shortName || matchData.awayTeam.name}
                />
              </View>
            </View>

            <View style={styles.tabsContainer}>
              <ScrollView ref={scrollViewRef} horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.tabsScrollContainer} bounces={false}>
                {TABS.map((tab, index) => (
                  <TouchableOpacity
                    key={tab.key}
                    onLayout={(event) => {
                      const { x, width } = event.nativeEvent.layout;
                      setTabLayouts((prev) => ({ ...prev, [index]: { x, width } }));
                    }}
                    style={[styles.tab, activeTab === tab.key && styles.activeTab]}
                    onPress={() => setActiveTab(tab.key)}
                    activeOpacity={0.8}
                  >
                    <Text style={[styles.tabText, activeTab === tab.key && styles.activeTabText]}>{tab.label}</Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
            {renderContent()}
          </>
        ) : (
          <View style={styles.card}>
            <Text style={styles.cardTitle}>No match data available</Text>
          </View>
        )}
      </ScrollView>

      {/* Sync Management Modal */}
      <SyncManagementModal
        visible={showSyncModal}
        matchId={matchId}
        onClose={() => setShowSyncModal(false)}
        onSyncUpdate={() => {
          fetchMatchData();
          loadParticipants();
        }}
      />
    </SafeAreaView>
  );
};


// --- DYNAMIC STYLES ---
const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  // General
  safeArea: { flex: 1, backgroundColor: theme.card },
  container: { flex: 1, backgroundColor: theme.background },
  scrollContent: { padding: 16, paddingBottom: 80 },
  card: {
    backgroundColor: theme.card,
    borderRadius: 16,
    paddingVertical: 12,
    marginBottom: 16,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.05,
    shadowRadius: 4,
    elevation: isDarkMode ? 0 : 2,
  },
  cardTitle: { fontSize: 18, fontWeight: 'bold', color: theme.text, marginBottom: 16, paddingHorizontal: 16, paddingTop: 4 },
  primaryButton: { backgroundColor: theme.primary, paddingVertical: 12, borderRadius: 10, alignItems: 'center', },
  edgeInset: { marginHorizontal: 16, },
  primaryButtonText: { color: theme.white, fontSize: 15, fontWeight: 'bold', },
  separator: { height: 1, backgroundColor: theme.border, },

  // Header section
  leagueTitle: { fontSize: 16, fontWeight: '600', color: theme.text, textAlign: 'center', paddingBottom: 12, },
  teamsRow: { flexDirection: 'row', justifyContent: 'space-around', alignItems: 'center', paddingTop: 16, paddingHorizontal: 16, },
  teamDisplayContainer: { alignItems: 'center', width: 100, },
  teamName: { marginTop: 4, fontSize: 16, fontWeight: 'bold', },
  vsText: { fontSize: 20, fontWeight: '300', color: theme.textSecondary, marginTop: -40, },

  // Tabs
  tabsContainer: { backgroundColor: theme.background, paddingVertical: 8, paddingHorizontal: 0, marginBottom: 8, },
  tabsScrollContainer: { paddingHorizontal: 16, },
  tab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: theme.card,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
    borderColor: theme.border,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeTab: {
    backgroundColor: theme.primary,
    borderColor: theme.primary,
  },
  tabText: { fontSize: 14, fontWeight: '600', textAlign: 'center', color: theme.text, },
  activeTabText: { color: theme.background, },

  // Team Tab
  homeAwayToggle: {
    flexDirection: 'row',
    backgroundColor: `${theme.text}12`,
    borderRadius: 10,
    padding: 4,
    marginBottom: 16,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
  },
  toggleButton: { flex: 1, paddingVertical: 10, borderRadius: 8, alignItems: 'center', },
  activeToggleButton: { backgroundColor: theme.primary, },
  toggleButtonText: { color: theme.text, fontWeight: 'bold', fontSize: 13, },
  activeToggleButtonText: { color: theme.background },
  iconButtonsRow: { flexDirection: 'row', justifyContent: 'space-around', marginBottom: 20, paddingHorizontal: 20, },
  iconButton: { width: 52, height: 52, borderRadius: 12, backgroundColor: `${theme.primary}26`, justifyContent: 'center', alignItems: 'center', },
  emailInput: { backgroundColor: theme.background, borderWidth: 1, borderColor: theme.border, borderRadius: 8, padding: 12, fontSize: 14, marginBottom: 12, marginHorizontal: 16, color: theme.text },

  // Overview Content
  countdownTitle: { textAlign: 'center', color: theme.textSecondary, marginBottom: 8, fontSize: 13, fontWeight: '500' },
  countdownContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.card,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.border,
    paddingVertical: 12,
    marginBottom: 16,
    alignSelf: 'center',
    paddingHorizontal: 20
  },
  countdownItem: { alignItems: 'center', minWidth: 55, },
  countdownValue: { fontSize: 24, fontWeight: 'bold', color: theme.text },
  countdownLabel: { fontSize: 11, color: theme.textSecondary, marginTop: 2 },
  countdownSeparator: { width: 1, height: 30, backgroundColor: theme.border, marginHorizontal: 15, },
  detailRow: { flexDirection: 'row', alignItems: 'center', backgroundColor: theme.background, borderRadius: 10, padding: 14, marginBottom: 10, marginHorizontal: 16, },
  detailLabel: { fontSize: 14, color: theme.text, fontWeight: '600', marginRight: 10, },
  valueContainer: { flex: 1, alignItems: 'flex-end', },
  detailValue: { fontSize: 14, color: theme.textSecondary, fontWeight: '500', textAlign: 'right', },
  officialRow: { flexDirection: 'row', alignItems: 'center', backgroundColor: theme.background, borderRadius: 10, paddingVertical: 10, paddingHorizontal: 14, marginBottom: 10, marginHorizontal: 16, },
  officialIconContainer: { alignItems: 'center', marginRight: 12, width: 32 },
  officialIconLabel: { fontSize: 10, fontWeight: 'bold', color: theme.textSecondary, marginTop: 2 },
  officialInfo: { flex: 1, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' },
  officialName: { fontSize: 14, color: theme.text, fontWeight: '600' },
  statusBadge: { paddingHorizontal: 8, paddingVertical: 4, borderRadius: 12, marginLeft: 8 },
  statusText: { fontSize: 12, fontWeight: '500', color: '#fff' },
  syncButton: { borderWidth: 1, borderColor: theme.border, borderRadius: 15, paddingHorizontal: 12, paddingVertical: 5, backgroundColor: theme.card, minWidth: 75, alignItems: 'center' },
  syncButtonText: { fontSize: 11, color: theme.textSecondary, fontWeight: '500' },
  teamSectionWrapper: { paddingHorizontal: 16, marginBottom: 16 },
  teamSectionHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 },
  teamTag: { backgroundColor: theme.primary, borderRadius: 15, paddingHorizontal: 10, paddingVertical: 5 },
  teamTagText: { color: theme.white, fontSize: 11, fontWeight: 'bold' },
  addButton: { padding: 6, borderRadius: 20, backgroundColor: `${theme.primary}1A`, },
  teamOfficialRow: { flexDirection: 'row', alignItems: 'center', backgroundColor: theme.background, borderRadius: 10, paddingVertical: 12, paddingHorizontal: 12, marginBottom: 8, minHeight: 60, },
  teamOfficialKitContainer: { flexDirection: 'column', alignItems: 'center', justifyContent: 'center', marginRight: 12, width: 32 },
  teamOfficialInfoContainer: { flex: 1, marginRight: 12, minWidth: 0 },
  teamOfficialManagerName: { fontSize: 13, color: theme.text, fontWeight: '500', flexWrap: 'wrap', lineHeight: 18 },
  teamOfficialRole: { fontSize: 11, color: theme.textSecondary, fontWeight: '400', marginTop: 2 },
  editingInput: {
    fontSize: 13,
    color: theme.text,
    fontWeight: '500',
    borderBottomWidth: 1,
    borderBottomColor: theme.primary,
    paddingVertical: 2,
    minWidth: 100
  },
  emptyStateContainer: {
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 16
  },
  emptyStateText: {
    fontSize: 14,
    color: theme.textSecondary,
    fontWeight: '500',
    textAlign: 'center'
  },
  emptyStateSubtext: {
    fontSize: 12,
    color: theme.textSecondary,
    textAlign: 'center',
    marginTop: 4,
    opacity: 0.7
  },

  // Earnings Content
  earningsContainer: {},
  inputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: theme.card,
    borderRadius: 10,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 6,
    borderWidth: 1,
    borderColor: theme.border
  },
  inputLabel: {
    fontSize: 14,
    color: theme.text,
    fontWeight: 'bold'
  },
  inputValue: {
    fontSize: 14,
    color: theme.text,
    fontWeight: 'bold',
    textAlign: 'right'
  },

  // Notes Content
  notesContainer: {},
  notesCard: { backgroundColor: theme.card, borderRadius: 12, borderWidth: 1, borderColor: theme.border, minHeight: 200 },
  notesTextInput: { padding: 14, fontSize: 15, color: theme.text, textAlignVertical: 'top', flex: 1 },

  // Dropdown Menu Styles
  dropdownOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
    paddingTop: 60,
    paddingRight: 16,
  },
  dropdownContainer: {
    backgroundColor: theme.card,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: theme.border,
    minWidth: 200,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.border,
  },
  dropdownItemLast: {
    borderBottomWidth: 0,
  },
  dropdownItemText: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.text,
  },
  deleteText: {
    color: '#FF3B30',
  },

  // Squad Display Styles
  squadDisplayContainer: {
    flex: 1,
  },
  squadDisplayCard: {
    backgroundColor: theme.card,
    borderRadius: 12,
    padding: 15,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.10,
    shadowRadius: 2.00,
    elevation: 2,
  },
  squadSectionHeaderFull: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10
  },
  squadSectionTitleFull: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.text
  },
  squadPlayerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.border,
    paddingHorizontal: 10,
    paddingVertical: 5,
    marginBottom: 8,
    minHeight: 50
  },
  squadPlayerNumber: {
    fontSize: 14,
    color: theme.textSecondary,
    fontWeight: '500',
    width: 30,
    textAlign: 'center',
    paddingVertical: 8,
    marginRight: 5
  },
  squadPlayerName: {
    flex: 1,
    fontSize: 14,
    color: theme.text,
    paddingVertical: 8,
    marginRight: 5
  },
  squadPlayerIcons: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    minWidth: 50,
    gap: 8,
    marginRight: 5,
  },
  squadOfficialRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.border,
    paddingHorizontal: 10,
    paddingVertical: 5,
    marginBottom: 8,
    minHeight: 50
  },
  squadOfficialRole: {
    flex: 1,
    fontSize: 14,
    color: theme.text,
    marginLeft: 10,
    fontWeight: '500'
  },
  squadOfficialName: {
    flex: 1.5,
    fontSize: 14,
    color: theme.textSecondary,
    textAlign: 'right',
    marginRight: 5
  },

  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF3E0',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#FFB74D',
  },
  warningText: {
    fontSize: 14,
    color: '#F57C00',
    fontWeight: '500',
    marginLeft: 8,
    flex: 1,
  },
});


export default MatchDetailScreen;