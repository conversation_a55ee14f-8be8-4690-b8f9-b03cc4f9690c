-- Migration to rename fields and add new Extra Time additional Sub fields

-- Rename benchSize to maxSubstitute in matches table
ALTER TABLE "matches" RENAME COLUMN "benchSize" TO "maxSubstitute";

-- Rename subOpportunities to substituteOpportunities in matches table
ALTER TABLE "matches" RENAME COLUMN "subOpportunities" TO "substituteOpportunities";

-- Rename subOpportunitiesAllowance to substituteOpportunitiesAllowance in matches table
ALTER TABLE "matches" RENAME COLUMN "subOpportunitiesAllowance" TO "substituteOpportunitiesAllowance";

-- Add new Extra Time additional Sub fields to matches table
ALTER TABLE "matches" ADD COLUMN "extraTimeAdditionalSub" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE "matches" ADD COLUMN "extraTimeAdditionalSubAllowance" INTEGER NOT NULL DEFAULT 1;

-- Rename benchSize to maxSubstitute in templates table
ALTER TABLE "templates" RENAME COLUMN "benchSize" TO "maxSubstitute";

-- Rename subOpportunities to substituteOpportunities in templates table
ALTER TABLE "templates" RENAME COLUMN "subOpportunities" TO "substituteOpportunities";

-- Rename subOpportunitiesAllowance to substituteOpportunitiesAllowance in templates table
ALTER TABLE "templates" RENAME COLUMN "subOpportunitiesAllowance" TO "substituteOpportunitiesAllowance";

-- Add new Extra Time additional Sub fields to templates table
ALTER TABLE "templates" ADD COLUMN "extraTimeAdditionalSub" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE "templates" ADD COLUMN "extraTimeAdditionalSubAllowance" INTEGER NOT NULL DEFAULT 1;