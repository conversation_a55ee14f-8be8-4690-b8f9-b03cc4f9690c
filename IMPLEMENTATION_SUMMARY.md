# Match Logic Implementation Summary

## Features Implemented

### 1. Squad Sheet Validation
- **Location**: `ref-app/screens/MatchDetailScreen.tsx` - MatchContent component
- **Functionality**: 
  - Validates that both home and away teams have complete squad sheets before allowing match start
  - Shows visual indicators (green/red) for squad completion status
  - Provides direct navigation to squad sheet screens for each team
  - Prevents match start until both squads are complete

### 2. Enhanced Timer Management
- **Location**: `ref-app/screens/StartMatchScreen.tsx`
- **Functionality**:
  - Supports multiple periods with configurable lengths from match template
  - Automatic half-time timer (15 minutes by default, configurable)
  - Period progression: Period 1 → Half Time → Period 2 → Match Complete
  - Displays remaining time in current period
  - Handles match completion after all periods

### 3. Team Selection Logic
- **Location**: `ref-app/screens/StartMatchScreen.tsx`
- **Functionality**:
  - User selects kickoff team for first period
  - Automatically alternates kickoff team for subsequent periods
  - Shows which team will kick off next during half-time
  - Maintains team selection state throughout match

### 4. Misconduct Code Integration
- **Location**: Multiple files
- **Files Modified**:
  - `ref-app/api/misconductService.ts` (new)
  - `ref-app/screens/MatchDetailScreen.tsx`
  - `ref-app/screens/CreateMatchScreen.tsx`
  - `ref-app/api/config.ts`
- **Functionality**:
  - Loads misconduct sets from ManageMisconductCodeScreen
  - Includes hardcoded Wales and England with FA Codes sets
  - Allows selection of misconduct set for each match
  - Integrates with match creation templates

### 5. Match Phases
- **Pre-Match**: Team selection for kickoff
- **Period Play**: Active match timer with period tracking
- **Half-Time**: Automatic 15-minute break with next period preview
- **Match Complete**: Final state with return to match details

## Key Components

### MatchContent Component
```typescript
// Shows squad validation, match settings, and misconduct code selection
<MatchContent 
  styles={styles} 
  matchData={matchData} 
  matchId={matchId} 
  squadValidation={squadValidation}
  onDataUpdate={fetchMatchData} 
/>
```

### Squad Validation Logic
```typescript
const loadSquadValidation = async () => {
  const [homeResponse, awayResponse] = await Promise.all([
    SquadService.getSquadByMatch(matchId, 'home'),
    SquadService.getSquadByMatch(matchId, 'away')
  ]);
  
  const homeValid = homeResponse.success && homeResponse.data?.players?.length > 0;
  const awayValid = awayResponse.success && awayResponse.data?.players?.length > 0;
  
  setSquadValidation({ home: homeValid, away: awayValid });
};
```

### Timer Management
```typescript
// Period timer
const periodLength = matchData?.periodLengths?.[currentPeriod - 1] || 45;
const periodLengthInSeconds = periodLength * 60;

// Auto-advance to half-time when period ends
if (newSeconds >= periodLengthInSeconds) {
  handlePeriodEnd();
}
```

## User Flow

1. **Match Details Screen**:
   - User sees squad sheet status for both teams
   - Must complete both squad sheets before "Start Match" is enabled
   - Can select misconduct code set for the match

2. **Squad Sheet Screens**:
   - User fills in players and officials for each team
   - Data is validated and saved to enable match start

3. **Start Match Screen**:
   - User selects which team kicks off first
   - Match begins with Period 1 timer
   - Timer counts up to period length (e.g., 45 minutes)
   - Automatic transition to half-time
   - Half-time timer (15 minutes)
   - Automatic setup for Period 2 with opposite team kickoff
   - Match completion after all periods

## Technical Details

### Squad Validation
- Checks for existence of players in squad data
- Real-time validation updates
- Visual feedback with color-coded indicators

### Timer Logic
- Uses React useEffect with setInterval
- Handles multiple timer states (period, half-time)
- Automatic phase transitions
- Configurable period lengths from match template

### Misconduct Integration
- Service layer for misconduct code management
- Hardcoded default sets (Wales, England with FA Codes)
- Support for custom user-created sets
- Integration with match creation workflow

## Files Modified/Created

### New Files:
- `ref-app/api/misconductService.ts`
- `IMPLEMENTATION_SUMMARY.md`

### Modified Files:
- `ref-app/screens/MatchDetailScreen.tsx`
- `ref-app/screens/StartMatchScreen.tsx`
- `ref-app/screens/CreateMatchScreen.tsx`
- `ref-app/api/config.ts`

## Next Steps

1. **Testing**: Test the complete flow from match creation to completion
2. **Error Handling**: Add more robust error handling for edge cases
3. **Persistence**: Save match state to handle app backgrounding
4. **Notifications**: Add notifications for period transitions
5. **Statistics**: Track match events and statistics during play