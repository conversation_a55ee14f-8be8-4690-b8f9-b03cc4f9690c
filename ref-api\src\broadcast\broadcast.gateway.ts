import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  ConnectedSocket,
  MessageBody,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, UseGuards } from '@nestjs/common';
import { BroadcastService, MatchAction, SyncNotification } from './broadcast.service';
import { MatchSyncService } from './match-sync.service';
import { RedisService } from '../redis/redis.service';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  matchId?: string;
}

@WebSocketGateway({
  cors: {
    origin: '*', // Configure this properly for production
    methods: ['GET', 'POST'],
  },
})
export class BroadcastGateway implements OnGatewayConnection, OnGatewayDisconnect, OnGatewayInit {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(BroadcastGateway.name);
  private connectedUsers = new Map<string, AuthenticatedSocket>();

  constructor(
    private readonly broadcastService: BroadcastService,
    private readonly matchSyncService: MatchSyncService,
    private readonly redisService: RedisService,
  ) {}

  /**
   * Initialize gateway and start listening for Redis notifications
   */
  afterInit(server: Server) {
    this.logger.log('🚀 WebSocket Gateway initialized');
    this.startNotificationListener();
  }

  async handleConnection(client: AuthenticatedSocket) {
    this.logger.log(`🔌 Client connected: ${client.id}`);
  }

  async handleDisconnect(client: AuthenticatedSocket) {
    this.logger.log(`🔌 Client disconnected: ${client.id}`);
    
    if (client.userId) {
      this.connectedUsers.delete(client.userId);
      
      // Leave match room if connected to one
      if (client.matchId) {
        client.leave(`match:${client.matchId}`);
        this.logger.log(`👋 User ${client.userId} left match ${client.matchId}`);
      }
    }
  }

  /**
   * Authenticate user and join their personal room
   */
  @SubscribeMessage('authenticate')
  async handleAuthenticate(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { userId: string; token?: string }
  ) {
    try {
      this.logger.log(`🔑 Authentication attempt for user: ${data.userId}`);

      // TODO: Validate token if needed
      client.userId = data.userId;
      this.connectedUsers.set(data.userId, client);

      // Join personal room for notifications
      client.join(`user:${data.userId}`);

      this.logger.log(`✅ User ${data.userId} authenticated and added to connected users`);
      this.logger.log(`🔔 Total connected users: ${this.connectedUsers.size}`);

      client.emit('authenticated', { success: true });
    } catch (error) {
      this.logger.error(`❌ Authentication failed for ${data.userId}: ${error.message}`);
      client.emit('authenticated', { success: false, error: error.message });
    }
  }

  /**
   * Join match room for real-time sync
   */
  @SubscribeMessage('join_match')
  async handleJoinMatch(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { matchId: string }
  ) {
    try {
      if (!client.userId) {
        client.emit('error', { message: 'Not authenticated' });
        return;
      }

      // Verify user is participant in this match
      const isParticipant = await this.matchSyncService.isUserParticipant(data.matchId, client.userId);
      if (!isParticipant) {
        client.emit('error', { message: 'Not authorized for this match' });
        return;
      }

      // Leave previous match room if any
      if (client.matchId) {
        client.leave(`match:${client.matchId}`);
      }

      // Join new match room
      client.matchId = data.matchId;
      client.join(`match:${data.matchId}`);
      
      this.logger.log(`🏟️ User ${client.userId} joined match ${data.matchId}`);
      
      client.emit('match_joined', { matchId: data.matchId });
    } catch (error) {
      this.logger.error(`❌ Failed to join match: ${error.message}`);
      client.emit('error', { message: error.message });
    }
  }

  /**
   * Broadcast match action to all participants
   */
  @SubscribeMessage('broadcast_action')
  async handleBroadcastAction(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() action: MatchAction
  ) {
    try {
      if (!client.userId || !client.matchId) {
        client.emit('error', { message: 'Not authenticated or not in match' });
        return;
      }

      // Verify user is referee or has permission to broadcast
      const userRole = await this.matchSyncService.getUserRole(client.matchId, client.userId);
      if (!userRole) {
        client.emit('error', { message: 'Not authorized to broadcast actions' });
        return;
      }

      // Add to Redis Stream
      await this.broadcastService.broadcastMatchAction(action);

      // Emit to all participants in the match room (except sender)
      client.to(`match:${client.matchId}`).emit('action_received', {
        ...action,
        fromRole: userRole,
        requiresApproval: userRole !== 'Referee' // Only referee actions are auto-approved
      });

      this.logger.log(`📡 Action ${action.type} broadcasted by ${client.userId} (${userRole})`);
      
      client.emit('action_broadcasted', { success: true, actionId: action.id });
    } catch (error) {
      this.logger.error(`❌ Failed to broadcast action: ${error.message}`);
      client.emit('error', { message: error.message });
    }
  }

  /**
   * Handle action approval/decline
   */
  @SubscribeMessage('action_response')
  async handleActionResponse(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { actionId: string; response: 'accept' | 'decline'; reason?: string }
  ) {
    try {
      if (!client.userId || !client.matchId) {
        client.emit('error', { message: 'Not authenticated or not in match' });
        return;
      }

      // Process the response (save to database, notify others, etc.)
      await this.matchSyncService.handleActionResponse(
        client.matchId,
        client.userId,
        data.actionId,
        data.response,
        data.reason
      );

      this.logger.log(`✅ User ${client.userId} ${data.response}ed action ${data.actionId}`);
      
      client.emit('response_recorded', { success: true });
    } catch (error) {
      this.logger.error(`❌ Failed to handle action response: ${error.message}`);
      client.emit('error', { message: error.message });
    }
  }

  /**
   * Send notification to specific user
   */
  async sendNotificationToUser(userId: string, notification: any) {
    const userSocket = this.connectedUsers.get(userId);
    if (userSocket) {
      userSocket.emit('notification', notification);
      userSocket.emit('sync_invite', notification); // Also emit specific event
      this.logger.log(`🔔 Sent notification to connected user ${userId}`);
    } else {
      // User not connected, save to Redis Stream for later
      await this.broadcastService.sendUserNotification(userId, notification);
      this.logger.log(`💾 Saved notification for offline user ${userId}`);
    }
  }

  /**
   * Send notification directly (called from services)
   */
  sendDirectNotification(userId: string, notification: any) {
    this.logger.log(`🔔 Attempting to send notification to user ${userId}`);
    this.logger.log(`🔔 Connected users: ${Array.from(this.connectedUsers.keys()).join(', ')}`);

    const userSocket = this.connectedUsers.get(userId);

    if (userSocket) {
      this.logger.log(`🔔 User ${userId} is connected, sending notification`);
      userSocket.emit('notification', notification);
      userSocket.emit('sync_invite', notification);
      this.logger.log(`🔔 Direct notification sent to user ${userId}`);
      return true;
    }

    this.logger.log(`❌ User ${userId} not connected for direct notification`);
    this.logger.log(`❌ Available users: ${Array.from(this.connectedUsers.keys())}`);
    return false;
  }

  /**
   * Broadcast to all participants in a match
   */
  async broadcastToMatch(matchId: string, event: string, data: any) {
    this.server.to(`match:${matchId}`).emit(event, data);
    this.logger.log(`📡 Broadcasted ${event} to match ${matchId}`);
  }

  /**
   * Get connected users count for a match
   */
  getMatchParticipantsCount(matchId: string): number {
    const room = this.server.sockets.adapter.rooms.get(`match:${matchId}`);
    return room ? room.size : 0;
  }

  /**
   * Start listening for Redis notifications and forward to WebSocket clients
   */
  private async startNotificationListener() {
    try {
      // This is a simplified approach - in production you might want to use Redis pub/sub
      // For now, we'll rely on the direct WebSocket emission in sendNotificationToUser
      this.logger.log('🔔 Notification listener started');
    } catch (error) {
      this.logger.error('❌ Failed to start notification listener:', error);
    }
  }
}
