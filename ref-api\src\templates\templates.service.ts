import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { CreateTemplateDto } from './dto/create-template.dto';
import { UpdateTemplateDto } from './dto/update-template.dto';

@Injectable()
export class TemplatesService {
  constructor(private prisma: PrismaService) { }

  async findAllByUser(userId: string) {
    return this.prisma.template.findMany({
      where: {
        userId: userId
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
  }

  async create(createTemplateDto: CreateTemplateDto) {
    return this.prisma.template.create({
      data: {
        name: createTemplateDto.name,
        userId: createTemplateDto.userId,
        teamSize: createTemplateDto.teamSize ?? 11,
        maxSubstitute: createTemplateDto.maxSubstitute ?? 5,
        numberOfPeriods: createTemplateDto.numberOfPeriods ?? 2,
        periodLengths: createTemplateDto.periodLengths ?? [45, 45],
        halfTime: createTemplateDto.halfTime ?? 15,
        extraTime: createTemplateDto.extraTime ?? false,
        extraTimeLengths: createTemplateDto.extraTimeLengths ?? undefined,
        substituteOpportunities: createTemplateDto.substituteOpportunities ?? false,
        substituteOpportunitiesAllowance: createTemplateDto.substituteOpportunitiesAllowance ?? 1,
        extraTimeSubOpportunities: createTemplateDto.extraTimeSubOpportunities ?? false,
        extraTimeSubOpportunitiesAllowance: createTemplateDto.extraTimeSubOpportunitiesAllowance ?? 1,
        extraTimeAdditionalSub: createTemplateDto.extraTimeAdditionalSub ?? false,
        extraTimeAdditionalSubAllowance: createTemplateDto.extraTimeAdditionalSubAllowance ?? 1,
        penalties: createTemplateDto.penalties ?? false,
        misconductCode: createTemplateDto.misconductCode ?? 'Wales',
        temporaryDismissals: createTemplateDto.temporaryDismissals ?? false,
        temporaryDismissalsTime: createTemplateDto.temporaryDismissalsTime ?? 10,
        injuryTimeAllowance: createTemplateDto.injuryTimeAllowance ?? false,
        injuryTimeSubs: createTemplateDto.injuryTimeSubs ?? 0,
        injuryTimeSanctions: createTemplateDto.injuryTimeSanctions ?? 0,
        injuryTimeGoals: createTemplateDto.injuryTimeGoals ?? 0,
        rollOnRollOff: createTemplateDto.rollOnRollOff ?? false,
      }
    });
  }

  async findOne(id: string) {
    return this.prisma.template.findUnique({
      where: { id }
    });
  }



  async update(id: string, updateTemplateDto: UpdateTemplateDto) {
    return this.prisma.template.update({
      where: { id },
      data: updateTemplateDto
    });
  }

  async remove(id: string) {
    return this.prisma.template.delete({
      where: { id }
    });
  }
}