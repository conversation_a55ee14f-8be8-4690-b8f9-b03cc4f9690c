import { 
  <PERSON><PERSON><PERSON>al, 
  <PERSON><PERSON>tring, 
  IsEmail, 
  IsBoolean, 
  IsNumber, 
  IsEnum,
  Length,
  Min,
  Max,
  Matches,
  IsUrl
} from 'class-validator';
import { Transform } from 'class-transformer';
import { Gender } from '@prisma/client';

export class UpdateUserDto {
  @IsOptional()
  @IsString()
  @Length(1, 100, { message: 'Name must be between 1 and 100 characters' })
  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  name?: string;

  @IsOptional()
  @IsString()
  @Length(1, 100, { message: 'Display name must be between 1 and 100 characters' })
  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  displayName?: string;

  @IsOptional()
  @IsEmail({}, { message: 'Must be a valid email address' })
  @Length(1, 255, { message: 'Email must be between 1 and 255 characters' })
  @Transform(({ value }) => typeof value === 'string' ? value.trim().toLowerCase() : value)
  email?: string;

  @IsOptional()
  @IsUrl({}, { message: 'Must be a valid URL' })
  @Length(1, 500, { message: 'Photo URL must be between 1 and 500 characters' })
  photoURL?: string;

  @IsOptional()
  @IsBoolean()
  emailVerified?: boolean;

  @IsOptional()
  @IsString()
  @Length(1, 50, { message: 'Provider must be between 1 and 50 characters' })
  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  provider?: string;

  @IsOptional()
  @IsString()
  @Length(3, 50, { message: 'Username must be between 3 and 50 characters' })
  @Matches(/^[a-zA-Z0-9_-]+$/, { message: 'Username can only contain letters, numbers, underscores, and hyphens' })
  @Transform(({ value }) => typeof value === 'string' ? value.trim().toLowerCase() : value)
  username?: string;

  @IsOptional()
  @Transform(({ value }) => {
    if (value === '' || value === null || value === undefined) return null;
    const num = parseFloat(value);
    return isNaN(num) ? value : num;
  })
  @IsNumber({}, { message: 'Weight must be a valid number' })
  @Min(30, { message: 'Weight must be at least 30 kg' })
  @Max(200, { message: 'Weight must be at most 200 kg' })
  weight?: number;

  @IsOptional()
  @Transform(({ value }) => {
    if (value === '' || value === null || value === undefined) return null;
    const num = parseFloat(value);
    return isNaN(num) ? value : num;
  })
  @IsNumber({}, { message: 'Height must be a valid number' })
  @Min(100, { message: 'Height must be at least 100 cm' })
  @Max(250, { message: 'Height must be at most 250 cm' })
  height?: number;

  @IsOptional()
  @Transform(({ value }) => {
    if (!value || value === '') return null;
    // Handle mobile app gender format
    const genderMap = {
      'Male': 'MALE',
      'Female': 'FEMALE', 
      'Other': 'OTHER',
      'Prefer not to say': 'PREFER_NOT_TO_SAY'
    };
    return genderMap[value] || value;
  })
  @IsEnum(Gender, { message: 'Gender must be a valid option' })
  gender?: Gender;

  @IsOptional()
  @IsString()
  @Length(1, 100, { message: 'Country must be between 1 and 100 characters' })
  @Transform(({ value }) => typeof value === 'string' ? value.trim() : value)
  country?: string;

  @IsOptional()
  @IsString()
  @Length(0, 100, { message: 'Football Association must be at most 100 characters' })
  @Transform(({ value }) => {
    if (!value || typeof value !== 'string') return null;
    const trimmed = value.trim();
    return trimmed === '' ? null : trimmed;
  })
  countryFA?: string;

  // Support mobile app's footballAssociation field name
  @IsOptional()
  @IsString()
  @Length(0, 100, { message: 'Football Association must be at most 100 characters' })
  @Transform(({ value }) => {
    if (!value || typeof value !== 'string') return null;
    const trimmed = value.trim();
    return trimmed === '' ? null : trimmed;
  })
  footballAssociation?: string;

  @IsOptional()
  @IsString()
  @Length(0, 50, { message: 'Referee level must be at most 50 characters' })
  @Transform(({ value }) => {
    if (!value || typeof value !== 'string') return null;
    const trimmed = value.trim();
    return trimmed === '' ? null : trimmed;
  })
  refereeLevel?: string;
}