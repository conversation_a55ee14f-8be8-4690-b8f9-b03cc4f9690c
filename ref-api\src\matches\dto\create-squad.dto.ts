import { IsString, <PERSON><PERSON>rray, <PERSON><PERSON>num, IsBoolean, IsN<PERSON>ber, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export enum TeamType {
  HOME = 'HOME',
  AWAY = 'AWAY'
}

export enum PlayerPosition {
  STARTER = 'STARTER',
  SUBSTITUTE = 'SUBSTITUTE'
}

export class CreateSquadPlayerDto {
  @IsNumber()
  number: number;

  @IsString()
  name: string;

  @IsEnum(PlayerPosition)
  position: PlayerPosition;

  @IsBoolean()
  isCaptain: boolean;

  @IsBoolean()
  isGoalkeeper: boolean;
}

export class CreateSquadOfficialDto {
  @IsString()
  role: string;

  @IsString()
  name: string;
}

export class CreateSquadDto {
  @IsString()
  matchId: string;

  @IsEnum(TeamType)
  teamType: TeamType;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateSquadPlayerDto)
  players: CreateSquadPlayerDto[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateSquadOfficialDto)
  officials: CreateSquadOfficialDto[];
}