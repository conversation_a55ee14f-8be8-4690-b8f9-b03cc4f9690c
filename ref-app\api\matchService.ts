import { apiClient } from './client';
import { API_CONFIG, ApiResponse, CreateMatchRequest, MatchResponse, MatchLogEvent } from './config';

export class MatchService {
  // Create a new match
  static async createMatch(matchData: CreateMatchRequest): Promise<ApiResponse<MatchResponse>> {
    return apiClient.post<MatchResponse>(API_CONFIG.ENDPOINTS.MATCHES.BASE, matchData);
  }

  // Get all matches
  static async getAllMatches(refereeId?: string): Promise<ApiResponse<MatchResponse[]>> {
    const params = refereeId ? { refereeId } : undefined;
    return apiClient.get<MatchResponse[]>(API_CONFIG.ENDPOINTS.MATCHES.BASE, params);
  }

  // Get upcoming matches
  static async getUpcomingMatches(refereeId?: string): Promise<ApiResponse<MatchResponse[]>> {
    const params = refereeId ? { refereeId } : undefined;
    return apiClient.get<MatchResponse[]>(API_CONFIG.ENDPOINTS.MATCHES.UPCOMING, params);
  }

  // Get previous matches
  static async getPreviousMatches(refereeId?: string): Promise<ApiResponse<MatchResponse[]>> {
    const params = refereeId ? { refereeId } : undefined;
    return apiClient.get<MatchResponse[]>(API_CONFIG.ENDPOINTS.MATCHES.PREVIOUS, params);
  }

  // Get match by ID
  static async getMatchById(id: string): Promise<ApiResponse<MatchResponse>> {
    const response = await apiClient.get<MatchResponse>(API_CONFIG.ENDPOINTS.MATCHES.BY_ID(id));
    
    // Debug log to check raw API response
    console.log('🔴 Raw API Response:', {
      success: response.success,
      data: response.data ? {
        id: response.data.id,
        extraTime: response.data.extraTime,
        extraTimeLengths: response.data.extraTimeLengths,
        templateName: response.data.templateName,
        misconductCode: response.data.misconductCode,
        fullData: response.data
      } : null
    });
    
    return response;
  }

  // Helper method to convert single API response to detailed format for match details screen
  static convertApiResponseToDetailFormat(match: MatchResponse): any {
    console.log('🔄 Converting API match data:', {
      id: match.id,
      extraTime: match.extraTime,
      extraTimeLengths: match.extraTimeLengths,
      templateName: match.templateName,
      fullMatch: match
    });
    
    return {
      id: match.id,
      competition: match.competition,
      venue: match.venue,
      matchDate: new Date(match.matchDate),
      officialRole: match.officialRole,

      // Team data with full kit information
      homeTeam: {
        name: match.homeTeamName,
        shortName: match.homeTeamShortName,
        kit: {
          base: match.homeKitBase,
          stripe: match.homeKitStripe,
          shorts: match.homeKitBase // Using base color for shorts for now
        }
      },
      awayTeam: {
        name: match.awayTeamName,
        shortName: match.awayTeamShortName,
        kit: {
          base: match.awayKitBase,
          stripe: match.awayKitStripe,
          shorts: match.awayKitBase // Using base color for shorts for now
        }
      },

      // Match format details
      teamSize: match.teamSize || 11,
      maxSubstitute: match.maxSubstitute || 5,
      numberOfPeriods: match.numberOfPeriods || 2,
      periodLengths: match.periodLengths || [45, 45],
      halfTime: match.halfTime || 15,

      // Extra time settings
      extraTime: match.extraTime,
      extraTimeLengths: match.extraTimeLengths || [15, 15],

      // Substitution rules
      substituteOpportunities: match.substituteOpportunities,
      substituteOpportunitiesAllowance: match.substituteOpportunitiesAllowance || 1,
      extraTimeSubOpportunities: match.extraTimeSubOpportunities,
      extraTimeSubOpportunitiesAllowance: match.extraTimeSubOpportunitiesAllowance || 1,
      extraTimeAdditionalSub: match.extraTimeAdditionalSub,
      extraTimeAdditionalSubAllowance: match.extraTimeAdditionalSubAllowance || 1,
      rollOnRollOff: match.rollOnRollOff,

      // Other rules
      penalties: match.penalties,
      temporaryDismissals: match.temporaryDismissals,
      temporaryDismissalsTime: match.temporaryDismissalsTime || 10,

      // Injury time settings
      injuryTimeAllowance: match.injuryTimeAllowance,
      injuryTime: {
        subs: match.injuryTimeSubs || 0,
        sanctions: match.injuryTimeSanctions || 0,
        goals: match.injuryTimeGoals || 0,
      },

      status: match.status,
      referee: match.referee,
      refereeId: match.referee?.id,
      refereeName: match.referee?.name,

      // Match officials from backend
      matchOfficials: match.matchOfficials || [],

      // Earnings from backend
      earnings: {
        fees: match.fees || '',
        expenses: match.expenses || '',
        mileage: match.mileage || '',
        travelTime: match.travelTime || ''
      },

      // Notes from backend
      notes: match.notes || '',

      // Template info
      templateName: match.templateName,

      // Misconduct code
      misconductCode: match.misconductCode || 'Wales'
    };
  }

  // Update match
  static async updateMatch(id: string, matchData: Partial<CreateMatchRequest>): Promise<ApiResponse<MatchResponse>> {
    return apiClient.patch<MatchResponse>(API_CONFIG.ENDPOINTS.MATCHES.BY_ID(id), matchData);
  }

  // Update match status
  static async updateMatchStatus(
    id: string,
    status: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED'
  ): Promise<ApiResponse<MatchResponse>> {
    return apiClient.patch<MatchResponse>(
      API_CONFIG.ENDPOINTS.MATCHES.UPDATE_STATUS(id),
      { status }
    );
  }

  // Delete match
  static async deleteMatch(id: string): Promise<ApiResponse<MatchResponse>> {
    return apiClient.delete<MatchResponse>(API_CONFIG.ENDPOINTS.MATCHES.BY_ID(id));
  }

  // Helper method to convert form data to API format
  static convertFormDataToApiFormat(formData: any): CreateMatchRequest {
    return {
      // Basic match info
      competition: formData.competition,
      venue: formData.venue,
      matchDate: formData.matchDate || new Date().toISOString(),
      officialRole: formData.officialRole || 'Referee',

      // Home team
      homeTeamName: formData.homeTeamName,
      homeTeamShortName: formData.homeTeamShortName,
      homeKitBase: formData.homeKitColors?.base || '#FF0000',
      homeKitStripe: formData.homeKitColors?.stripe || '#00FFFF',

      // Away team
      awayTeamName: formData.awayTeamName,
      awayTeamShortName: formData.awayTeamShortName,
      awayKitBase: formData.awayKitColors?.base || '#FFFFFF',
      awayKitStripe: formData.awayKitColors?.stripe || '#0000FF',

      // Match format
      teamSize: formData.teamSize || 11,
      maxSubstitute: formData.maxSubstitute || 5,
      numberOfPeriods: formData.numberOfPeriods || 2,
      periodLengths: formData.periodLengths || [45, 45],
      halfTime: formData.halfTime || 15,

      // Extra time settings
      extraTime: formData.extraTime === 'Yes',
      extraTimeLengths: formData.extraTime === 'Yes' ? formData.extraTimeLengths : undefined,

      // Substitution rules
      substituteOpportunities: formData.SubstituteOpportunities === 'Yes',
      substituteOpportunitiesAllowance: formData.SubstituteOpportunitiesAllowance || 1,
      extraTimeSubOpportunities: formData.extraTimeSubOpportunities === 'Yes',
      extraTimeSubOpportunitiesAllowance: formData.extraTimeSubOpportunitiesAllowance || 1,
      extraTimeAdditionalSub: formData.extraTimeAdditionalSub === 'Yes',
      extraTimeAdditionalSubAllowance: formData.extraTimeAdditionalSubAllowance || 1,
      rollOnRollOff: formData.rollOnRollOff === 'Yes',

      // Other rules
      penalties: formData.penalties === 'Yes',
      misconductCode: formData.misconductCode || 'Wales',
      temporaryDismissals: formData.temporaryDismissals === 'Yes',
      temporaryDismissalsTime: formData.temporaryDismissalsTime || 10,

      // Injury time
      injuryTimeAllowance: formData.injuryTimeAllowance === 'Yes',
      injuryTimeSubs: formData.injuryTime?.subs || 0,
      injuryTimeSanctions: formData.injuryTime?.sanctions || 0,
      injuryTimeGoals: formData.injuryTime?.goals || 0,

      // Match officials
      matchOfficials: formData.matchOfficialsList?.map((official: any) => ({
        role: official.role,
        name: official.name
      })) || [],

      // Earnings
      fees: formData.fees,
      expenses: formData.expenses,
      mileage: formData.mileage,
      travelTime: formData.travelTime,

      // Notes
      notes: formData.noteContent,

      // Template info
      templateName: formData.selectedTemplate !== '__CREATE_NEW__' ? formData.selectedTemplate : formData.newTemplateName,
    };
  }

  // Save match logs
  static async saveMatchLogs(id: string, logs: MatchLogEvent[]): Promise<ApiResponse<any>> {
    return apiClient.post<any>(API_CONFIG.ENDPOINTS.MATCHES.MATCH_LOGS(id), { logs });
  }

  // Get match logs
  static async getMatchLogs(id: string): Promise<ApiResponse<MatchLogEvent[]>> {
    return apiClient.get<MatchLogEvent[]>(API_CONFIG.ENDPOINTS.MATCHES.MATCH_LOGS(id));
  }

  // Get filter options from match summaries
  static async getFilterOptions(): Promise<ApiResponse<any>> {
    return apiClient.get<any>(API_CONFIG.ENDPOINTS.MATCHES.FILTER_OPTIONS);
  }

  // Get filtered matches based on criteria
  static async getFilteredMatches(filters: any): Promise<ApiResponse<MatchResponse[]>> {
    return apiClient.post<MatchResponse[]>(API_CONFIG.ENDPOINTS.MATCHES.FILTERED_MATCHES, filters);
  }

  // Helper method to convert API response to dashboard format
  static convertApiResponseToDashboardFormat(matches: MatchResponse[]): any[] {
    return matches.map(match => {
      const matchDateTime = new Date(match.matchDate);

      // Gracefully handle invalid dates from the API
      if (isNaN(matchDateTime.getTime())) {
        console.warn(`Invalid matchDate received from API for match ID ${match.id}: ${match.matchDate}`);
        return {
          division: match.competition,
          role: match.officialRole,
          homeTeam: { name: match.homeTeamName, kitColor: match.homeKitBase, kitStripe: match.homeKitStripe },
          awayTeam: { name: match.awayTeamName, kitColor: match.awayKitBase, kitStripe: match.awayKitStripe },
          location: match.venue,
          date: 'TBC', // To be confirmed
          time: 'TBC',
          id: match.id,
          status: match.status,
        };
      }

      // Format date as DD/MM/YY
      const date = `${matchDateTime.getDate().toString().padStart(2, '0')}/${(matchDateTime.getMonth() + 1).toString().padStart(2, '0')}/${matchDateTime.getFullYear().toString().slice(-2)}`;

      // Format time as HH:MM
      const time = `${matchDateTime.getHours().toString().padStart(2, '0')}:${matchDateTime.getMinutes().toString().padStart(2, '0')}`;

      return {
        division: match.competition,
        role: match.officialRole,
        homeTeam: {
          name: match.homeTeamName,
          kitColor: match.homeKitBase,
          kitStripe: match.homeKitStripe
        },
        awayTeam: {
          name: match.awayTeamName,
          kitColor: match.awayKitBase,
          kitStripe: match.awayKitStripe
        },
        location: match.venue,
        date,
        time,
        id: match.id,
        status: match.status
      };
    });
  }
}

export default MatchService;
