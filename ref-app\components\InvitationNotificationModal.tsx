import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import BroadcastService from '../api/broadcastService';

interface MatchInvitation {
  id: string;
  type: 'sync_invite';
  matchId: string;
  fromUserId: string;
  data: {
    matchDetails: {
      competition: string;
      venue: string;
      homeTeam: string;
      awayTeam: string;
      matchDate: string;
    };
    role: string;
    invitedByName: string;
  };
  timestamp: number;
}

interface InvitationNotificationModalProps {
  visible: boolean;
  invitation: MatchInvitation | null;
  onClose: () => void;
  onAccept?: (matchId: string) => void;
  onDecline?: (invitationId: string) => void;
}

const InvitationNotificationModal: React.FC<InvitationNotificationModalProps> = ({
  visible,
  invitation,
  onClose,
  onAccept,
  onDecline,
}) => {
  const { theme, isDarkMode } = useTheme();
  const styles = getStyles(theme, isDarkMode);
  const [responding, setResponding] = useState(false);

  if (!invitation) return null;

  const { matchDetails, role, invitedByName } = invitation.data;
  const matchDate = new Date(matchDetails.matchDate);

  const handleAccept = async () => {
    try {
      setResponding(true);
      
      const response = await BroadcastService.acceptInvitation(invitation.matchId);
      
      if (response.success) {
        Alert.alert(
          'Invitation Accepted',
          `You've joined the match as ${role}. The match has been added to your dashboard with your assigned role.`,
          [
            {
              text: 'View Dashboard',
              onPress: () => {
                onAccept?.(invitation.matchId);
                onClose();
                // Navigate to dashboard or refresh matches
              }
            }
          ]
        );
      } else {
        Alert.alert('Error', response.error || 'Failed to accept invitation');
      }
    } catch (error: any) {
      console.error('Failed to accept invitation:', error);
      Alert.alert('Error', error.message || 'Failed to accept invitation');
    } finally {
      setResponding(false);
    }
  };

  const handleDecline = () => {
    Alert.alert(
      'Decline Invitation',
      'Are you sure you want to decline this match invitation?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Decline',
          style: 'destructive',
          onPress: () => {
            onDecline?.(invitation.id);
            onClose();
          }
        }
      ]
    );
  };

  const formatMatchDate = () => {
    return matchDate.toLocaleDateString('en-GB', {
      weekday: 'short',
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getRoleIcon = (role: string) => {
    switch (role.toLowerCase()) {
      case 'referee': return 'sports-soccer';
      case 'ar1':
      case 'ar2': return 'flag';
      case '4th': return 'timer';
      default: return 'person';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'referee': return '#4CAF50';
      case 'ar1': return '#2196F3';
      case 'ar2': return '#FF9800';
      case '4th': return '#9C27B0';
      default: return theme.primary;
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          {/* Header */}
          <View style={styles.header}>
            <View style={[styles.iconContainer, { backgroundColor: `${getRoleColor(role)}20` }]}>
              <MaterialIcons name={getRoleIcon(role)} size={32} color={getRoleColor(role)} />
            </View>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <MaterialIcons name="close" size={24} color={theme.textSecondary} />
            </TouchableOpacity>
          </View>

          {/* Content */}
          <View style={styles.content}>
            <Text style={styles.title}>Match Invitation</Text>
            <Text style={styles.subtitle}>
              {invitedByName} has invited you to officiate as {role}
            </Text>

            {/* Match Details */}
            <View style={styles.matchCard}>
              <View style={styles.matchHeader}>
                <Text style={styles.matchTitle}>{matchDetails.competition}</Text>
                <Text style={styles.matchDate}>{formatMatchDate()}</Text>
              </View>
              
              <View style={styles.teamsContainer}>
                <Text style={styles.teamName}>{matchDetails.homeTeam}</Text>
                <Text style={styles.vsText}>vs</Text>
                <Text style={styles.teamName}>{matchDetails.awayTeam}</Text>
              </View>
              
              <View style={styles.venueContainer}>
                <MaterialIcons name="location-on" size={16} color={theme.textSecondary} />
                <Text style={styles.venueText}>{matchDetails.venue}</Text>
              </View>
            </View>

            {/* Role Badge */}
            <View style={styles.roleContainer}>
              <View style={[styles.roleBadge, { backgroundColor: getRoleColor(role) }]}>
                <MaterialIcons name={getRoleIcon(role)} size={20} color="#fff" />
                <Text style={styles.roleText}>{role}</Text>
              </View>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.declineButton]}
              onPress={handleDecline}
              disabled={responding}
            >
              <MaterialIcons name="close" size={20} color="#fff" />
              <Text style={styles.buttonText}>Decline</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.button, styles.acceptButton]}
              onPress={handleAccept}
              disabled={responding}
            >
              {responding ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <>
                  <MaterialIcons name="check" size={20} color="#fff" />
                  <Text style={styles.buttonText}>Accept</Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  container: {
    backgroundColor: theme.background,
    borderRadius: 16,
    width: '100%',
    maxWidth: 400,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButton: {
    padding: 8,
  },
  content: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.text,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: theme.textSecondary,
    marginBottom: 20,
  },
  matchCard: {
    backgroundColor: theme.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  matchHeader: {
    marginBottom: 12,
  },
  matchTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.text,
    marginBottom: 4,
  },
  matchDate: {
    fontSize: 14,
    color: theme.textSecondary,
  },
  teamsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  teamName: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.text,
    flex: 1,
    textAlign: 'center',
  },
  vsText: {
    fontSize: 14,
    color: theme.textSecondary,
    marginHorizontal: 12,
  },
  venueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  venueText: {
    fontSize: 14,
    color: theme.textSecondary,
    marginLeft: 4,
  },
  roleContainer: {
    alignItems: 'center',
  },
  roleBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 8,
  },
  roleText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fff',
  },
  buttonContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 20,
    gap: 12,
  },
  button: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 8,
    gap: 8,
  },
  acceptButton: {
    backgroundColor: '#4CAF50',
  },
  declineButton: {
    backgroundColor: '#F44336',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
});

export default InvitationNotificationModal;
