import { Controller, Get, Post, Body, Patch, Param, Delete, HttpStatus, HttpException } from '@nestjs/common';
import { TemplatesService } from './templates.service';
import { CreateTemplateDto } from './dto/create-template.dto';
import { UpdateTemplateDto } from './dto/update-template.dto';

@Controller('templates')
export class Templates<PERSON>ontroller {
    constructor(private readonly templatesService: TemplatesService) { }

    @Post()
    async create(@Body() createTemplateDto: CreateTemplateDto) {
        try {
            const template = await this.templatesService.create(createTemplateDto);
            return {
                success: true,
                data: template
            };
        } catch (error) {
            console.error('Error creating template:', error);
            throw new HttpException({
                success: false,
                error: 'Failed to create template'
            }, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Get(':userId')
    async findAllByUser(@Param('userId') userId: string) {
        try {
            const templates = await this.templatesService.findAllByUser(userId);
            return {
                success: true,
                data: templates
            };
        } catch (error) {
            console.error('Error fetching templates:', error);
            throw new HttpException({
                success: false,
                error: 'Failed to fetch templates'
            }, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Get('single/:id')
    async findOne(@Param('id') id: string) {
        try {
            const template = await this.templatesService.findOne(id);
            if (!template) {
                throw new HttpException({
                    success: false,
                    error: 'Template not found'
                }, HttpStatus.NOT_FOUND);
            }
            return {
                success: true,
                data: template
            };
        } catch (error) {
            console.error('Error fetching template:', error);
            throw new HttpException({
                success: false,
                error: 'Failed to fetch template'
            }, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Patch(':id')
    async update(@Param('id') id: string, @Body() updateTemplateDto: UpdateTemplateDto) {
        try {
            const template = await this.templatesService.update(id, updateTemplateDto);
            return {
                success: true,
                data: template
            };
        } catch (error) {
            console.error('Error updating template:', error);
            throw new HttpException({
                success: false,
                error: 'Failed to update template'
            }, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }



    @Delete(':id')
    async remove(@Param('id') id: string) {
        try {
            await this.templatesService.remove(id);
            return {
                success: true,
                message: 'Template deleted successfully'
            };
        } catch (error) {
            console.error('Error deleting template:', error);
            throw new HttpException({
                success: false,
                error: 'Failed to delete template'
            }, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}