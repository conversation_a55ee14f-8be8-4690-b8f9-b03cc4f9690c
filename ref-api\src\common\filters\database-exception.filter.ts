import { 
  ExceptionFilter, 
  Catch, 
  ArgumentsHost, 
  HttpStatus,
  Logger
} from '@nestjs/common';
import { Response } from 'express';
import { PrismaClientKnownRequestError, PrismaClientValidationError } from '@prisma/client/runtime/library';

@Catch(PrismaClientKnownRequestError, PrismaClientValidationError)
export class DatabaseExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(DatabaseExceptionFilter.name);

  catch(exception: PrismaClientKnownRequestError | PrismaClientValidationError, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest();

    // Log the full error for debugging (server-side only)
    this.logger.error('Database error occurred', {
      error: exception.message,
      code: 'code' in exception ? exception.code : 'VALIDATION_ERROR',
      path: request.url,
      method: request.method,
      userId: request.user?.id || 'anonymous',
      timestamp: new Date().toISOString()
    });

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'An internal server error occurred';
    let userMessage = 'Something went wrong. Please try again later.';

    if (exception instanceof PrismaClientKnownRequestError) {
      switch (exception.code) {
        case 'P2002':
          status = HttpStatus.CONFLICT;
          message = 'Unique constraint violation';
          userMessage = 'This record already exists.';
          break;
        case 'P2025':
          status = HttpStatus.NOT_FOUND;
          message = 'Record not found';
          userMessage = 'The requested resource was not found.';
          break;
        case 'P2003':
          status = HttpStatus.BAD_REQUEST;
          message = 'Foreign key constraint violation';
          userMessage = 'Invalid reference to related data.';
          break;
        case 'P2014':
          status = HttpStatus.BAD_REQUEST;
          message = 'Required relation violation';
          userMessage = 'Missing required related data.';
          break;
        case 'P2023':
          status = HttpStatus.BAD_REQUEST;
          message = 'Inconsistent column data';
          userMessage = 'Invalid data format provided.';
          break;
        default:
          // Don't expose unknown database errors
          break;
      }
    } else if (exception instanceof PrismaClientValidationError) {
      status = HttpStatus.BAD_REQUEST;
      message = 'Data validation error';
      userMessage = 'The provided data is invalid.';
    }

    response.status(status).json({
      success: false,
      statusCode: status,
      message: userMessage,
      timestamp: new Date().toISOString(),
      path: request.url
    });
  }
}