-- CreateTable
CREATE TABLE "templates" (
    "id" TEXT NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "teamSize" INTEGER NOT NULL DEFAULT 11,
    "benchSize" INTEGER NOT NULL DEFAULT 5,
    "numberOfPeriods" INTEGER NOT NULL DEFAULT 2,
    "periodLengths" JSONB NOT NULL,
    "halfTime" INTEGER NOT NULL DEFAULT 15,
    "extraTime" BOOLEAN NOT NULL DEFAULT false,
    "extraTimeLengths" JSONB,
    "subOpportunities" BOOLEAN NOT NULL DEFAULT false,
    "subOpportunitiesAllowance" INTEGER NOT NULL DEFAULT 1,
    "extraTimeSubOpportunities" BOOLEAN NOT NULL DEFAULT false,
    "extraTimeSubOpportunitiesAllowance" INTEGER NOT NULL DEFAULT 1,
    "penalties" BOOLEAN NOT NULL DEFAULT false,
    "misconductCode" VARCHAR(50) NOT NULL DEFAULT 'Wales',
    "temporaryDismissals" BOOLEAN NOT NULL DEFAULT false,
    "temporaryDismissalsTime" INTEGER NOT NULL DEFAULT 10,
    "injuryTimeAllowance" BOOLEAN NOT NULL DEFAULT false,
    "injuryTimeSubs" INTEGER NOT NULL DEFAULT 0,
    "injuryTimeSanctions" INTEGER NOT NULL DEFAULT 0,
    "injuryTimeGoals" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT NOT NULL,

    CONSTRAINT "templates_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "templates" ADD CONSTRAINT "templates_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
