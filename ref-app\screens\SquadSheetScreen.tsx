import React, { useState, useRef, useCallback } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Platform,
  TextInput,
  LayoutAnimation,
  UIManager,
  StatusBar,
  KeyboardAvoidingView,
  Alert,
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Header from '../components/Header';
import { useTheme } from '../contexts/ThemeContext';
import SquadService from '../api/squadService';

// Import all icons and colors from the new file
import {
  AppColors,
  CloseCircleIcon,
  UserPlusIcon,
  CaptainIcon,
  GoalkeeperIcon,
  SwapIcon,
  UpDownArrowIcon,
  MainCaptainAssignIcon,
} from '../components/icons'; // Adjust path if needed

// Type definitions
interface Player {
  id: string;
  number: number;
  name: string;
  isCaptain: boolean;
  isGoalkeeper: boolean;
  isBeingSwapped: boolean;
  isCaptainSelectable?: boolean;
}

interface TeamOfficial {
  id: string;
  role: string;
  name: string;
  isEditing: boolean;
  tempRole: string;
  tempName: string;
}

interface SwapSelection {
  list: string;
  id: string;
}

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

let nextId = 100;

const initialStarters = Array.from({ length: 11 }, (_, i) => ({
  id: `S${i + 1}`,
  number: i + 1,
  name: `Player ${i + 1}`,
  isCaptain: i === 0,
  isGoalkeeper: i === 0,
  isBeingSwapped: false,
  isCaptainSelectable: false
}));

const initialSubstitutes = Array.from({ length: 5 }, (_, i) => ({
  id: `B${i + 1}`,
  number: 11 + i + 1,
  name: `Player ${11 + i + 1}`,
  isCaptain: false,
  isGoalkeeper: false,
  isBeingSwapped: false
}));

const initialTeamOfficials = [
  {
    id: `TO1`,
    role: 'Manager',
    name: '.......',
    isEditing: false,
    tempRole: 'Manager',
    tempName: '.......'
  },
  {
    id: `TO2`,
    role: 'Assistant Manager',
    name: '.......',
    isEditing: false,
    tempRole: 'Assistant Manager',
    tempName: '.......'
  },
  {
    id: `TO3`,
    role: 'Coach',
    name: '.......',
    isEditing: false,
    tempRole: 'Coach',
    tempName: '.......'
  },
  {
    id: `TO4`,
    role: 'Doctor',
    name: '.......',
    isEditing: false,
    tempRole: 'Doctor',
    tempName: '.......'
  },
];

// Get team size from match data (defaults to 11 if not specified)
const getTeamSizeFromMatchData = (matchData: any) => {
  const teamSize = matchData?.teamSize || 11;
  console.log('📋 Squad Sheet - Team Size:', teamSize);
  console.log('📋 Squad Sheet - Match Data Debug:', {
    hasMatchData: !!matchData,
    teamSize: matchData?.teamSize,
    templateName: matchData?.templateName,
    matchDataKeys: matchData ? Object.keys(matchData) : 'no matchData'
  });
  return teamSize;
};

// Generate initial starters based on team size
const generateInitialStarters = (teamSize: number) => {
  return Array.from({ length: teamSize }, (_, i) => ({
    id: `S${i + 1}`,
    number: i + 1,
    name: `Player ${i + 1}`,
    isCaptain: i === 0,
    isGoalkeeper: i === 0,
    isBeingSwapped: false,
    isCaptainSelectable: false
  }));
};

const SquadSheetScreen = ({ route }: { route: any }) => {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const styles = getStyles(theme);

  // Get team data from route params if available
  const teamData = route?.params?.teamData;
  const teamName = 'Squad Sheet';

  // Get match data from route params
  const matchId = route?.params?.matchId;
  const teamType = route?.params?.teamType; // 'home' or 'away'
  const matchData = route?.params?.matchData; // Pass match data from previous screen

  // Get team size from match data
  const getTeamSize = () => getTeamSizeFromMatchData(matchData);

  const [starters, setStarters] = useState<Player[]>(() => generateInitialStarters(getTeamSizeFromMatchData(matchData)));
  const [substitutes, setSubstitutes] = useState<Player[]>(initialSubstitutes);
  const [teamOfficials, setTeamOfficials] = useState<TeamOfficial[]>(initialTeamOfficials);
  const [selectedPlayerForSwap, setSelectedPlayerForSwap] = useState<SwapSelection | null>(null);
  const [isAssigningCaptain, setIsAssigningCaptain] = useState(false);
  const [loading, setLoading] = useState(false);
  const [existingSquadId, setExistingSquadId] = useState<string | undefined>(undefined);

  // Refs for keyboard handling
  const scrollViewRef = useRef<ScrollView>(null);

  // Load existing squad data when screen loads
  const loadSquadData = async () => {
    if (!matchId || !teamType) return;

    try {
      setLoading(true);
      const response = await SquadService.getSquadByMatch(matchId, teamType);

      if (response.success && response.data) {
        const { starters: loadedStarters, substitutes: loadedSubstitutes, officials: loadedOfficials } =
          SquadService.convertFromApiFormat(response.data);

        setStarters(loadedStarters);
        setSubstitutes(loadedSubstitutes);
        setTeamOfficials(loadedOfficials);
        setExistingSquadId(response.data.id);
      } else if (response.isExpectedError) {
        // Squad doesn't exist yet - this is normal for new matches
        console.log(`No squad data found for ${teamType} team - starting with empty squad sheet`);
      }
    } catch (error) {
      console.log('No existing squad data found - starting with empty squad sheet');
    } finally {
      setLoading(false);
    }
  };

  // Load data when screen focuses
  useFocusEffect(
    React.useCallback(() => {
      loadSquadData();
    }, [matchId, teamType])
  );

  const animateListUpdate = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
  };

  const handleSave = async () => {
    if (!matchId || !teamType) {
      Alert.alert('Error', 'Missing match or team information');
      return;
    }

    try {
      setLoading(true);

      // Convert current state to API format
      const squadData = SquadService.convertToApiFormat(
        matchId,
        teamType,
        starters,
        substitutes,
        teamOfficials,
        existingSquadId
      );

      const response = await SquadService.saveSquad(squadData);

      if (response.success) {
        Alert.alert(
          'Success',
          'Squad sheet saved successfully!',
          [
            {
              text: 'OK',
              onPress: () => navigation.goBack()
            }
          ]
        );
      } else {
        Alert.alert('Error', response.error || 'Failed to save squad sheet');
      }
    } catch (error) {
      console.error('Error saving squad data:', error);
      Alert.alert('Error', 'Failed to save squad sheet');
    } finally {
      setLoading(false);
    }
  };

  const handleAddPlayer = (listType: string) => {
    const teamSize = getTeamSize();
    
    // Prevent adding more starters than team size allows
    if (listType === 'starters' && starters.length >= teamSize) {
      Alert.alert(
        'Maximum Players Reached',
        `You can only have ${teamSize} players in the starting lineup for this match format.`,
        [{ text: 'OK' }]
      );
      return;
    }

    animateListUpdate();
    const newPlayer = {
      id: `P${nextId++}`,
      number: listType === 'starters' ? starters.length + 1 : substitutes.length + 1 + starters.length,
      name: 'New Player',
      isCaptain: false,
      isGoalkeeper: false,
      isBeingSwapped: false,
      isCaptainSelectable: false
    };

    if (listType === 'starters') {
      setStarters([...starters, newPlayer]);
    } else if (listType === 'substitutes') {
      setSubstitutes([...substitutes, newPlayer]);
    }
  };

  const handleRemovePlayer = (listType: string, id: string) => {
    animateListUpdate();
    if (listType === 'starters') {
      setStarters(starters.filter(p => p.id !== id));
    } else {
      setSubstitutes(substitutes.filter(p => p.id !== id));
    }

    if (selectedPlayerForSwap && selectedPlayerForSwap.id === id) {
      setSelectedPlayerForSwap(null);
    }
  };

  const handlePlayerNameChange = (listType: string, id: string, newName: string) => {
    if (listType === 'starters') {
      setStarters(starters.map(p => p.id === id ? { ...p, name: newName } : p));
    } else {
      setSubstitutes(substitutes.map(p => p.id === id ? { ...p, name: newName } : p));
    }
  };

  const handlePlayerNumberChange = (listType: string, id: string, newNumber: string) => {
    const num = parseInt(newNumber, 10);
    if (listType === 'starters') {
      setStarters(starters.map(p => p.id === id ? { ...p, number: isNaN(num) ? 0 : num } : p));
    } else {
      setSubstitutes(substitutes.map(p => p.id === id ? { ...p, number: isNaN(num) ? 0 : num } : p));
    }
  };

  const handleMainCaptainIconPress = () => {
    animateListUpdate();
    const newAssigningState = !isAssigningCaptain;
    setIsAssigningCaptain(newAssigningState);
    setStarters(starters.map(p => ({ ...p, isCaptainSelectable: newAssigningState })));

    if (!newAssigningState) {
      setStarters(starters.map(p => ({ ...p, isCaptainSelectable: false })));
    }
  };

  const assignCaptain = (playerId: string) => {
    if (!isAssigningCaptain) return;

    animateListUpdate();
    setStarters(starters.map(p => ({
      ...p,
      isCaptain: p.id === playerId,
      isCaptainSelectable: false
    })));
    setIsAssigningCaptain(false);
  };

  const toggleGoalkeeper = (listType: string, id: string) => {
    if (listType !== 'starters') return;

    animateListUpdate();
    setStarters(starters.map(p => ({
      ...p,
      isGoalkeeper: p.id === id ? !p.isGoalkeeper : false
    })));
  };

  const handleSwapIconPress = (listType: string, playerId: string) => {
    animateListUpdate();

    if (!selectedPlayerForSwap) {
      setSelectedPlayerForSwap({ list: listType, id: playerId });
      if (listType === 'starters')
        setStarters(starters.map(p => p.id === playerId ? { ...p, isBeingSwapped: true } : { ...p, isBeingSwapped: false }));
      else
        setSubstitutes(substitutes.map(p => p.id === playerId ? { ...p, isBeingSwapped: true } : { ...p, isBeingSwapped: false }));
    } else {
      const firstPlayerList = selectedPlayerForSwap.list;
      const firstPlayerId = selectedPlayerForSwap.id;
      const secondPlayerList = listType;
      const secondPlayerId = playerId;

      if (firstPlayerList === secondPlayerList && firstPlayerId === secondPlayerId) {
        setSelectedPlayerForSwap(null);
        if (listType === 'starters')
          setStarters(starters.map(p => ({ ...p, isBeingSwapped: false })));
        else
          setSubstitutes(substitutes.map(p => ({ ...p, isBeingSwapped: false })));
        return;
      }

      if (firstPlayerList === secondPlayerList) {
        console.log("Reordering within same list is more complex, placeholder action.");
        setSelectedPlayerForSwap(null);
        if (listType === 'starters')
          setStarters(starters.map(p => ({ ...p, isBeingSwapped: false })));
        else
          setSubstitutes(substitutes.map(p => ({ ...p, isBeingSwapped: false })));
        return;
      }

      let player1: Player | undefined, player2: Player | undefined;
      let player1Index: number = -1, player2Index: number = -1;

      if (firstPlayerList === 'starters') {
        player1 = starters.find((p, i) => {
          if (p.id === firstPlayerId) {
            player1Index = i;
            return true;
          }
        });
      } else {
        player1 = substitutes.find((p, i) => {
          if (p.id === firstPlayerId) {
            player1Index = i;
            return true;
          }
        });
      }

      if (secondPlayerList === 'starters') {
        player2 = starters.find((p, i) => {
          if (p.id === secondPlayerId) {
            player2Index = i;
            return true;
          }
        });
      } else {
        player2 = substitutes.find((p, i) => {
          if (p.id === secondPlayerId) {
            player2Index = i;
            return true;
          }
        });
      }

      if (player1 && player2) {
        const newStarters = [...starters];
        const newSubstitutes = [...substitutes];

        if (firstPlayerList === 'starters' && secondPlayerList === 'substitutes') {
          newStarters[player1Index!] = { ...player2, isBeingSwapped: false, isCaptain: player1.isCaptain, isGoalkeeper: player1.isGoalkeeper };
          newSubstitutes[player2Index!] = { ...player1, isBeingSwapped: false, isCaptain: false, isGoalkeeper: false };
        } else if (firstPlayerList === 'substitutes' && secondPlayerList === 'starters') {
          newSubstitutes[player1Index!] = { ...player2, isBeingSwapped: false, isCaptain: false, isGoalkeeper: false };
          newStarters[player2Index!] = { ...player1, isBeingSwapped: false, isCaptain: player2.isCaptain, isGoalkeeper: player2.isGoalkeeper };
        }

        setStarters(newStarters.map(p => ({ ...p, isBeingSwapped: false })));
        setSubstitutes(newSubstitutes.map(p => ({ ...p, isBeingSwapped: false })));
      }

      setSelectedPlayerForSwap(null);
    }
  };

  const handleAddTeamOfficial = () => {
    animateListUpdate();
    setTeamOfficials([...teamOfficials, {
      id: `TO${nextId++}`,
      role: '',
      name: '',
      isEditing: true,
      tempRole: '',
      tempName: ''
    }]);
  };

  const handleRemoveTeamOfficial = (id: string) => {
    animateListUpdate();
    setTeamOfficials(teamOfficials.filter(o => o.id !== id));
  };

  const toggleEditTeamOfficial = (id: string) => {
    animateListUpdate();
    setTeamOfficials(teamOfficials.map(o => o.id === id ? {
      ...o,
      isEditing: !o.isEditing,
      tempRole: o.role,
      tempName: o.name
    } : { ...o, isEditing: false }));
  };

  const handleTeamOfficialChange = (id: string, field: string, value: string) => {
    setTeamOfficials(teamOfficials.map(o => o.id === id ? {
      ...o,
      [field === 'role' ? 'tempRole' : 'tempName']: value
    } : o));
  };

  const saveTeamOfficialChanges = (id: string) => {
    animateListUpdate();
    setTeamOfficials(teamOfficials.map(o => {
      if (o.id === id) {
        return {
          ...o,
          role: o.tempRole.trim() === "" ? o.role : o.tempRole,
          name: o.tempName.trim() === "" ? o.name : o.tempName,
          isEditing: false
        };
      }
      return o;
    }));
  };

  const handleInputFocus = (inputRef: any) => {
    // Scroll to bring the focused input into view
    setTimeout(() => {
      inputRef?.measure((x: number, y: number, width: number, height: number, pageX: number, pageY: number) => {
        scrollViewRef.current?.scrollTo({
          y: pageY - 100, // Offset to show input above keyboard
          animated: true,
        });
      });
    }, 100);
  };

  const renderPlayerRow = (player: Player, listType: string) => (
    <TouchableOpacity
      key={player.id}
      style={[
        styles.playerRow,
        player.isBeingSwapped && styles.playerRowSelected,
        player.isCaptainSelectable && styles.playerRowCaptainSelectable,
      ]}
      onPress={isAssigningCaptain && listType === 'starters' ? () => assignCaptain(player.id) : undefined}
      disabled={isAssigningCaptain && listType !== 'starters'}
    >
      <TextInput
        style={styles.playerNumberInput}
        value={String(player.number)}
        onChangeText={(text) => handlePlayerNumberChange(listType, player.id, text)}
        keyboardType="number-pad"
        maxLength={2}
        editable={!isAssigningCaptain}
        onFocus={(e) => handleInputFocus(e.target)}
      />
      <TextInput
        style={styles.playerNameInput}
        value={player.name}
        onChangeText={(text) => handlePlayerNameChange(listType, player.id, text)}
        placeholder="Player Name"
        editable={!isAssigningCaptain}
        onFocus={(e) => handleInputFocus(e.target)}
      />
      <View style={styles.playerStatusIconsContainer}>
        {player.isCaptain && (
          <View style={{ marginLeft: 10, transform: [{ translateY: 4 }] }}>
            <CaptainIcon size={38} />
          </View>
        )}
        {player.isGoalkeeper && (
          <View style={{ marginLeft: 15 }}>
            <GoalkeeperIcon size={23} color={AppColors.textBlack} />
          </View>
        )}
      </View>
      <TouchableOpacity
        onPress={() => !isAssigningCaptain && handleRemovePlayer(listType, player.id)}
        style={styles.actionButton}
        disabled={isAssigningCaptain}
      >
        <CloseCircleIcon />
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => !isAssigningCaptain && handleSwapIconPress(listType, player.id)}
        style={styles.actionButton}
        disabled={isAssigningCaptain}
      >
        <SwapIcon
          color={selectedPlayerForSwap && selectedPlayerForSwap.id === player.id ? AppColors.primaryGreen : AppColors.textBlack}
        />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  const renderTeamOfficialRow = (official: TeamOfficial) => {
    if (official.isEditing) {
      return (
        <View key={official.id} style={[styles.playerRow, styles.editingOfficialRow]}>
          <TextInput
            style={styles.officialRoleInput}
            value={official.tempRole}
            onChangeText={(text) => handleTeamOfficialChange(official.id, 'role', text)}
            placeholder="Role"
            autoFocus
          />
          <TextInput
            style={styles.officialNameInputWide}
            value={official.tempName}
            onChangeText={(text) => handleTeamOfficialChange(official.id, 'name', text)}
            placeholder="Name"
          />
          <TouchableOpacity
            onPress={() => saveTeamOfficialChanges(official.id)}
            style={styles.saveOfficialButton}
          >
            <Text style={styles.saveButtonText}>Save</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <TouchableOpacity
        key={official.id}
        style={styles.playerRow}
        onLongPress={() => toggleEditTeamOfficial(official.id)}
      >
        <UpDownArrowIcon />
        <Text style={styles.officialRoleText}>{official.role}</Text>
        <Text style={styles.officialNameText}>{official.name}</Text>
        <TouchableOpacity
          onPress={() => handleRemoveTeamOfficial(official.id)}
          style={styles.actionButton}
        >
          <CloseCircleIcon />
        </TouchableOpacity>
      </TouchableOpacity>
    );
  };

  return (
    <KeyboardAvoidingView
      style={styles.screenContainer}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <Header
        title={teamName}
        showBackButton={true}
        rightComponent={
          <TouchableOpacity onPress={handleSave}>
            <Text style={styles.saveButtonHeader}>Save</Text>
          </TouchableOpacity>
        }
      />
      <ScrollView
        ref={scrollViewRef}
        contentContainerStyle={styles.scrollContentContainer}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.squadSection}>
          <View style={styles.squadSectionHeader}>
            <Text style={styles.squadSectionTitle}>
              Starting Line Up ({starters.length}/{getTeamSize()})
            </Text>
            <TouchableOpacity onPress={handleMainCaptainIconPress}>
              <MainCaptainAssignIcon
                color={isAssigningCaptain ? AppColors.primaryGreen : AppColors.textBlack}
              />
            </TouchableOpacity>
          </View>
          {starters.map(player => renderPlayerRow(player, 'starters'))}
          {starters.length < getTeamSize() && (
            <TouchableOpacity 
              style={styles.addPlayerButton} 
              onPress={() => handleAddPlayer('starters')}
            >
              <UserPlusIcon />
              <Text style={styles.addPlayerText}>Add Player</Text>
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.squadSection}>
          <View style={styles.squadSectionHeader}>
            <Text style={styles.squadSectionTitle}>Substitutes</Text>
            <TouchableOpacity onPress={() => handleAddPlayer('substitutes')}>
              <UserPlusIcon />
            </TouchableOpacity>
          </View>
          {substitutes.map(player => renderPlayerRow(player, 'substitutes'))}
        </View>

        <View style={styles.squadSection}>
          <View style={styles.squadSectionHeader}>
            <Text style={styles.squadSectionTitle}>Team Officials</Text>
            <TouchableOpacity onPress={handleAddTeamOfficial}>
              <UserPlusIcon />
            </TouchableOpacity>
          </View>
          {teamOfficials.map(official => renderTeamOfficialRow(official))}
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const getStyles = (theme: any) => StyleSheet.create({
  screenContainer: {
    flex: 1,
    backgroundColor: theme.background,
  },
  saveButtonHeader: {
    color: theme.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  scrollContentContainer: {
    padding: 15,
    paddingBottom: 30
  },
  squadSection: {
    backgroundColor: theme.card,
    borderRadius: 12,
    padding: 15,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.10,
    shadowRadius: 2.00,
    elevation: 2,
  },
  squadSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10
  },
  squadSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.text
  },
  playerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.border,
    paddingHorizontal: 10,
    paddingVertical: 5,
    marginBottom: 8,
    minHeight: 50
  },
  playerRowSelected: {
    borderColor: AppColors.primaryGreen,
    borderWidth: 1.5,
    backgroundColor: '#E8F5E9'
  },
  playerRowCaptainSelectable: {
    borderColor: AppColors.captainSelectBorderColor,
    borderWidth: 1.5,
    backgroundColor: '#E8F5E9'
  },
  playerNumberInput: {
    fontSize: 14,
    color: theme.textSecondary,
    fontWeight: '500',
    width: 30,
    textAlign: 'center',
    paddingVertical: 8,
    marginRight: 5
  },
  playerNameInput: {
    flex: 1,
    fontSize: 14,
    color: theme.text,
    paddingVertical: 8,
    marginRight: 5
  },
  playerStatusIconsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    minWidth: 50,
    gap: 8,
    marginRight: 5,
  },
  actionButton: {
    padding: 6,
    marginLeft: 5
  },
  officialRoleText: {
    flex: 1,
    fontSize: 14,
    color: theme.text,
    marginLeft: 10,
    fontWeight: '500'
  },
  officialNameText: {
    flex: 1.5,
    fontSize: 14,
    color: theme.textSecondary,
    textAlign: 'right',
    marginRight: 5
  },
  editingOfficialRow: {
    paddingVertical: 8
  },
  officialRoleInput: {
    flex: 1,
    fontSize: 14,
    color: theme.text,
    paddingVertical: 8,
    marginRight: 5,
    borderBottomWidth: 0.5,
    borderBottomColor: theme.border
  },
  officialNameInputWide: {
    flex: 1.5,
    fontSize: 14,
    color: theme.textSecondary,
    paddingVertical: 8,
    marginRight: 5,
    borderBottomWidth: 0.5,
    borderBottomColor: theme.border,
    textAlign: 'right'
  },
  saveOfficialButton: {
    backgroundColor: AppColors.primaryGreen,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 5,
    marginLeft: 5
  },
  saveButtonText: {
    color: AppColors.screenBackground,
    fontSize: 12,
    fontWeight: 'bold'
  },
  addPlayerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.border,
    borderStyle: 'dashed',
    paddingHorizontal: 10,
    paddingVertical: 15,
    marginBottom: 8,
    minHeight: 50
  },
  addPlayerText: {
    fontSize: 14,
    color: theme.textSecondary,
    marginLeft: 8,
    fontStyle: 'italic'
  }
});

export default SquadSheetScreen;