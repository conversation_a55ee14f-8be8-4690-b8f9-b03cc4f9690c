import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { DeviceEventEmitter } from 'react-native';
import { useAuth } from './AuthContext';
import UserService, { UserProfile } from '../api/userService';
import NotificationService from '../services/NotificationService';

interface UserContextType {
  userProfile: UserProfile | null;
  loading: boolean;
  error: string | null;
  refreshProfile: () => Promise<void>;
}

interface UserProviderProps {
  children: ReactNode;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  const fetchUserProfile = async () => {
    if (!user) {
      setUserProfile(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      console.log('🔄 Fetching user profile...');
      
      // Ensure we have a fresh token and set it in the API client
      const idToken = await user.getIdToken(true);
      console.log('🔑 Got fresh ID token:', idToken ? 'Yes' : 'No');
      
      // Import apiClient here to avoid circular dependency
      const { apiClient } = await import('../api/client');
      apiClient.setAuthToken(idToken);
      console.log('🔑 Token set in API client');
      
      const response = await UserService.getProfile();
      
      if (response.success && response.data) {
        console.log('✅ User profile fetched successfully:', response.data);
        setUserProfile(response.data);

        // Initialize notification service for this user
        try {
          console.log('🔔 Initializing notification service for user ID:', response.data.id);
          await NotificationService.initialize(response.data.id, idToken);
          console.log('🔔 Notification service initialized');

          // Emit event that notification service is ready
          DeviceEventEmitter.emit('notificationServiceReady');
        } catch (notificationError) {
          console.error('❌ Failed to initialize notification service:', notificationError);
        }
      } else {
        setError(response.error || 'Failed to fetch user profile');
        console.error('❌ Failed to fetch user profile:', response.error);
      }
    } catch (error: any) {
      setError(error.message || 'Failed to fetch user profile');
      console.error('❌ Error fetching user profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const refreshProfile = async () => {
    await fetchUserProfile();
  };

  // Fetch profile when user changes
  useEffect(() => {
    if (user) {
      fetchUserProfile();
    } else {
      setUserProfile(null);
      setError(null);
      // Disconnect notification service when user logs out
      NotificationService.disconnect();
    }
  }, [user]);

  const value: UserContextType = {
    userProfile,
    loading,
    error,
    refreshProfile,
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

export default UserContext;