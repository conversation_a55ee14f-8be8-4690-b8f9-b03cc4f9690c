import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import WebSocketService, { ActionNotification } from '../api/websocketService';

interface ActionNotificationModalProps {
  visible: boolean;
  notification: ActionNotification | null;
  onClose: () => void;
  onResponse?: (actionId: string, response: 'accept' | 'decline') => void;
}

const ActionNotificationModal: React.FC<ActionNotificationModalProps> = ({
  visible,
  notification,
  onClose,
  onResponse,
}) => {
  const { theme, isDarkMode } = useTheme();
  const styles = getStyles(theme, isDarkMode);
  const [responding, setResponding] = useState(false);

  if (!notification) return null;

  const getActionIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'card':
      case 'red_card':
      case 'yellow_card':
        return 'credit-card';
      case 'goal':
        return 'sports-soccer';
      case 'substitution':
        return 'swap-horiz';
      case 'period_start':
      case 'period_end':
        return 'timer';
      default:
        return 'notification-important';
    }
  };

  const getActionColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'red_card':
        return '#F44336';
      case 'yellow_card':
        return '#FF9800';
      case 'goal':
        return '#4CAF50';
      case 'substitution':
        return '#2196F3';
      default:
        return theme.primary;
    }
  };

  const getActionTitle = (type: string, data: any) => {
    switch (type.toLowerCase()) {
      case 'red_card':
        return `Red Card - ${data.playerName || 'Player'} #${data.playerNumber || '?'}`;
      case 'yellow_card':
        return `Yellow Card - ${data.playerName || 'Player'} #${data.playerNumber || '?'}`;
      case 'goal':
        return `Goal - ${data.playerName || 'Player'} #${data.playerNumber || '?'}`;
      case 'substitution':
        return `Substitution - ${data.playerIn?.name || 'Player'} in for ${data.playerOut?.name || 'Player'}`;
      case 'period_start':
        return `Period ${data.period || '?'} Started`;
      case 'period_end':
        return `Period ${data.period || '?'} Ended`;
      default:
        return `${type.replace('_', ' ').toUpperCase()}`;
    }
  };

  const getActionDescription = (type: string, data: any) => {
    const time = data.time || data.minute || '?';
    const team = data.team === 'home' ? 'Home Team' : data.team === 'away' ? 'Away Team' : 'Team';
    
    switch (type.toLowerCase()) {
      case 'red_card':
      case 'yellow_card':
        return `${team} • ${time}' • ${data.code || 'Misconduct'}`;
      case 'goal':
        return `${team} • ${time}' • Score: ${data.homeScore || 0}-${data.awayScore || 0}`;
      case 'substitution':
        return `${team} • ${time}'`;
      case 'period_start':
      case 'period_end':
        return `Match time: ${time}'`;
      default:
        return `${team} • ${time}'`;
    }
  };

  const handleResponse = async (response: 'accept' | 'decline') => {
    if (!notification) return;

    try {
      setResponding(true);
      
      // Send response via WebSocket
      WebSocketService.respondToAction(notification.id, response);
      
      // Call callback if provided
      onResponse?.(notification.id, response);
      
      // Show feedback
      Alert.alert(
        'Response Sent',
        `You ${response}ed the ${notification.type.replace('_', ' ')} action.`,
        [{ text: 'OK', onPress: onClose }]
      );
      
    } catch (error) {
      console.error('Failed to respond to action:', error);
      Alert.alert('Error', 'Failed to send response. Please try again.');
    } finally {
      setResponding(false);
    }
  };

  const actionColor = getActionColor(notification.type);
  const actionIcon = getActionIcon(notification.type);
  const actionTitle = getActionTitle(notification.type, notification.data);
  const actionDescription = getActionDescription(notification.type, notification.data);

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          {/* Header */}
          <View style={styles.header}>
            <View style={[styles.iconContainer, { backgroundColor: `${actionColor}20` }]}>
              <MaterialIcons name={actionIcon} size={32} color={actionColor} />
            </View>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <MaterialIcons name="close" size={24} color={theme.textSecondary} />
            </TouchableOpacity>
          </View>

          {/* Content */}
          <View style={styles.content}>
            <Text style={styles.title}>{actionTitle}</Text>
            <Text style={styles.description}>{actionDescription}</Text>
            
            <View style={styles.fromContainer}>
              <MaterialIcons name="person" size={16} color={theme.textSecondary} />
              <Text style={styles.fromText}>
                From: {notification.fromRole || 'Official'}
              </Text>
            </View>

            {notification.requiresApproval && (
              <View style={styles.approvalNote}>
                <MaterialIcons name="info" size={16} color={theme.primary} />
                <Text style={styles.approvalText}>
                  This action requires your approval
                </Text>
              </View>
            )}
          </View>

          {/* Action Buttons */}
          {notification.requiresApproval ? (
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.declineButton]}
                onPress={() => handleResponse('decline')}
                disabled={responding}
              >
                {responding ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <>
                    <MaterialIcons name="close" size={20} color="#fff" />
                    <Text style={styles.buttonText}>Decline</Text>
                  </>
                )}
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.button, styles.acceptButton]}
                onPress={() => handleResponse('accept')}
                disabled={responding}
              >
                {responding ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <>
                    <MaterialIcons name="check" size={20} color="#fff" />
                    <Text style={styles.buttonText}>Accept</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.okButton]}
                onPress={onClose}
              >
                <Text style={styles.buttonText}>OK</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
};

const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  container: {
    backgroundColor: theme.background,
    borderRadius: 16,
    width: '100%',
    maxWidth: 400,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButton: {
    padding: 8,
  },
  content: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.text,
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    color: theme.textSecondary,
    marginBottom: 16,
  },
  fromContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  fromText: {
    fontSize: 14,
    color: theme.textSecondary,
    marginLeft: 6,
  },
  approvalNote: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${theme.primary}10`,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginTop: 8,
  },
  approvalText: {
    fontSize: 14,
    color: theme.primary,
    marginLeft: 6,
    fontWeight: '500',
  },
  buttonContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 20,
    gap: 12,
  },
  button: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    borderRadius: 8,
    gap: 8,
  },
  acceptButton: {
    backgroundColor: '#4CAF50',
  },
  declineButton: {
    backgroundColor: '#F44336',
  },
  okButton: {
    backgroundColor: theme.primary,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
});

export default ActionNotificationModal;
