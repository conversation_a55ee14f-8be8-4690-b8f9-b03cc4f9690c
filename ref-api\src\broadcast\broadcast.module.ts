import { Module, OnModuleInit } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { BroadcastService } from './broadcast.service';
import { BroadcastGateway } from './broadcast.gateway';
import { MatchSyncService } from './match-sync.service';
import { BroadcastController } from './broadcast.controller';
import { PrismaService } from '../prisma.service';
import { AuthModule } from '../auth/auth.module';
import { UsersModule } from '../users/users.module';
import { MatchesModule } from '../matches/matches.module';
import { RedisModule } from '../redis/redis.module';

@Module({
  imports: [AuthModule, UsersModule, MatchesModule, RedisModule],
  providers: [
    BroadcastService,
    BroadcastGateway,
    MatchSyncService,
    PrismaService,
  ],
  controllers: [BroadcastController],
  exports: [BroadcastService, MatchSyncService],
})
export class BroadcastModule implements OnModuleInit {
  constructor(private moduleRef: ModuleRef) {}

  onModuleInit() {
    // Set up the gateway reference in the service to avoid circular dependency
    const broadcastService = this.moduleRef.get(BroadcastService, { strict: false });
    const broadcastGateway = this.moduleRef.get(BroadcastGateway, { strict: false });

    if (broadcastService && broadcastGateway) {
      broadcastService.setBroadcastGateway(broadcastGateway);
    }
  }
}
