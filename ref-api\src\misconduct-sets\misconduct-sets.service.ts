import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { CreateMisconductSetDto } from './dto/create-misconduct-set.dto';
import { MisconductSet } from '@prisma/client';

@Injectable()
export class MisconductSetsService {
  constructor(private prisma: PrismaService) { }

  async create(createMisconductSetDto: CreateMisconductSetDto): Promise<MisconductSet> {
    try {
      console.log('Creating misconduct set:', createMisconductSetDto);

      const result = await this.prisma.$queryRaw`
        INSERT INTO misconduct_sets (id, name, "playersYellowCodes", "playersRedCodes", "officialsYellowCodes", "officialsRedCodes", "userId", "createdAt", "updatedAt")
        VALUES (
          gen_random_uuid(), 
          ${createMisconductSetDto.name}, 
          ${JSON.stringify(createMisconductSetDto.playersYellowCodes)}::jsonb, 
          ${JSON.stringify(createMisconductSetDto.playersRedCodes)}::jsonb, 
          ${JSON.stringify(createMisconductSetDto.officialsYellowCodes)}::jsonb, 
          ${JSON.stringify(createMisconductSetDto.officialsRedCodes)}::jsonb, 
          ${createMisconductSetDto.userId}, 
          NOW(), 
          NOW()
        )
        RETURNING *;
      `;

      console.log('Create result:', result);
      return (result as any[])[0] as MisconductSet;
    } catch (error) {
      console.error('Error creating misconduct set:', error);
      throw error;
    }
  }

  async findByUser(userId: string): Promise<MisconductSet[]> {
    try {
      console.log('Fetching misconduct sets for user:', userId);

      // First check if table exists
      const tableCheck = await this.prisma.$queryRaw`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'misconduct_sets'
        );
      `;
      console.log('Table exists:', tableCheck);

      const result = await this.prisma.$queryRaw`
        SELECT * FROM misconduct_sets 
        WHERE "userId" = ${userId} 
        ORDER BY "createdAt" DESC;
      `;

      console.log('Misconduct sets found:', result);
      return (result as MisconductSet[]) || [];
    } catch (error) {
      console.error('Error fetching misconduct sets:', error);
      // Return empty array to prevent frontend crash
      return [];
    }
  }

  async update(id: string, updateMisconductSetDto: CreateMisconductSetDto): Promise<MisconductSet> {
    try {
      console.log('Updating misconduct set:', id, updateMisconductSetDto);

      const result = await this.prisma.$queryRaw`
        UPDATE misconduct_sets 
        SET 
          name = ${updateMisconductSetDto.name},
          "playersYellowCodes" = ${JSON.stringify(updateMisconductSetDto.playersYellowCodes)}::jsonb,
          "playersRedCodes" = ${JSON.stringify(updateMisconductSetDto.playersRedCodes)}::jsonb,
          "officialsYellowCodes" = ${JSON.stringify(updateMisconductSetDto.officialsYellowCodes)}::jsonb,
          "officialsRedCodes" = ${JSON.stringify(updateMisconductSetDto.officialsRedCodes)}::jsonb,
          "updatedAt" = NOW()
        WHERE id = ${id}
        RETURNING *;
      `;

      console.log('Update result:', result);
      return (result as any[])[0] as MisconductSet;
    } catch (error) {
      console.error('Error updating misconduct set:', error);
      throw error;
    }
  }

  async remove(id: string): Promise<MisconductSet> {
    try {
      console.log('Deleting misconduct set with id:', id);

      const result = await this.prisma.$queryRaw`
        DELETE FROM misconduct_sets 
        WHERE id = ${id} 
        RETURNING *;
      `;

      console.log('Delete result:', result);
      return (result as any[])[0] as MisconductSet;
    } catch (error) {
      console.error('Error deleting misconduct set:', error);
      throw error;
    }
  }

  async testDatabaseConnection(): Promise<any> {
    try {
      // Test basic connection
      await this.prisma.$connect();

      // Check if table exists
      const tableExists = await this.prisma.$queryRaw`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'misconduct_sets'
        );
      `;

      // Get table structure
      const tableStructure = await this.prisma.$queryRaw`
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'misconduct_sets' 
        ORDER BY ordinal_position;
      `;

      // Count records
      const recordCount = await this.prisma.$queryRaw`
        SELECT COUNT(*) as count FROM misconduct_sets;
      `;

      return {
        connected: true,
        tableExists,
        tableStructure,
        recordCount
      };
    } catch (error) {
      console.error('Database test error:', error);
      throw error;
    }
  }
}