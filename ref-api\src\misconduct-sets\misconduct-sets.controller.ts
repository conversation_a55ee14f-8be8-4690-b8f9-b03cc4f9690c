import { Controller, Get, Post, Body, Patch, Param, Delete, Put } from '@nestjs/common';
import { MisconductSetsService } from './misconduct-sets.service';
import { CreateMisconductSetDto } from './dto/create-misconduct-set.dto';

@Controller('misconduct-sets')
export class MisconductSetsController {
  constructor(private readonly misconductSetsService: MisconductSetsService) {}

  @Post()
  create(@Body() createMisconductSetDto: CreateMisconductSetDto) {
    return this.misconductSetsService.create(createMisconductSetDto);
  }

  @Get()
  findAll() {
    // Return empty array for now, as we don't have a findAll method
    return [];
  }

  @Get(':userId')
  findByUser(@Param('userId') userId: string) {
    return this.misconductSetsService.findByUser(userId);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updateMisconductSetDto: CreateMisconductSetDto) {
    return this.misconductSetsService.update(id, updateMisconductSetDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.misconductSetsService.remove(id);
  }

  @Get('test/database')
  testDatabase() {
    return this.misconductSetsService.testDatabaseConnection();
  }
}