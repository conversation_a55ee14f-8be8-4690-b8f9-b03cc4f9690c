import {
  <PERSON>,
  Post,
  Get,
  Body,
  Param,
  UseGuards,
  Request,
  HttpException,
  HttpStatus,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { IsString, IsOptional, IsIn } from 'class-validator';
import { MatchSyncService, SyncInvitation } from './match-sync.service';
import { BroadcastService } from './broadcast.service';
import { FirebaseAuthGuard } from '../auth/firebase-auth.guard';

export class InviteUserDto {
  @IsString()
  matchId: string;

  @IsString()
  @IsIn(['Referee', 'AR1', 'AR2', '4th'])
  role: string;

  @IsOptional()
  @IsString()
  invitedUserEmail?: string;

  @IsOptional()
  @IsString()
  invitedUserUsername?: string;
}

export class AcceptInvitationDto {
  @IsString()
  matchId: string;
}

@Controller('broadcast')
@UseGuards(FirebaseAuthGuard)
export class BroadcastController {
  constructor(
    private readonly matchSyncService: MatchSyncService,
    private readonly broadcastService: BroadcastService,
  ) {}

  /**
   * POST /broadcast/invite
   * Invite user to sync with match
   */
  @Post('invite')
  @UsePipes(new ValidationPipe({
    transform: true,
    whitelist: false, // Allow extra properties
    forbidNonWhitelisted: false
  }))
  async inviteUser(@Body() inviteDto: InviteUserDto, @Request() req: any) {
    try {
      if (!inviteDto.invitedUserEmail && !inviteDto.invitedUserUsername) {
        throw new HttpException(
          {
            success: false,
            error: 'Either email or username is required',
            message: 'Invalid invitation data'
          },
          HttpStatus.BAD_REQUEST
        );
      }

      const invitation: SyncInvitation = {
        matchId: inviteDto.matchId,
        invitedBy: req.user.id,
        role: inviteDto.role,
        invitedUserEmail: inviteDto.invitedUserEmail,
        invitedUserUsername: inviteDto.invitedUserUsername,
      };

      await this.matchSyncService.inviteUserToMatch(invitation);

      return {
        success: true,
        message: 'User invited successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to invite user'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * POST /broadcast/accept-invitation
   * Accept sync invitation
   */
  @Post('accept-invitation')
  async acceptInvitation(@Body() acceptDto: AcceptInvitationDto, @Request() req: any) {
    try {
      await this.matchSyncService.acceptInvitation(acceptDto.matchId, req.user.id);

      return {
        success: true,
        message: 'Invitation accepted successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to accept invitation'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * GET /broadcast/notifications
   * Get user's pending notifications
   */
  @Get('notifications')
  async getUserNotifications(@Request() req: any) {
    try {
      // Get pending invitations for this user
      const notifications = await this.matchSyncService.getUserNotifications(req.user.id);

      return {
        success: true,
        data: notifications,
        message: 'Notifications retrieved successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: 'Failed to get notifications',
          message: error.message
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * GET /broadcast/match/:matchId/participants
   * Get match participants
   */
  @Get('match/:matchId/participants')
  async getMatchParticipants(@Param('matchId') matchId: string, @Request() req: any) {
    try {
      // Verify user has access to this match
      const isParticipant = await this.matchSyncService.isUserParticipant(matchId, req.user.id);
      console.log(`🔍 User ${req.user.id} access check for match ${matchId}: ${isParticipant}`);

      if (!isParticipant) {
        console.log(`❌ User ${req.user.id} denied access to match ${matchId} participants`);
        throw new HttpException(
          {
            success: false,
            error: 'Unauthorized',
            message: 'You are not a participant in this match'
          },
          HttpStatus.FORBIDDEN
        );
      }

      console.log(`✅ User ${req.user.id} has access to match ${matchId} participants`);
      const participants = await this.matchSyncService.getMatchParticipants(matchId);

      console.log(`🔍 Returning ${participants.length} participants for match ${matchId}:`);
      participants.forEach(p => {
        console.log(`  - ${p.user.name} (${p.user.email}) - Role: ${p.role}, Status: ${p.status}`);
      });

      return {
        success: true,
        data: participants,
        message: 'Participants retrieved successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to get match participants'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * GET /broadcast/match/:matchId/actions
   * Get match actions from Redis Stream
   */
  @Get('match/:matchId/actions')
  async getMatchActions(
    @Param('matchId') matchId: string,
    @Request() req: any
  ) {
    try {
      // Verify user has access to this match
      const isParticipant = await this.matchSyncService.isUserParticipant(matchId, req.user.id);
      if (!isParticipant) {
        throw new HttpException(
          {
            success: false,
            error: 'Unauthorized',
            message: 'You are not a participant in this match'
          },
          HttpStatus.FORBIDDEN
        );
      }

      const actions = await this.broadcastService.getMatchActions(matchId);

      return {
        success: true,
        data: actions,
        message: 'Match actions retrieved successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to get match actions'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }



  /**
   * GET /broadcast/match/:matchId/my-role
   * Get user's role in match
   */
  @Get('match/:matchId/my-role')
  async getMyRole(@Param('matchId') matchId: string, @Request() req: any) {
    try {
      const role = await this.matchSyncService.getUserRole(matchId, req.user.id);

      return {
        success: true,
        data: { role },
        message: 'Role retrieved successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to get user role'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * POST /broadcast/match-start
   * Broadcast match start to all synced participants
   */
  @UseGuards(FirebaseAuthGuard)
  @Post('match-start')
  async broadcastMatchStart(
    @Body() body: { matchId: string; kickoffTeam: string; period: number; matchData?: any },
    @Request() req: any
  ) {
    try {
      const refereeId = req.user.id;

      const startData = {
        refereeId,
        kickoffTeam: body.kickoffTeam,
        period: body.period,
        matchData: body.matchData
      };

      await this.matchSyncService.broadcastMatchStart(body.matchId, startData);

      return {
        success: true,
        message: 'Match start broadcasted successfully'
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to broadcast match start'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * POST /broadcast/action
   * Broadcast match action to synced participants
   */
  @Post('action')
  async broadcastAction(
    @Body() body: {
      matchId: string;
      actionType: string;
      actionData: any;
      timestamp: string;
    },
    @Request() req: any
  ) {
    try {
      const refereeId = req.user.id;

      const actionData = {
        refereeId,
        actionType: body.actionType,
        actionData: body.actionData,
        timestamp: body.timestamp
      };

      const result = await this.matchSyncService.broadcastAction(body.matchId, actionData);

      return {
        success: true,
        message: 'Action broadcasted successfully',
        data: result
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to broadcast action'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * POST /broadcast/action-response
   * Respond to a broadcasted action (accept/decline)
   */
  @Post('action-response')
  async respondToAction(
    @Body() body: {
      actionId: string;
      response: 'accept' | 'decline';
      matchId: string;
    },
    @Request() req: any
  ) {
    try {
      const userId = req.user.id;

      const result = await this.matchSyncService.respondToAction({
        actionId: body.actionId,
        userId: userId,
        response: body.response,
        matchId: body.matchId
      });

      return {
        success: true,
        message: `Action ${body.response}ed successfully`,
        data: result
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          error: error.message,
          message: 'Failed to respond to action'
        },
        HttpStatus.BAD_REQUEST
      );
    }
  }
}
