import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Dimensions,
  ActivityIndicator,
  Image,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import Header from '../components/Header';
import { useTheme } from '../contexts/ThemeContext';
import { useUser } from '../contexts/UserContext';
import MatchService from '../api/matchService';
import Svg, { Defs, ClipPath, Path, G, Rect } from 'react-native-svg';

const MONTHS = [
  'JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC',
];

const WEEK_DAYS = ['SAT', 'SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI'];

const generateCalendarGrid = (year: number, month: number) => {
  const firstDayOfMonth = new Date(year, month, 1).getDay();
  const daysInMonth = new Date(year, month + 1, 0).getDate();
  let dayCounter = 1;
  const grid = [];
  const startingDayIndex = (firstDayOfMonth + 1) % 7;

  for (let i = 0; i < 6; i++) {
    const week = [];
    for (let j = 0; j < 7; j++) {
      if ((i === 0 && j < startingDayIndex) || dayCounter > daysInMonth) {
        week.push(null);
      } else {
        week.push(dayCounter++);
      }
    }
    grid.push(week);
    if (dayCounter > daysInMonth) break;
  }
  return grid;
};

// --- INTERFACES AND TYPES ---
type Team = {
  name: string;
  kitColor: string;
  kitStripe?: string;
};

type Match = {
  id?: string;
  division: string;
  role: string;
  homeTeam: Team;
  awayTeam: Team;
  location: string;
  date: string;
  time: string;
  homeScore?: number;
  awayScore?: number;
  status?: string;
};

interface StripedTshirtProps {
  baseColor: string;
  stripeColor?: string;
  size?: number;
}

// --- SVG T-SHIRT COMPONENT ---
const StripedTshirt: React.FC<StripedTshirtProps> = ({ baseColor, stripeColor = '#d60a2e', size = 36 }) => (
  <Svg width={size} height={size} viewBox="-1 0 19 19">
    <Defs><ClipPath id="tshirtClip"><Path d="m15.867 7.593-1.534.967a.544.544 0 0 1-.698-.118l-.762-.957v7.256a.476.476 0 0 1-.475.475h-7.79a.476.476 0 0 1-.475-.475V7.477l-.769.965a.544.544 0 0 1-.697.118l-1.535-.967a.387.387 0 0 1-.083-.607l2.245-2.492a2.814 2.814 0 0 1 2.092-.932h.935a2.374 2.374 0 0 0 4.364 0h.934a2.816 2.816 0 0 1 2.093.933l2.24 2.49a.388.388 0 0 1-.085.608z" /></ClipPath></Defs>
    <Path d="m15.867 7.593-1.534.967a.544.544 0 0 1-.698-.118l-.762-.957v7.256a.476.476 0 0 1-.475.475h-7.79a.476.476 0 0 1-.475-.475V7.477l-.769.965a.544.544 0 0 1-.697.118l-1.535-.967a.387.387 0 0 1-.083-.607l2.245-2.492a2.814 2.814 0 0 1 2.092-.932h.935a2.374 2.374 0 0 0 4.364 0h.934a2.816 2.816 0 0 1 2.093.933l2.24 2.49a.388.388 0 0 1-.085.608z" fill={baseColor} />
    <G clipPath="url(#tshirtClip)">
      {[3.5, 6, 8.5, 11, 13.5].map(x => (
        <Rect key={x} x={x} y="3" width="1.2" height="12" fill={stripeColor} />
      ))}
    </G>
  </Svg>
);

const CalendarScreen = () => {
  const navigation = useNavigation();
  const { theme, isDarkMode } = useTheme();
  const { userProfile, loading: userLoading } = useUser();
  const styles = getStyles(theme, isDarkMode);

  const [currentDate, setCurrentDate] = useState(new Date());
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [selectedMatches, setSelectedMatches] = useState<Match[]>([]);
  const [isLoadingMatches, setIsLoadingMatches] = useState(false);
  const [allMatches, setAllMatches] = useState<Match[]>([]);

  const monthScrollViewRef = useRef<ScrollView>(null);
  const [monthLayouts, setMonthLayouts] = useState<Record<number, { x: number; width: number }>>({});
  const selectedMonthIndex = currentDate.getMonth();
  const calendarGrid = generateCalendarGrid(currentDate.getFullYear(), selectedMonthIndex);

  // Load all matches on component mount
  useEffect(() => {
    fetchAllMatches();
  }, []);

  // Update month scroll position
  useEffect(() => {
    if (monthLayouts[selectedMonthIndex]) {
      const { x, width } = monthLayouts[selectedMonthIndex];
      const screenWidth = Dimensions.get('window').width;
      const scrollToX = x - screenWidth / 2 + width / 2;
      monthScrollViewRef.current?.scrollTo({ x: scrollToX, animated: true });
    }
  }, [selectedMonthIndex, monthLayouts]);

  // Filter matches when date selection changes
  useEffect(() => {
    console.log('Date selection changed:', {
      startDate: startDate?.toDateString(),
      endDate: endDate?.toDateString(),
      allMatchesCount: allMatches.length
    });

    if (startDate && endDate) {
      filterMatchesForDateRange(startDate, endDate);
    } else if (startDate && !endDate) {
      filterMatchesForDateRange(startDate, startDate);
    } else {
      setSelectedMatches([]);
    }
  }, [startDate, endDate, allMatches]);

  const handleMonthPress = (monthIndex: number) => {
    setCurrentDate(new Date(currentDate.getFullYear(), monthIndex, 1));
  };

  // Fetch all matches from API
  const fetchAllMatches = async () => {
    try {
      setIsLoadingMatches(true);

      // Fetch both upcoming and previous matches
      const [upcomingResponse, previousResponse] = await Promise.all([
        MatchService.getUpcomingMatches(),
        MatchService.getPreviousMatches()
      ]);

      const allMatchesData: Match[] = [];

      if (upcomingResponse.success && upcomingResponse.data) {
        const formattedUpcoming = MatchService.convertApiResponseToDashboardFormat(upcomingResponse.data);
        allMatchesData.push(...formattedUpcoming);
      }

      if (previousResponse.success && previousResponse.data) {
        const formattedPrevious = MatchService.convertApiResponseToDashboardFormat(previousResponse.data);
        allMatchesData.push(...formattedPrevious);
      }

      setAllMatches(allMatchesData);
      console.log('Loaded matches:', {
        total: allMatchesData.length,
        matches: allMatchesData.map(m => ({ date: m.date, homeTeam: m.homeTeam.name, awayTeam: m.awayTeam.name }))
      });
    } catch (error) {
      console.error('Error fetching matches:', error);
      // Use fallback data if API fails
      setAllMatches([]);
    } finally {
      setIsLoadingMatches(false);
    }
  };

  // Filter matches for selected date range
  const filterMatchesForDateRange = (start: Date, end: Date) => {
    // Normalize start and end dates to avoid time component issues
    const normalizedStart = new Date(start.getFullYear(), start.getMonth(), start.getDate());
    const normalizedEnd = new Date(end.getFullYear(), end.getMonth(), end.getDate());

    console.log('Filtering matches for date range:', {
      start: normalizedStart.toDateString(),
      end: normalizedEnd.toDateString(),
      totalMatches: allMatches.length
    });

    const filtered = allMatches.filter(match => {
      const matchDate = parseMatchDate(match.date);
      const normalizedMatchDate = new Date(matchDate.getFullYear(), matchDate.getMonth(), matchDate.getDate());

      console.log('Checking match:', {
        matchDateStr: match.date,
        parsedDate: matchDate.toDateString(),
        normalizedDate: normalizedMatchDate.toDateString(),
        inRange: normalizedMatchDate >= normalizedStart && normalizedMatchDate <= normalizedEnd
      });

      return normalizedMatchDate >= normalizedStart && normalizedMatchDate <= normalizedEnd;
    });

    console.log('Filtered matches:', filtered.length);
    setSelectedMatches(filtered);
  };

  // Parse match date string to Date object
  const parseMatchDate = (dateStr: string): Date => {
    try {
      console.log('Parsing date string:', dateStr);

      // Handle both formats: "DD/MM/YY" and "DD.MM.YY"
      let dateParts;
      if (dateStr.includes('/')) {
        dateParts = dateStr.split('/');
      } else if (dateStr.includes('.')) {
        dateParts = dateStr.split('.');
      } else {
        console.warn('Unknown date format:', dateStr);
        return new Date();
      }

      // Using DD/MM/YY format (European format)
      const [day, month, year] = dateParts;
      const fullYear = parseInt(year, 10) < 100 ? 2000 + parseInt(year, 10) : parseInt(year, 10);

      console.log('Date parts (DD/MM/YY):', { day, month, year, fullYear });

      // Create date with normalized time (midnight)
      const parsedDate = new Date(fullYear, parseInt(month, 10) - 1, parseInt(day, 10));
      parsedDate.setHours(0, 0, 0, 0);

      console.log('Parsed date:', parsedDate.toDateString(), 'Month index:', parsedDate.getMonth());

      return parsedDate;
    } catch (error) {
      console.error('Error parsing date:', dateStr, error);
      return new Date();
    }
  };

  // Test the date parsing for debugging
  useEffect(() => {
    // Test parsing your specific date format
    const testDate = '11/09/25';
    const parsed = parseMatchDate(testDate);
    console.log('Test parsing 11/09/25 (DD/MM/YY):', {
      original: testDate,
      parsed: parsed.toDateString(),
      month: parsed.getMonth(), // Should be 8 for September
      day: parsed.getDate(), // Should be 11
      year: parsed.getFullYear() // Should be 2025
    });
  }, []);

  const handleDayPress = (day: number) => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const clickedDate = new Date(year, month, day);
    clickedDate.setHours(0, 0, 0, 0);

    console.log('Day pressed:', {
      day,
      clickedDate: clickedDate.toDateString(),
      currentStartDate: startDate?.toDateString(),
      currentEndDate: endDate?.toDateString()
    });

    if (!startDate || (startDate && endDate)) {
      // First selection or reset selection
      setStartDate(clickedDate);
      setEndDate(null);
      console.log('Set start date:', clickedDate.toDateString());
    } else if (startDate && !endDate) {
      // Second selection - set end date
      if (clickedDate.getTime() < startDate.getTime()) {
        // If clicked date is before start date, swap them
        setEndDate(startDate);
        setStartDate(clickedDate);
        console.log('Swapped dates - start:', clickedDate.toDateString(), 'end:', startDate.toDateString());
      } else {
        // Normal case - clicked date is after start date
        setEndDate(clickedDate);
        console.log('Set end date:', clickedDate.toDateString());
      }
    }
  };

  // Render match card component
  const renderMatchCard = (match: Match, index: number) => (
    <View key={index} style={styles.matchCard}>
      <View style={styles.matchHeader}>
        <View style={styles.matchDivision}>
          <View style={[styles.divisionDot, { backgroundColor: theme.primary }]} />
          <Text style={styles.divisionText} numberOfLines={1}>{match.division}</Text>
        </View>
        <View style={styles.roleBadge}>
          <Text style={styles.roleText}>{match.role}</Text>
        </View>
      </View>

      <TouchableOpacity onPress={() => {
        if (match.status === 'COMPLETED') {
          (navigation as any).navigate('MatchResults', { matchId: match.id });
        } else {
          (navigation as any).navigate('MatchDetail', { matchId: match.id });
        }
      }}>
        <View style={styles.teamsCard}>
          {/* Home Team Column */}
          <View style={styles.teamColumn}>
            <StripedTshirt
              baseColor={match.homeTeam.kitColor}
              stripeColor={match.homeTeam.kitStripe}
            />
            <Text style={styles.teamName}>{match.homeTeam.name}</Text>
          </View>

          {/* Center Column for Score/Separator */}
          {typeof match.homeScore === 'number' && typeof match.awayScore === 'number' ? (
            <View style={styles.scoreContainer}>
              <Text style={styles.scoreText}>{`${match.homeScore} - ${match.awayScore}`}</Text>
            </View>
          ) : (
            <View style={styles.teamSeparatorContainer}>
              <View style={styles.teamSeparator} />
            </View>
          )}

          {/* Away Team Column */}
          <View style={styles.teamColumn}>
            <StripedTshirt
              baseColor={match.awayTeam.kitColor}
              stripeColor={match.awayTeam.kitStripe}
            />
            <Text style={styles.teamName}>{match.awayTeam.name}</Text>
          </View>
        </View>
      </TouchableOpacity>

      <View style={styles.matchDetails}>
        <View style={styles.locationSection}>
          <MaterialIcons name="location-on" size={16} color={theme.text} style={{ opacity: 0.7 }} />
          <Text style={styles.locationText}>{match.location}</Text>
        </View>
        <View style={styles.timeSection}>
          <Text style={styles.dateText}>{match.date}</Text>
          <View style={styles.timeSeparator} />
          <Text style={styles.timeText}>{match.time}</Text>
        </View>
      </View>
    </View>
  );

  // Get formatted date range text
  const getDateRangeText = () => {
    if (!startDate) return 'Select dates to view matches';
    if (!endDate) return `Matches for ${startDate.toLocaleDateString('en-GB')}`;

    if (startDate.getTime() === endDate.getTime()) {
      return `Matches for ${startDate.toLocaleDateString('en-GB')}`;
    }

    return `Matches from ${startDate.toLocaleDateString('en-GB')} to ${endDate.toLocaleDateString('en-GB')}`;
  };


  return (
    <View style={styles.container}>
      <Header title="Calendar" />
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
        <View style={styles.profileCard}>
          <View style={styles.profilePic}>
            {userProfile?.photoURL ? (
              <Image
                source={{ uri: userProfile.photoURL }}
                style={styles.profileImage}
              />
            ) : (
              <MaterialIcons name="person" size={24} color={theme.text} style={{ opacity: 0.5 }} />
            )}
          </View>
          <View style={styles.profileInfo}>
            {userLoading ? (
              <ActivityIndicator size="small" color={theme.primary} />
            ) : (
              <>
                <Text style={styles.profileName}>
                  {userProfile?.displayName || userProfile?.name || 'Loading...'}
                </Text>
                <Text style={styles.profileEmail}>
                  {userProfile?.email || ''}
                </Text>
              </>
            )}
          </View>
        </View>

        <ScrollView
          ref={monthScrollViewRef}
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.monthScroll}
        >
          {MONTHS.map((month, index) => (
            <TouchableOpacity
              key={month}
              onLayout={(event) => {
                const { x, width } = event.nativeEvent.layout;
                setMonthLayouts((prev) => ({ ...prev, [index]: { x, width } }));
              }}
              style={[
                styles.monthBtn,
                selectedMonthIndex === index && styles.monthBtnActive,
              ]}
              onPress={() => handleMonthPress(index)}
            >
              <Text style={[styles.monthText, selectedMonthIndex === index && styles.monthTextActive]}>
                {month}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        <View style={styles.calendarGrid}>
          <View style={styles.weekRow}>
            {WEEK_DAYS.map((day) => (
              <View key={day} style={styles.weekDayContainer}>
                <Text style={styles.weekDayText}>{day}</Text>
              </View>
            ))}
          </View>
          {calendarGrid.map((week, weekIndex) => (
            <View key={weekIndex} style={styles.daysRow}>
              {week.map((day, dayIndex) => {
                if (day === null) {
                  return (
                    <View key={dayIndex} style={[styles.dayBtn, styles.dayBtnDisabled]} />
                  );
                }

                const dayDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
                dayDate.setHours(0, 0, 0, 0);

                const isSelected = startDate && endDate && dayDate >= startDate && dayDate <= endDate;
                const isStartDateOnly = startDate && !endDate && dayDate.getTime() === startDate.getTime();
                const isActive = isSelected || isStartDateOnly;

                return (
                  <TouchableOpacity
                    key={dayIndex}
                    style={[
                      styles.dayBtn,
                      isActive && styles.dayBtnActive,
                    ]}
                    disabled={!day}
                    onPress={() => day && handleDayPress(day)}
                  >
                    <Text
                      style={[
                        styles.dayText,
                        isActive && styles.dayTextActive,
                      ]}
                    >
                      {day}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          ))}
        </View>

        {/* Date Range Selection Info */}
        <View style={styles.dateRangeInfo}>
          <Text style={styles.dateRangeText}>{getDateRangeText()}</Text>
          {(startDate || endDate) && (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={() => {
                setStartDate(null);
                setEndDate(null);
                setSelectedMatches([]);
              }}
            >
              <MaterialIcons name="clear" size={20} color={theme.primary} />
            </TouchableOpacity>
          )}
        </View>

        {/* Matches Display */}
        {isLoadingMatches ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.primary} />
            <Text style={styles.loadingText}>Loading matches...</Text>
          </View>
        ) : selectedMatches.length > 0 ? (
          <View style={styles.matchesList}>
            {selectedMatches.map(renderMatchCard)}
          </View>
        ) : (startDate || endDate) ? (
          <View style={styles.emptyContainer}>
            <MaterialIcons name="event-busy" size={48} color={theme.text} style={{ opacity: 0.3 }} />
            <Text style={styles.emptyText}>No matches found</Text>
            <Text style={styles.emptySubText}>No matches scheduled for the selected date(s)</Text>
          </View>
        ) : null}
      </ScrollView>
    </View>
  );
};

// --- DYNAMIC STYLES ---
const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  container: { flex: 1, backgroundColor: theme.background },
  scrollContent: { paddingHorizontal: 16, paddingTop: 12 },
  profileCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.card,
    borderRadius: 10,
    padding: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: theme.border,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.1,
    shadowRadius: 2,
    elevation: isDarkMode ? 0 : 3,
  },
  profilePic: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: theme.border,
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  profileImage: {
    width: 44,
    height: 44,
    borderRadius: 22,
  },
  profileInfo: {
    marginLeft: 16,
    flex: 1,
  },
  profileName: {
    fontSize: 18,
    fontWeight: '700',
    color: theme.text,
  },
  profileEmail: {
    fontSize: 14,
    color: theme.text,
    opacity: 0.7,
    marginTop: 2,
  },
  monthScroll: { marginBottom: 16 },
  monthBtn: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: theme.card,
    borderRadius: 18,
    marginRight: 8,
    borderWidth: 1,
    borderColor: theme.border,
  },
  monthBtnActive: {
    backgroundColor: theme.primary,
    borderColor: theme.primary,
  },
  monthText: {
    fontSize: 14,
    color: theme.text,
    fontWeight: '600'
  },
  monthTextActive: { color: '#fff' },
  calendarGrid: {
    backgroundColor: theme.card,
    borderRadius: 12,
    padding: 10,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: theme.border,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.1,
    shadowRadius: 2,
    elevation: isDarkMode ? 0 : 3,
  },
  weekRow: {
    flexDirection: 'row',
    marginBottom: 8
  },
  weekDayContainer: {
    flex: 1,
    alignItems: 'center',
  },
  weekDayText: {
    color: theme.text,
    opacity: 0.6,
    fontWeight: '600',
    fontSize: 11,
  },
  daysRow: { flexDirection: 'row', justifyContent: 'space-around', marginBottom: 6 },
  dayBtn: {
    flex: 1,
    margin: 3,
    aspectRatio: 1,
    backgroundColor: 'transparent',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.border,
    alignItems: 'center',
    justifyContent: 'center',
  },
  dayBtnActive: {
    backgroundColor: isDarkMode ? `${theme.primary}3990` : '#e9f4e8',
    borderColor: theme.primary
  },
  dayBtnDisabled: { backgroundColor: 'transparent', borderWidth: 0 },
  dayText: {
    color: theme.text,
    fontWeight: 'bold',
    fontSize: 14
  },
  dayTextActive: { color: theme.primary, fontWeight: 'bold' },
  // Date range info section
  dateRangeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.card,
    borderRadius: 10,
    padding: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: theme.border,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.1,
    shadowRadius: 2,
    elevation: isDarkMode ? 0 : 3,
  },
  dateRangeText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.text,
    flex: 1,
  },
  clearButton: {
    padding: 4,
    marginLeft: 8,
  },

  // Match cards section
  matchesList: {
    marginTop: 8
  },
  matchCard: {
    backgroundColor: theme.card,
    borderRadius: 10,
    padding: 12,
    marginBottom: 12,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: isDarkMode ? 0 : 2,
  },
  matchHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
    gap: 8,
  },
  matchDivision: {
    flexDirection: 'row',
    alignItems: 'center',
    flexShrink: 1,
    overflow: 'hidden',
  },
  divisionDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  divisionText: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.text,
    opacity: 0.7,
  },
  roleBadge: {
    backgroundColor: theme.background,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  roleText: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.text,
  },
  teamsCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 4,
    backgroundColor: `${theme.text}08`,
    borderRadius: 8,
    marginBottom: 8,
  },
  teamColumn: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  teamName: {
    fontSize: 12,
    fontWeight: '500',
    color: theme.text,
    textAlign: 'center',
    lineHeight: 16,
    marginTop: 6,
  },
  teamSeparatorContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    height: 36,
    marginHorizontal: 4,
  },
  teamSeparator: {
    width: 1,
    height: 24,
    backgroundColor: theme.text,
  },
  scoreContainer: {
    backgroundColor: theme.border,
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 16,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    marginHorizontal: 4,
  },
  scoreText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.text,
  },
  matchDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    backgroundColor: 'transparent',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.border,
    gap: 16,
  },
  locationSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  locationText: {
    marginLeft: 8,
    fontSize: 13,
    fontWeight: '500',
    color: theme.text,
    opacity: 0.8,
    flexShrink: 1,
  },
  timeSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateText: {
    fontSize: 13,
    fontWeight: '600',
    color: theme.text,
  },
  timeSeparator: {
    width: 1,
    height: 16,
    backgroundColor: theme.border,
    marginHorizontal: 12,
  },
  timeText: {
    fontSize: 13,
    fontWeight: '600',
    color: theme.text,
  },

  // Loading and empty states
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.text,
    opacity: 0.7,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 32,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: '600',
    color: theme.text,
    textAlign: 'center',
  },
  emptySubText: {
    marginTop: 8,
    fontSize: 14,
    color: theme.text,
    opacity: 0.6,
    textAlign: 'center',
  },
});

export default CalendarScreen;