import { IsString, <PERSON>NotEmpty, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';

export class CreateMisconductSetDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsArray()
  playersYellowCodes: Array<{
    id: string;
    code: string;
    description: string;
  }>;

  @IsArray()
  playersRedCodes: Array<{
    id: string;
    code: string;
    description: string;
  }>;

  @IsArray()
  officialsYellowCodes: Array<{
    id: string;
    code: string;
    description: string;
  }>;

  @IsArray()
  officialsRedCodes: Array<{
    id: string;
    code: string;
    description: string;
  }>;

  @IsString()
  @IsNotEmpty()
  userId: string;
}