import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet, Modal, TextInput, Alert } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Header from '../components/Header';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { useUser } from '../contexts/UserContext';
import { apiClient } from '../api/client';

type MisconductCode = {
  id: string;
  code: string;
  description: string;
  subcategories?: SubCategory[];
};

type CardType = 'yellow' | 'red';

type CustomCode = {
  id: string;
  code: string;
  description: string;
  cardType: CardType;
  subcategories?: SubCategory[];
};

type SubCategory = {
  id: string;
  code: string;
  description: string;
};

type MisconductSet = {
  id: string;
  name: string;
  playersYellowCodes: CustomCode[];
  playersRedCodes: CustomCode[];
  officialsYellowCodes: CustomCode[];
  officialsRedCodes: CustomCode[];
};

const ManageMisconductCodeScreen = () => {
  const { isDarkMode, theme } = useTheme();
  const { user } = useAuth();
  const { userProfile, loading: userLoading } = useUser();
  const styles = getStyles(theme, isDarkMode);
  const navigation = useNavigation();

  const [selectedTab, setSelectedTab] = useState<'players' | 'officials'>('players');
  const [showModal, setShowModal] = useState(false);
  const [modalTab, setModalTab] = useState<'PLAYERS' | 'OFFICIALS'>('PLAYERS');
  const [showDropdown, setShowDropdown] = useState(false);

  // Custom codes state - separate for players and officials
  const [customYellowCodesPlayers, setCustomYellowCodesPlayers] = useState<CustomCode[]>([]);
  const [customRedCodesPlayers, setCustomRedCodesPlayers] = useState<CustomCode[]>([]);
  const [customYellowCodesOfficials, setCustomYellowCodesOfficials] = useState<CustomCode[]>([]);
  const [customRedCodesOfficials, setCustomRedCodesOfficials] = useState<CustomCode[]>([]);
  const [misconductName, setMisconductName] = useState('');

  // Saved misconduct sets
  const [misconductSets, setMisconductSets] = useState<MisconductSet[]>([]);
  const [selectedMisconductSet, setSelectedMisconductSet] = useState<MisconductSet | null>(null);

  // Edit mode states
  const [editingYellow, setEditingYellow] = useState(false);
  const [editingRed, setEditingRed] = useState(false);
  const [editedYellowCodes, setEditedYellowCodes] = useState<MisconductCode[]>([]);
  const [editedRedCodes, setEditedRedCodes] = useState<MisconductCode[]>([]);



  const addCustomCode = (cardType: CardType) => {
    const newCode: CustomCode = {
      id: Date.now().toString(),
      code: '',
      description: '',
      cardType,
      subcategories: []
    };

    if (modalTab === 'PLAYERS') {
      if (cardType === 'yellow') {
        setCustomYellowCodesPlayers([...customYellowCodesPlayers, newCode]);
      } else {
        setCustomRedCodesPlayers([...customRedCodesPlayers, newCode]);
      }
    } else {
      if (cardType === 'yellow') {
        setCustomYellowCodesOfficials([...customYellowCodesOfficials, newCode]);
      } else {
        setCustomRedCodesOfficials([...customRedCodesOfficials, newCode]);
      }
    }
  };

  const updateCustomCode = (id: string, field: 'code' | 'description', value: string) => {
    const updateCodes = (codes: CustomCode[]) =>
      codes.map(code => code.id === id ? { ...code, [field]: value } : code);

    if (modalTab === 'PLAYERS') {
      setCustomYellowCodesPlayers(prev => updateCodes(prev));
      setCustomRedCodesPlayers(prev => updateCodes(prev));
    } else {
      setCustomYellowCodesOfficials(prev => updateCodes(prev));
      setCustomRedCodesOfficials(prev => updateCodes(prev));
    }
  };

  const deleteCustomCode = (id: string, cardType: CardType) => {
    if (modalTab === 'PLAYERS') {
      if (cardType === 'yellow') {
        setCustomYellowCodesPlayers(prev => prev.filter(code => code.id !== id));
      } else {
        setCustomRedCodesPlayers(prev => prev.filter(code => code.id !== id));
      }
    } else {
      if (cardType === 'yellow') {
        setCustomYellowCodesOfficials(prev => prev.filter(code => code.id !== id));
      } else {
        setCustomRedCodesOfficials(prev => prev.filter(code => code.id !== id));
      }
    }
  };

  const addSubcategory = (codeId: string, cardType: CardType) => {
    const newSubcategory: SubCategory = {
      id: Date.now().toString(),
      code: '',
      description: ''
    };

    const updateCodeWithSubcategory = (codes: CustomCode[]) =>
      codes.map(code =>
        code.id === codeId
          ? { ...code, subcategories: [...(code.subcategories || []), newSubcategory] }
          : code
      );

    if (modalTab === 'PLAYERS') {
      if (cardType === 'yellow') {
        setCustomYellowCodesPlayers(prev => updateCodeWithSubcategory(prev));
      } else {
        setCustomRedCodesPlayers(prev => updateCodeWithSubcategory(prev));
      }
    } else {
      if (cardType === 'yellow') {
        setCustomYellowCodesOfficials(prev => updateCodeWithSubcategory(prev));
      } else {
        setCustomRedCodesOfficials(prev => updateCodeWithSubcategory(prev));
      }
    }
  };

  const updateSubcategory = (codeId: string, subcategoryId: string, field: 'code' | 'description', value: string, cardType: CardType) => {
    const updateCodeSubcategories = (codes: CustomCode[]) =>
      codes.map(code =>
        code.id === codeId
          ? {
            ...code,
            subcategories: (code.subcategories || []).map(sub =>
              sub.id === subcategoryId ? { ...sub, [field]: value } : sub
            )
          }
          : code
      );

    if (modalTab === 'PLAYERS') {
      if (cardType === 'yellow') {
        setCustomYellowCodesPlayers(prev => updateCodeSubcategories(prev));
      } else {
        setCustomRedCodesPlayers(prev => updateCodeSubcategories(prev));
      }
    } else {
      if (cardType === 'yellow') {
        setCustomYellowCodesOfficials(prev => updateCodeSubcategories(prev));
      } else {
        setCustomRedCodesOfficials(prev => updateCodeSubcategories(prev));
      }
    }
  };

  const deleteSubcategory = (codeId: string, subcategoryId: string, cardType: CardType) => {
    const updateCodeSubcategories = (codes: CustomCode[]) =>
      codes.map(code =>
        code.id === codeId
          ? {
            ...code,
            subcategories: (code.subcategories || []).filter(sub => sub.id !== subcategoryId)
          }
          : code
      );

    if (modalTab === 'PLAYERS') {
      if (cardType === 'yellow') {
        setCustomYellowCodesPlayers(prev => updateCodeSubcategories(prev));
      } else {
        setCustomRedCodesPlayers(prev => updateCodeSubcategories(prev));
      }
    } else {
      if (cardType === 'yellow') {
        setCustomYellowCodesOfficials(prev => updateCodeSubcategories(prev));
      } else {
        setCustomRedCodesOfficials(prev => updateCodeSubcategories(prev));
      }
    }
  };

  // Load misconduct sets when user profile is available
  useEffect(() => {
    if (userProfile?.id && !userLoading) {
      loadMisconductSets();
      loadSelectedMisconductSet();
    }
  }, [userProfile?.id, userLoading]);

  // Create hardcoded Wales misconduct set
  const createWalesMisconductSet = (): MisconductSet => {
    return {
      id: 'wales-default',
      name: 'Wales',
      playersYellowCodes: [
        {
          id: 'c1',
          code: 'C1',
          description: 'Unsporting Behaviour',
          cardType: 'yellow',
          subcategories: [
            { id: 'c1-aa', code: 'AA', description: 'Adopting an aggressive attitude' },
            { id: 'c1-di', code: 'DI', description: 'Simulation' },
            { id: 'c1-dp', code: 'DP', description: 'Dangerous Play' },
            { id: 'c1-ft', code: 'FT', description: 'Foul Tackle' },
            { id: 'c1-gc', code: 'GC', description: 'Goal Celebration' },
            { id: 'c1-hb', code: 'HB', description: 'Handball' },
            { id: 'c1-rp', code: 'RP', description: 'Reckless Play' },
            { id: 'c1-sp', code: 'SP', description: 'Pushing or Pulling an opponent' },
            { id: 'c1-tr', code: 'TR', description: 'Tripping' },
            { id: 'c1-ub', code: 'UB', description: 'Unspecified Behaviour' }
          ]
        },
        {
          id: 'c2',
          code: 'C2',
          description: 'Shows dissent by word or action(T)',
          cardType: 'yellow',
          subcategories: []
        },
        {
          id: 'c3',
          code: 'C3',
          description: 'Persistently infringing the Laws of the Game',
          cardType: 'yellow',
          subcategories: []
        },
        {
          id: 'c4',
          code: 'C4',
          description: 'Delays restart',
          cardType: 'yellow',
          subcategories: []
        },
        {
          id: 'c5',
          code: 'C5',
          description: 'Fails to retreat required distance at a restart',
          cardType: 'yellow',
          subcategories: []
        },
        {
          id: 'c6',
          code: 'C6',
          description: 'Enters the field of play without permission',
          cardType: 'yellow',
          subcategories: []
        },
        {
          id: 'c7',
          code: 'C7',
          description: 'Leaves the field of play without permission',
          cardType: 'yellow',
          subcategories: []
        }
      ],
      playersRedCodes: [
        {
          id: 's1',
          code: 'S1',
          description: 'Serious foul play',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 's2',
          code: 'S2',
          description: 'Violent conduct',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 's3',
          code: 'S3',
          description: 'Spitting at an opponent or any other person',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 's4',
          code: 'S4',
          description: 'Denying a goal-scoring opportunity to opponents by deliberate handball',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 's5',
          code: 'S5',
          description: 'Denying a goal-scoring opportunity to opponents by an offense punishable by a free kick or penalty',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 's6',
          code: 'S6',
          description: 'Using offensive, insulting, or abusive language/gestures',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 's7',
          code: 'S7',
          description: 'Receiving a second caution',
          cardType: 'red',
          subcategories: []
        }
      ],
      officialsYellowCodes: [
        {
          id: 't1',
          code: 'T1',
          description: 'Yellow',
          cardType: 'yellow',
          subcategories: []
        }
      ],
      officialsRedCodes: [
        {
          id: 't2',
          code: 'T2',
          description: 'Red',
          cardType: 'red',
          subcategories: []
        }
      ]
    };
  };

  // Create hardcoded England with FA Codes misconduct set
  const createEnglandFAMisconductSet = (): MisconductSet => {
    return {
      id: 'england-fa-default',
      name: 'England with FA Codes',
      playersYellowCodes: [
        {
          id: 'c1-eng',
          code: 'C1',
          description: 'Unsporting Behaviour',
          cardType: 'yellow',
          subcategories: [
            { id: 'c1-aa-eng', code: 'AA', description: 'Adopting an aggressive attitude' },
            { id: 'c1-di-eng', code: 'DI', description: 'Simulation' },
            { id: 'c1-dp-eng', code: 'DP', description: 'Dangerous Play' },
            { id: 'c1-ft-eng', code: 'FT', description: 'Foul Tackle' },
            { id: 'c1-gc-eng', code: 'GC', description: 'Goal Celebration' },
            { id: 'c1-hb-eng', code: 'HB', description: 'Handball' },
            { id: 'c1-rp-eng', code: 'RP', description: 'Reckless Play' },
            { id: 'c1-sp-eng', code: 'SP', description: 'Pushing or Pulling an opponent' },
            { id: 'c1-tr-eng', code: 'TR', description: 'Tripping' },
            { id: 'c1-ub-eng', code: 'UB', description: 'Unspecified Behaviour' }
          ]
        },
        {
          id: 'c2-eng',
          code: 'C2',
          description: 'Shows dissent by word or action(T)',
          cardType: 'yellow',
          subcategories: []
        },
        {
          id: 'c3-eng',
          code: 'C3',
          description: 'Persistent infringement',
          cardType: 'yellow',
          subcategories: []
        },
        {
          id: 'c4-eng',
          code: 'C4',
          description: 'Delays the restart of play',
          cardType: 'yellow',
          subcategories: []
        },
        {
          id: 'c5-eng',
          code: 'C5',
          description: 'Fails to retreat required distance',
          cardType: 'yellow',
          subcategories: []
        },
        {
          id: 'c6-eng',
          code: 'C6',
          description: 'Enters the FOP without permission',
          cardType: 'yellow',
          subcategories: []
        },
        {
          id: 'c7-eng',
          code: 'C7',
          description: 'Leaves the FOP without permission',
          cardType: 'yellow',
          subcategories: []
        }
      ],
      playersRedCodes: [
        {
          id: 's1-eng',
          code: 'S1',
          description: 'Serious foul play',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 's2-eng',
          code: 'S2',
          description: 'Violent conduct',
          cardType: 'red',
          subcategories: [
            { id: 's2a-eng', code: 'S2A', description: 'Head to head contact' },
            { id: 's2b-eng', code: 'S2B', description: 'Elbowing' },
            { id: 's2c-eng', code: 'S2C', description: 'Kicking' },
            { id: 's2d-eng', code: 'S2D', description: 'Stamping' },
            { id: 's2e-eng', code: 'S2E', description: 'Striking' },
            { id: 's2f-eng', code: 'S2F', description: 'Biting' },
            { id: 's2g-eng', code: 'S2G', description: 'Other' }
          ]
        },
        {
          id: 's3-eng',
          code: 'S3',
          description: 'Spitting',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 's4-eng',
          code: 'S4',
          description: 'Denies goal by hand',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 's5-eng',
          code: 'S5',
          description: 'DOGSO',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 's6-eng',
          code: 'S6',
          description: 'OFFINABUSS',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 's7-eng',
          code: 'S7',
          description: 'Second Caution',
          cardType: 'red',
          subcategories: []
        }
      ],
      officialsYellowCodes: [
        {
          id: 't1-eng',
          code: 'T1',
          description: 'Clearly/persistently not respecting the confines of their team\'s technical area',
          cardType: 'yellow',
          subcategories: []
        },
        {
          id: 't2-eng',
          code: 'T2',
          description: 'Delaying the restart of play by their team',
          cardType: 'yellow',
          subcategories: []
        },
        {
          id: 't3-eng',
          code: 'T3',
          description: 'Deliberately entering the technical area of the opposing team (non-confrontational)',
          cardType: 'yellow',
          subcategories: []
        },
        {
          id: 't4-eng',
          code: 'T4',
          description: 'Dissent by word or action',
          cardType: 'yellow',
          subcategories: []
        },
        {
          id: 't5-eng',
          code: 'T5',
          description: 'Entering the referee review area (RRA)',
          cardType: 'yellow',
          subcategories: []
        },
        {
          id: 't6-eng',
          code: 'T6',
          description: 'Excessively showing the TV signal for a VAR \'review\'',
          cardType: 'yellow',
          subcategories: []
        },
        {
          id: 't7-eng',
          code: 'T7',
          description: 'Excessively/persistently gesturing for a red or yellow card',
          cardType: 'yellow',
          subcategories: []
        },
        {
          id: 't8-eng',
          code: 'T8',
          description: 'Gestures which show a clear lack of respect for the match officials(s) e.g. sarcastic clapping',
          cardType: 'yellow',
          subcategories: []
        },
        {
          id: 't9-eng',
          code: 'T9',
          description: 'Gesturing or acting in a provocative or inflammatory manner',
          cardType: 'yellow',
          subcategories: []
        },
        {
          id: 't10-eng',
          code: 'T10',
          description: 'Persistent unacceptable behaviour (including repeated warning offences)',
          cardType: 'yellow',
          subcategories: []
        },
        {
          id: 't11-eng',
          code: 'T11',
          description: 'Showing a lack of respect for the game',
          cardType: 'yellow',
          subcategories: []
        },
        {
          id: 't12-eng',
          code: 'T12',
          description: 'Throwing/kicking drinks bottles or other objects',
          cardType: 'yellow',
          subcategories: []
        },
        {
          id: 't13-eng',
          code: 'T13',
          description: 'Other',
          cardType: 'yellow',
          subcategories: []
        }
      ],
      officialsRedCodes: [
        {
          id: 't14-eng',
          code: 'T14',
          description: 'Delaying the restart of play by the opposing team',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 't15-eng',
          code: 'T15',
          description: 'Deliberately leaving the technical area to show dissent or remonstrate with a Match Official',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 't16-eng',
          code: 'T16',
          description: 'Deliberately leaving the technical to act in a provocative or inflammatory manner',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 't17-eng',
          code: 'T17',
          description: 'Deliberately leaving the technical to enter the opposing technical area in an aggressive or confrontational manner',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 't18-eng',
          code: 'T18',
          description: 'Deliberately throwing/kicking an object onto the field of play',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 't19-eng',
          code: 'T19',
          description: 'Entering the field of play to confront a match official (including half time and full-time)',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 't20-eng',
          code: 'T20',
          description: 'Entering the field of play to interfere with play, an opposing player or match official',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 't21-eng',
          code: 'T21',
          description: 'Entering the video operation room (VOR)',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 't22-eng',
          code: 'T22',
          description: 'Physical or aggressive behaviour (including biting and spitting)',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 't23-eng',
          code: 'T23',
          description: 'Receiving a second caution in the same match',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 't24-eng',
          code: 'T24',
          description: 'Using offensive, insulting or abusive language and/or gestures',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 't25-eng',
          code: 'T25',
          description: 'Using unauthorised electronic or communication equipment and/or behaving in an appropriate manner as the result of using such equipment',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 't26-eng',
          code: 'T26',
          description: 'Violent Conduct',
          cardType: 'red',
          subcategories: []
        },
        {
          id: 't27-eng',
          code: 'T27',
          description: 'Other',
          cardType: 'red',
          subcategories: []
        }
      ]
    };
  };

  // Save selected misconduct set to AsyncStorage
  const saveSelectedMisconductSet = async (set: MisconductSet | null) => {
    try {
      if (set) {
        await AsyncStorage.setItem('selectedMisconductSet', JSON.stringify(set));
      } else {
        await AsyncStorage.removeItem('selectedMisconductSet');
      }
    } catch (error) {
      console.error('Error saving selected misconduct set:', error);
    }
  };

  // Load selected misconduct set from AsyncStorage
  const loadSelectedMisconductSet = async () => {
    try {
      const savedSet = await AsyncStorage.getItem('selectedMisconductSet');
      if (savedSet) {
        const parsedSet = JSON.parse(savedSet);
        setSelectedMisconductSet(parsedSet);
      } else {
        // If no saved set, default to Wales
        const walesSet = createWalesMisconductSet();
        setSelectedMisconductSet(walesSet);
        await saveSelectedMisconductSet(walesSet);
      }
    } catch (error) {
      console.error('Error loading selected misconduct set:', error);
      // Fallback to Wales set
      const walesSet = createWalesMisconductSet();
      setSelectedMisconductSet(walesSet);
    }
  };

  const loadMisconductSets = async () => {
    try {
      console.log('UserProfile:', userProfile);
      console.log('UserProfile ID:', userProfile?.id);

      // Always include hardcoded sets
      const walesSet = createWalesMisconductSet();
      const englandSet = createEnglandFAMisconductSet();
      let allSets = [walesSet, englandSet];

      if (!userProfile?.id) {
        console.log('No user profile ID, setting hardcoded sets only');
        setMisconductSets(allSets);
        return;
      }

      console.log('Loading misconduct sets for user:', userProfile.id);
      const response = await apiClient.get(`/misconduct-sets/${userProfile.id}`);
      console.log('API response:', response);

      // Handle both ApiResponse format and direct data format
      let userData;
      if (response.success !== undefined) {
        // ApiResponse format
        if (response.success && response.data) {
          userData = response.data;
        } else {
          console.error('Failed to load misconduct sets:', response.error);
          setMisconductSets(allSets); // Still include hardcoded sets
          return;
        }
      } else {
        // Direct data format (response is the data itself)
        userData = response;
      }

      const userSets = Array.isArray(userData) ? userData : [];
      // Combine hardcoded sets with user's custom sets
      allSets = [walesSet, englandSet, ...userSets];
      setMisconductSets(allSets);
    } catch (error) {
      console.error('Error loading misconduct sets:', error);
      // Fallback to hardcoded sets only
      const walesSet = createWalesMisconductSet();
      const englandSet = createEnglandFAMisconductSet();
      setMisconductSets([walesSet, englandSet]);
    }
  };

  // Get codes to display based on selected misconduct set and current tab
  const getDisplayedYellowCodes = (): MisconductCode[] => {
    if (selectedMisconductSet) {
      const codes = selectedTab === 'players'
        ? selectedMisconductSet.playersYellowCodes || []
        : selectedMisconductSet.officialsYellowCodes || [];

      return codes.map(code => ({
        id: code.id,
        code: code.code,
        description: code.description,
        subcategories: code.subcategories || []
      }));
    }
    // Return empty array instead of FIFA codes
    return [];
  };

  const getDisplayedRedCodes = (): MisconductCode[] => {
    if (selectedMisconductSet) {
      const codes = selectedTab === 'players'
        ? selectedMisconductSet.playersRedCodes || []
        : selectedMisconductSet.officialsRedCodes || [];

      return codes.map(code => ({
        id: code.id,
        code: code.code,
        description: code.description,
        subcategories: code.subcategories || []
      }));
    }
    // Return empty array instead of FIFA codes
    return [];
  };

  // Edit functions
  const startEditingYellow = () => {
    if (selectedMisconductSet) {
      const codes = selectedTab === 'players'
        ? selectedMisconductSet.playersYellowCodes || []
        : selectedMisconductSet.officialsYellowCodes || [];

      setEditedYellowCodes(codes.map(code => ({
        id: code.id,
        code: code.code,
        description: code.description,
        subcategories: code.subcategories || []
      })));
      setEditingYellow(true);
    }
  };

  const startEditingRed = () => {
    if (selectedMisconductSet) {
      const codes = selectedTab === 'players'
        ? selectedMisconductSet.playersRedCodes || []
        : selectedMisconductSet.officialsRedCodes || [];

      setEditedRedCodes(codes.map(code => ({
        id: code.id,
        code: code.code,
        description: code.description,
        subcategories: code.subcategories || []
      })));
      setEditingRed(true);
    }
  };

  const cancelEditing = (cardType: CardType) => {
    if (cardType === 'yellow') {
      setEditingYellow(false);
      setEditedYellowCodes([]);
    } else {
      setEditingRed(false);
      setEditedRedCodes([]);
    }
  };

  const updateEditedCode = (id: string, field: 'code' | 'description', value: string, cardType: CardType) => {
    const updateCodes = (codes: MisconductCode[]) =>
      codes.map(code => code.id === id ? { ...code, [field]: value } : code);

    if (cardType === 'yellow') {
      setEditedYellowCodes(prev => updateCodes(prev));
    } else {
      setEditedRedCodes(prev => updateCodes(prev));
    }
  };

  const addEditedCode = (cardType: CardType) => {
    const newCode: MisconductCode = {
      id: Date.now().toString(),
      code: '',
      description: '',
      subcategories: []
    };

    if (cardType === 'yellow') {
      setEditedYellowCodes(prev => [...prev, newCode]);
    } else {
      setEditedRedCodes(prev => [...prev, newCode]);
    }
  };

  const addEditedSubcategory = (codeId: string, cardType: CardType) => {
    const newSubcategory: SubCategory = {
      id: Date.now().toString(),
      code: '',
      description: ''
    };

    const updateCodeWithSubcategory = (codes: MisconductCode[]) =>
      codes.map(code =>
        code.id === codeId
          ? { ...code, subcategories: [...(code.subcategories || []), newSubcategory] }
          : code
      );

    if (cardType === 'yellow') {
      setEditedYellowCodes(prev => updateCodeWithSubcategory(prev));
    } else {
      setEditedRedCodes(prev => updateCodeWithSubcategory(prev));
    }
  };

  const updateEditedSubcategory = (codeId: string, subcategoryId: string, field: 'code' | 'description', value: string, cardType: CardType) => {
    const updateCodeSubcategories = (codes: MisconductCode[]) =>
      codes.map(code =>
        code.id === codeId
          ? {
            ...code,
            subcategories: (code.subcategories || []).map(sub =>
              sub.id === subcategoryId ? { ...sub, [field]: value } : sub
            )
          }
          : code
      );

    if (cardType === 'yellow') {
      setEditedYellowCodes(prev => updateCodeSubcategories(prev));
    } else {
      setEditedRedCodes(prev => updateCodeSubcategories(prev));
    }
  };

  const deleteEditedSubcategory = (codeId: string, subcategoryId: string, cardType: CardType) => {
    const updateCodeSubcategories = (codes: MisconductCode[]) =>
      codes.map(code =>
        code.id === codeId
          ? {
            ...code,
            subcategories: (code.subcategories || []).filter(sub => sub.id !== subcategoryId)
          }
          : code
      );

    if (cardType === 'yellow') {
      setEditedYellowCodes(prev => updateCodeSubcategories(prev));
    } else {
      setEditedRedCodes(prev => updateCodeSubcategories(prev));
    }
  };

  const deleteEditedCode = (id: string, cardType: CardType) => {
    if (cardType === 'yellow') {
      setEditedYellowCodes(prev => prev.filter(code => code.id !== id));
    } else {
      setEditedRedCodes(prev => prev.filter(code => code.id !== id));
    }
  };

  const saveEditedCodes = async (cardType: CardType) => {
    if (!selectedMisconductSet || !userProfile?.id) {
      Alert.alert('Error', 'No misconduct set selected or user not loaded');
      return;
    }

    // Prevent editing of hardcoded sets
    if (selectedMisconductSet.id === 'wales-default' || selectedMisconductSet.id === 'england-fa-default') {
      Alert.alert('Error', 'Hardcoded misconduct sets cannot be edited. Please create a custom set instead.');
      return;
    }

    try {
      // Create updated misconduct set
      const updatedSet = { ...selectedMisconductSet };

      if (cardType === 'yellow') {
        if (selectedTab === 'players') {
          updatedSet.playersYellowCodes = editedYellowCodes.map(code => ({
            id: code.id,
            code: code.code,
            description: code.description,
            cardType: 'yellow' as CardType,
            subcategories: code.subcategories || []
          }));
        } else {
          updatedSet.officialsYellowCodes = editedYellowCodes.map(code => ({
            id: code.id,
            code: code.code,
            description: code.description,
            cardType: 'yellow' as CardType,
            subcategories: code.subcategories || []
          }));
        }
      } else {
        if (selectedTab === 'players') {
          updatedSet.playersRedCodes = editedRedCodes.map(code => ({
            id: code.id,
            code: code.code,
            description: code.description,
            cardType: 'red' as CardType,
            subcategories: code.subcategories || []
          }));
        } else {
          updatedSet.officialsRedCodes = editedRedCodes.map(code => ({
            id: code.id,
            code: code.code,
            description: code.description,
            cardType: 'red' as CardType,
            subcategories: code.subcategories || []
          }));
        }
      }

      console.log('Updating misconduct set:', updatedSet);
      const response = await apiClient.put(`/misconduct-sets/${selectedMisconductSet.id}`, {
        name: updatedSet.name,
        playersYellowCodes: updatedSet.playersYellowCodes,
        playersRedCodes: updatedSet.playersRedCodes,
        officialsYellowCodes: updatedSet.officialsYellowCodes,
        officialsRedCodes: updatedSet.officialsRedCodes,
        userId: userProfile.id,
      });

      // Handle both ApiResponse format and direct data format
      let isSuccess = false;
      if (response.success !== undefined) {
        // ApiResponse format
        isSuccess = response.success;
        if (!isSuccess) {
          throw new Error(response.error || 'Failed to update misconduct set');
        }
      } else {
        // Direct data format (response is the data itself)
        isSuccess = true;
      }

      if (isSuccess) {
        // Update local state
        setSelectedMisconductSet(updatedSet);
        setMisconductSets(prev => prev.map(set =>
          set.id === updatedSet.id ? updatedSet : set
        ));

        // Exit edit mode
        cancelEditing(cardType);

        Alert.alert('Success', `${cardType === 'yellow' ? 'Yellow' : 'Red'} card codes updated successfully!`);
      }
    } catch (error) {
      console.error('Error updating misconduct codes:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      Alert.alert('Error', `Failed to update misconduct codes: ${errorMessage}`);
    }
  };

  const saveCustomCodes = async () => {
    if (!misconductName.trim()) {
      Alert.alert('Error', 'Please enter a misconduct name');
      return;
    }

    // Check if there are any codes for either players or officials
    const hasPlayersCodes = customYellowCodesPlayers.length > 0 || customRedCodesPlayers.length > 0;
    const hasOfficialsCodes = customYellowCodesOfficials.length > 0 || customRedCodesOfficials.length > 0;

    if (!hasPlayersCodes && !hasOfficialsCodes) {
      Alert.alert('Error', 'Please add at least one misconduct code for players or team officials');
      return;
    }

    try {
      console.log('Save - UserProfile:', userProfile);
      console.log('Save - UserProfile ID:', userProfile?.id);

      if (!userProfile?.id) {
        Alert.alert('Error', 'User profile not loaded');
        return;
      }

      const misconductSet = {
        name: misconductName,
        playersYellowCodes: customYellowCodesPlayers,
        playersRedCodes: customRedCodesPlayers,
        officialsYellowCodes: customYellowCodesOfficials,
        officialsRedCodes: customRedCodesOfficials,
        userId: userProfile.id,
      };

      console.log('Saving misconduct set:', misconductSet);
      const response = await apiClient.post('/misconduct-sets', misconductSet);
      console.log('Save response:', response);

      // Handle both ApiResponse format and direct data format
      let savedData;
      let isSuccess = false;

      if (response.success !== undefined) {
        // ApiResponse format
        isSuccess = response.success;
        savedData = response.data;
        if (!isSuccess) {
          throw new Error(response.error || 'Failed to save misconduct set');
        }
      } else {
        // Direct data format (response is the data itself)
        isSuccess = true;
        savedData = response;
      }

      if (isSuccess) {
        // Reset all forms
        setCustomYellowCodesPlayers([]);
        setCustomRedCodesPlayers([]);
        setCustomYellowCodesOfficials([]);
        setCustomRedCodesOfficials([]);
        setMisconductName('');
        setShowModal(false);

        // Reload sets and select the newly created set
        await loadMisconductSets();

        // Set the newly created set as selected
        if (savedData && typeof savedData === 'object' && 'id' in savedData) {
          const newSet = savedData as MisconductSet;
          setSelectedMisconductSet(newSet);
          await saveSelectedMisconductSet(newSet);
        }

        Alert.alert('Success', 'Custom misconduct codes saved successfully!');
      }
    } catch (error) {
      console.error('Error saving misconduct codes:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      Alert.alert('Error', `Failed to save misconduct codes: ${errorMessage}`);
    }
  };

  const renderSubcategoryInput = (codeId: string, subcategory: SubCategory, cardType: CardType) => (
    <View key={subcategory.id} style={styles.subcategoryInput}>
      <View style={styles.subcategoryInputRow}>
        <TextInput
          style={styles.subcategoryCodeInput}
          placeholder="Sub Code"
          placeholderTextColor={theme.text + '80'}
          value={subcategory.code}
          onChangeText={(text) => updateSubcategory(codeId, subcategory.id, 'code', text, cardType)}
        />
        <Text style={styles.separator}>-</Text>
        <TextInput
          style={styles.subcategoryDescriptionInput}
          placeholder="Sub Description"
          placeholderTextColor={theme.text + '80'}
          value={subcategory.description}
          onChangeText={(text) => updateSubcategory(codeId, subcategory.id, 'description', text, cardType)}
        />
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => deleteSubcategory(codeId, subcategory.id, cardType)}
        >
          <MaterialIcons name="delete" size={18} color={theme.text} style={{ opacity: 0.6 }} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderCustomCodeInput = (code: CustomCode) => (
    <View key={code.id} style={styles.customCodeInput}>
      <View style={styles.inputRow}>
        <TextInput
          style={styles.codeInput}
          placeholder="Code"
          placeholderTextColor={theme.text + '80'}
          value={code.code}
          onChangeText={(text) => updateCustomCode(code.id, 'code', text)}
        />
        <Text style={styles.separator}>-</Text>
        <TextInput
          style={styles.descriptionInput}
          placeholder="Description"
          placeholderTextColor={theme.text + '80'}
          value={code.description}
          onChangeText={(text) => updateCustomCode(code.id, 'description', text)}
        />
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => deleteCustomCode(code.id, code.cardType)}
        >
          <MaterialIcons name="delete" size={20} color={theme.text} style={{ opacity: 0.6 }} />
        </TouchableOpacity>
      </View>

      {/* Subcategories */}
      {code.subcategories && code.subcategories.length > 0 && (
        <View style={styles.subcategoriesContainer}>
          {code.subcategories.map((subcategory) =>
            renderSubcategoryInput(code.id, subcategory, code.cardType)
          )}
        </View>
      )}

      {/* Add subcategory button */}
      <TouchableOpacity
        style={styles.addSubcategoryButton}
        onPress={() => addSubcategory(code.id, code.cardType)}
      >
        <MaterialIcons name="add" size={16} color="#4CAF50" />
        <Text style={styles.addSubcategoryText}>Add Subcategory</Text>
      </TouchableOpacity>
    </View>
  );

  const renderCustomModal = () => (
    <Modal
      visible={showModal}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <View style={styles.modalContainer}>
        {/* Modal Header */}
        <View style={styles.modalHeader}>
          <TouchableOpacity
            style={styles.modalBackButton}
            onPress={() => setShowModal(false)}
          >
            <MaterialIcons name="arrow-back" size={24} color="#4CAF50" />
            <Text style={styles.modalBackText}>Back</Text>
          </TouchableOpacity>

          <Text style={styles.modalTitle}>Edit custom Misconduct</Text>

          <TouchableOpacity
            style={styles.modalSaveButton}
            onPress={saveCustomCodes}
          >
            <Text style={styles.modalSaveText}>Save</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent}>
          {/* Misconduct Name Input */}
          <View style={styles.nameInputContainer}>
            <Text style={styles.nameInputLabel}>Misconduct Name</Text>
            <TextInput
              style={styles.nameInput}
              placeholder="Enter misconduct set name"
              placeholderTextColor={theme.text + '80'}
              value={misconductName}
              onChangeText={setMisconductName}
            />
          </View>

          {/* Modal Tab selector */}
          <View style={styles.modalTabContainer}>
            <TouchableOpacity
              style={[
                styles.modalTab,
                modalTab === 'PLAYERS' && styles.modalActiveTab
              ]}
              onPress={() => setModalTab('PLAYERS')}
            >
              <Text style={[
                styles.modalTabText,
                modalTab === 'PLAYERS' && styles.modalActiveTabText
              ]}>
                Players
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.modalTab,
                modalTab === 'OFFICIALS' && styles.modalActiveTab
              ]}
              onPress={() => setModalTab('OFFICIALS')}
            >
              <Text style={[
                styles.modalTabText,
                modalTab === 'OFFICIALS' && styles.modalActiveTabText
              ]}>
                Team Officials
              </Text>
            </TouchableOpacity>
          </View>

          {/* Yellow Card Section */}
          <View style={styles.modalCardContainer}>
            <View style={styles.modalCardHeader}>
              <View style={styles.modalCardTitleContainer}>
                <Text style={styles.modalCardTitle}>Yellow</Text>
                <View style={[styles.modalCardIndicator, { backgroundColor: '#FFC107' }]} />
              </View>
              <TouchableOpacity
                style={styles.modalAddButton}
                onPress={() => addCustomCode('yellow')}
              >
                <Text style={styles.modalAddText}>Add</Text>
              </TouchableOpacity>
            </View>

            {(modalTab === 'PLAYERS' ? customYellowCodesPlayers : customYellowCodesOfficials).map(renderCustomCodeInput)}
          </View>

          {/* Red Card Section */}
          <View style={styles.modalCardContainer}>
            <View style={styles.modalCardHeader}>
              <View style={styles.modalCardTitleContainer}>
                <Text style={styles.modalCardTitle}>Red</Text>
                <View style={[styles.modalCardIndicator, { backgroundColor: '#F44336' }]} />
              </View>
              <TouchableOpacity
                style={styles.modalAddButton}
                onPress={() => addCustomCode('red')}
              >
                <Text style={styles.modalAddText}>Add</Text>
              </TouchableOpacity>
            </View>

            {(modalTab === 'PLAYERS' ? customRedCodesPlayers : customRedCodesOfficials).map(renderCustomCodeInput)}
          </View>
        </ScrollView>
      </View>
    </Modal>
  );

  const renderEditableSubcategoryItem = (codeId: string, subcategory: SubCategory, cardType: CardType) => (
    <View key={subcategory.id} style={styles.subcategoryInput}>
      <View style={styles.subcategoryInputRow}>
        <TextInput
          style={styles.subcategoryCodeInput}
          placeholder="Sub Code"
          placeholderTextColor={theme.text + '80'}
          value={subcategory.code}
          onChangeText={(text) => updateEditedSubcategory(codeId, subcategory.id, 'code', text, cardType)}
        />
        <Text style={styles.separator}>-</Text>
        <TextInput
          style={styles.subcategoryDescriptionInput}
          placeholder="Sub Description"
          placeholderTextColor={theme.text + '80'}
          value={subcategory.description}
          onChangeText={(text) => updateEditedSubcategory(codeId, subcategory.id, 'description', text, cardType)}
        />
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => deleteEditedSubcategory(codeId, subcategory.id, cardType)}
        >
          <MaterialIcons name="delete" size={18} color={theme.text} style={{ opacity: 0.6 }} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderEditableCodeItem = (code: MisconductCode, cardType: CardType) => (
    <View key={code.id} style={styles.editableCodeInput}>
      <View style={styles.editableInputRow}>
        <TextInput
          style={styles.editableCodeInputField}
          placeholder="Code"
          placeholderTextColor={theme.text + '80'}
          value={code.code}
          onChangeText={(text) => updateEditedCode(code.id, 'code', text, cardType)}
        />
        <Text style={styles.separator}>-</Text>
        <TextInput
          style={styles.editableDescriptionInput}
          placeholder="Description"
          placeholderTextColor={theme.text + '80'}
          value={code.description}
          onChangeText={(text) => updateEditedCode(code.id, 'description', text, cardType)}
        />
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => deleteEditedCode(code.id, cardType)}
        >
          <MaterialIcons name="delete" size={20} color={theme.text} style={{ opacity: 0.6 }} />
        </TouchableOpacity>
      </View>

      {/* Subcategories in edit mode */}
      {code.subcategories && code.subcategories.length > 0 && (
        <View style={styles.subcategoriesContainer}>
          {code.subcategories.map((subcategory) =>
            renderEditableSubcategoryItem(code.id, subcategory, cardType)
          )}
        </View>
      )}

      {/* Add subcategory button in edit mode */}
      <TouchableOpacity
        style={styles.addSubcategoryButton}
        onPress={() => addEditedSubcategory(code.id, cardType)}
      >
        <MaterialIcons name="add" size={16} color="#4CAF50" />
        <Text style={styles.addSubcategoryText}>Add Subcategory</Text>
      </TouchableOpacity>
    </View>
  );

  const renderMisconductCard = (title: string, cardType: CardType, codes: MisconductCode[]) => {
    const isEditing = cardType === 'yellow' ? editingYellow : editingRed;
    const editedCodes = cardType === 'yellow' ? editedYellowCodes : editedRedCodes;
    const hasSelectedSet = selectedMisconductSet !== null;
    const isHardcodedSet = selectedMisconductSet?.id === 'wales-default' || selectedMisconductSet?.id === 'england-fa-default';
    const displayCodes = isEditing ? editedCodes : codes;

    return (
      <View style={styles.cardContainer}>
        <View style={styles.cardHeader}>
          <View style={styles.cardTitleContainer}>
            <Text style={styles.cardTitle}>{title}</Text>
            <View style={[styles.cardIndicator, { backgroundColor: cardType === 'yellow' ? '#FFC107' : '#F44336' }]} />
          </View>

          {hasSelectedSet && !isHardcodedSet && (
            <View style={styles.cardActions}>
              {isEditing ? (
                <>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => cancelEditing(cardType)}
                  >
                    <Text style={styles.cancelButtonText}>Cancel</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.saveButton}
                    onPress={() => saveEditedCodes(cardType)}
                  >
                    <Text style={styles.saveButtonText}>Save</Text>
                  </TouchableOpacity>
                </>
              ) : (
                <TouchableOpacity
                  style={styles.editButton}
                  onPress={() => cardType === 'yellow' ? startEditingYellow() : startEditingRed()}
                >
                  <Text style={styles.editButtonText}>Edit</Text>
                </TouchableOpacity>
              )}
            </View>
          )}
        </View>

        <View style={styles.codesContainer}>
          {isEditing ? (
            <>
              {displayCodes.map((code) => renderEditableCodeItem(code, cardType))}
              <TouchableOpacity
                style={styles.addCodeButton}
                onPress={() => addEditedCode(cardType)}
              >
                <MaterialIcons name="add" size={20} color="#4CAF50" />
                <Text style={styles.addCodeButtonText}>Add {title} Code</Text>
              </TouchableOpacity>
            </>
          ) : (
            displayCodes.length > 0 ? (
              displayCodes.map((code) => (
                <View key={code.id} style={styles.codeItem}>
                  <Text style={styles.codeText}>{code.code} - {code.description}</Text>
                  {code.subcategories && code.subcategories.length > 0 && (
                    <View style={styles.subcategoriesDisplayContainer}>
                      {code.subcategories.map((subcategory) => (
                        <View key={subcategory.id} style={styles.subcategoryDisplayItem}>
                          <Text style={styles.subcategoryDisplayText}>
                            {subcategory.code} - {subcategory.description}
                          </Text>
                        </View>
                      ))}
                    </View>
                  )}
                </View>
              ))
            ) : (
              <View style={styles.emptyCodeItem}>
                <Text style={styles.emptyCodeText}>No {title.toLowerCase()} codes added</Text>
              </View>
            )
          )}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <Header title="Manage Misconduct" showBackButton={true} />

      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        {/* Selected misconduct codes section */}
        <TouchableOpacity
          style={styles.sectionItem}
          onPress={() => setShowDropdown(!showDropdown)}
        >
          <Text style={styles.sectionItemText}>Selected misconduct codes</Text>
          <View style={styles.sectionItemRight}>
            <Text style={styles.sectionItemValue}>
              {selectedMisconductSet ? selectedMisconductSet.name : 'None selected'}
            </Text>
            <MaterialIcons
              name={showDropdown ? "keyboard-arrow-up" : "chevron-right"}
              size={22}
              color={theme.text}
              style={{ opacity: 0.5 }}
            />
          </View>
        </TouchableOpacity>

        {/* Dropdown for misconduct sets */}
        {showDropdown && (
          <View style={styles.dropdownContainer}>
            {Array.isArray(misconductSets) && misconductSets.map((set) => (
              <TouchableOpacity
                key={set.id}
                style={styles.dropdownItem}
                onPress={async () => {
                  setSelectedMisconductSet(set);
                  await saveSelectedMisconductSet(set);
                  setShowDropdown(false);
                }}
              >
                <Text style={[styles.dropdownText, selectedMisconductSet?.id === set.id && styles.selectedDropdownText]}>
                  {set.name}
                </Text>
                {selectedMisconductSet?.id === set.id && (
                  <MaterialIcons name="check" size={20} color="#4CAF50" />
                )}
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Add custom codes section */}
        <TouchableOpacity
          style={styles.sectionItem}
          onPress={() => setShowModal(true)}
        >
          <Text style={styles.sectionItemText}>Add custom codes</Text>
        </TouchableOpacity>

        {/* Tab selector */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[
              styles.tab,
              selectedTab === 'players' && styles.activeTab
            ]}
            onPress={() => setSelectedTab('players')}
          >
            <Text style={[
              styles.tabText,
              selectedTab === 'players' && styles.activeTabText
            ]}>
              Players
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.tab,
              selectedTab === 'officials' && styles.activeTab
            ]}
            onPress={() => setSelectedTab('officials')}
          >
            <Text style={[
              styles.tabText,
              selectedTab === 'officials' && styles.activeTabText
            ]}>
              Team Officials
            </Text>
          </TouchableOpacity>
        </View>

        {/* Misconduct cards */}
        {renderMisconductCard('Yellow', 'yellow', getDisplayedYellowCodes())}
        {renderMisconductCard('Red', 'red', getDisplayedRedCodes())}
      </ScrollView>

      {/* Custom Modal */}
      {renderCustomModal()}
    </View>
  );
};

const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.background,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 32,
    paddingTop: 16,
  },
  sectionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: theme.card,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: isDarkMode ? 'transparent' : "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.10,
    shadowRadius: 2.00,
    elevation: isDarkMode ? 0 : 2,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
  },
  sectionItemText: {
    fontSize: 15,
    color: theme.text,
    fontWeight: '500',
    flex: 1,
  },
  sectionItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionItemValue: {
    fontSize: 15,
    color: theme.text,
    opacity: 0.7,
    fontWeight: '500',
    marginRight: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: theme.card,
    borderRadius: 12,
    padding: 4,
    marginBottom: 20,
    marginTop: 8,
    shadowColor: isDarkMode ? 'transparent' : "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.10,
    shadowRadius: 2.00,
    elevation: isDarkMode ? 0 : 2,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: '#4CAF50',
  },
  tabText: {
    fontSize: 15,
    fontWeight: '600',
    color: theme.text,
  },
  activeTabText: {
    color: '#FFFFFF',
  },
  cardContainer: {
    backgroundColor: theme.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    shadowColor: isDarkMode ? 'transparent' : "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.10,
    shadowRadius: 2.00,
    elevation: isDarkMode ? 0 : 2,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.text,
    marginRight: 8,
  },
  cardIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  editButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  editButtonText: {
    fontSize: 15,
    fontWeight: '500',
    color: '#4CAF50',
  },
  codesContainer: {
    gap: 8,
  },
  codeItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.border,
  },
  codeText: {
    fontSize: 14,
    color: theme.text,
    fontWeight: '500',
  },
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: theme.background,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.border,
  },
  modalBackButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalBackText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4CAF50',
    marginLeft: 4,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.text,
  },
  modalSaveButton: {
    paddingHorizontal: 8,
  },
  modalSaveText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#4CAF50',
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  modalTabContainer: {
    flexDirection: 'row',
    backgroundColor: theme.card,
    borderRadius: 12,
    padding: 4,
    marginBottom: 20,
    shadowColor: isDarkMode ? 'transparent' : "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.10,
    shadowRadius: 2.00,
    elevation: isDarkMode ? 0 : 2,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
  },
  modalTab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  modalActiveTab: {
    backgroundColor: '#4CAF50',
  },
  modalTabText: {
    fontSize: 15,
    fontWeight: '600',
    color: theme.text,
  },
  modalActiveTabText: {
    color: '#FFFFFF',
  },
  modalCardContainer: {
    backgroundColor: theme.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    shadowColor: isDarkMode ? 'transparent' : "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.10,
    shadowRadius: 2.00,
    elevation: isDarkMode ? 0 : 2,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
  },
  modalCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalCardTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalCardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.text,
    marginRight: 8,
  },
  modalCardIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  modalAddButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  modalAddText: {
    fontSize: 15,
    fontWeight: '500',
    color: '#4CAF50',
  },
  customCodeInput: {
    marginBottom: 12,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.border,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  codeInput: {
    fontSize: 14,
    color: theme.text,
    fontWeight: '500',
    minWidth: 60,
    maxWidth: 80,
  },
  separator: {
    fontSize: 14,
    color: theme.text,
    fontWeight: '500',
    marginHorizontal: 8,
  },
  descriptionInput: {
    flex: 1,
    fontSize: 14,
    color: theme.text,
    fontWeight: '500',
  },
  deleteButton: {
    padding: 4,
    marginLeft: 8,
  },
  // Misconduct name input styles
  nameInputContainer: {
    marginBottom: 20,
  },
  nameInputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.text,
    marginBottom: 8,
  },
  nameInput: {
    backgroundColor: theme.card,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.border,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 15,
    color: theme.text,
  },
  // Dropdown styles
  dropdownContainer: {
    backgroundColor: theme.card,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: isDarkMode ? 'transparent' : "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.10,
    shadowRadius: 2.00,
    elevation: isDarkMode ? 0 : 2,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.border,
  },
  dropdownText: {
    fontSize: 15,
    color: theme.text,
    fontWeight: '500',
    flex: 1,
  },
  selectedDropdownText: {
    color: '#4CAF50',
  },
  // Edit mode styles
  cardActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  cancelButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: theme.text + '20',
    borderRadius: 6,
  },
  cancelButtonText: {
    fontSize: 15,
    fontWeight: '500',
    color: theme.text,
  },
  saveButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#4CAF50',
    borderRadius: 6,
  },
  saveButtonText: {
    fontSize: 15,
    fontWeight: '500',
    color: '#FFFFFF',
  },
  editableCodeInput: {
    marginBottom: 12,
  },
  editableInputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.border,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  editableCodeInputField: {
    fontSize: 14,
    color: theme.text,
    fontWeight: '500',
    minWidth: 60,
    maxWidth: 80,
    borderWidth: 1,
    borderColor: theme.border,
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: theme.card,
  },
  editableDescriptionInput: {
    flex: 1,
    fontSize: 14,
    color: theme.text,
    fontWeight: '500',
    borderWidth: 1,
    borderColor: theme.border,
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: theme.card,
  },
  addCodeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#4CAF50',
    borderStyle: 'dashed',
  },
  addCodeButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4CAF50',
    marginLeft: 8,
  },
  emptyCodeItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.border,
    alignItems: 'center',
  },
  emptyCodeText: {
    fontSize: 14,
    color: theme.text,
    opacity: 0.5,
    fontStyle: 'italic',
  },
  // Subcategory styles
  subcategoriesContainer: {
    marginTop: 8,
    marginLeft: 16,
    paddingLeft: 12,
    borderLeftWidth: 2,
    borderLeftColor: theme.text + '20',
  },
  subcategoryInput: {
    marginBottom: 8,
  },
  subcategoryInputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.background + '80',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.border + '60',
    paddingHorizontal: 10,
    paddingVertical: 6,
  },
  subcategoryCodeInput: {
    fontSize: 13,
    color: theme.text,
    fontWeight: '500',
    minWidth: 50,
    maxWidth: 70,
  },
  subcategoryDescriptionInput: {
    flex: 1,
    fontSize: 13,
    color: theme.text,
    fontWeight: '500',
  },
  addSubcategoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: theme.background + '60',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#4CAF50' + '40',
    borderStyle: 'dashed',
    marginTop: 8,
    marginLeft: 16,
  },
  addSubcategoryText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#4CAF50',
    marginLeft: 4,
  },
  // Subcategory display styles
  subcategoriesDisplayContainer: {
    marginTop: 8,
    marginLeft: 16,
    paddingLeft: 12,
    borderLeftWidth: 2,
    borderLeftColor: theme.text + '20',
  },
  subcategoryDisplayItem: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    backgroundColor: theme.background + '60',
    borderRadius: 4,
    marginBottom: 4,
    borderWidth: 1,
    borderColor: theme.border + '40',
  },
  subcategoryDisplayText: {
    fontSize: 13,
    color: theme.text,
    fontWeight: '400',
    opacity: 0.8,
  },
});

export default ManageMisconductCodeScreen;