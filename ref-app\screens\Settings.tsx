import React, { useState } from 'react';
import { View, Text, ScrollView, Switch, TouchableOpacity, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import Header from '../components/Header';
import { useTheme } from '../contexts/ThemeContext';

// Prop type definitions
type SectionItem = {
  label: string;
  value?: string | boolean;
  arrow?: boolean;
  switch?: boolean;
  onToggle?: (value: boolean) => void;
  onPress?: () => void;
};

type SectionProps = {
  title: string;
  items: SectionItem[];
  styles: any; // Pass styles from getStyles
  theme: any; // Pass theme object
};

// Reusable Section Component (Now receives styles and theme)
const Section = ({ title, items, styles, theme }: SectionProps) => (
  <>
    <Text style={styles.sectionHeader}>{title}</Text>
    <View style={styles.sectionBox}>
      {items.map((item: SectionItem) => (
        <TouchableOpacity
          key={item.label}
          style={styles.row}
          activeOpacity={item.arrow || item.onPress ? 0.7 : 1}
          onPress={item.onPress}
          disabled={!item.arrow && !item.onPress}
        >
          <Text style={styles.rowLabel}>{item.label}</Text>
          <View style={styles.rowRight}>
            {item.value && typeof item.value === 'string' && <Text style={styles.rowValue}>{item.value}</Text>}
            {item.switch && item.onToggle && (
              <Switch
                value={item.value as boolean}
                onValueChange={item.onToggle}
                trackColor={{ false: theme.border, true: `${theme.primary}60` }}
                thumbColor={item.value ? theme.primary : (theme.isDarkMode ? theme.card : '#f4f3f4')}
                ios_backgroundColor={theme.border}
              />
            )}
            {item.arrow && <MaterialIcons name="chevron-right" size={22} color={theme.text} style={{ opacity: 0.5 }}/>}
          </View>
        </TouchableOpacity>
      ))}
    </View>
  </>
);

const Settings = () => {
  const { isDarkMode, toggleTheme, theme } = useTheme();
  const styles = getStyles(theme, isDarkMode);
  const navigation = useNavigation();
  
  // State for the switches
  const [startHalftimeAutomatically, setStartHalftimeAutomatically] = useState(true);
  const [resetTimeToZero, setResetTimeToZero] = useState(false);
  const [pushNotification, setPushNotification] = useState(true);
  const [alwaysAskGoalType, setAlwaysAskGoalType] = useState(false);

  const timerSettings: SectionItem[] = [
    { label: 'Timer options', arrow: true },
    { 
      label: 'Start halftime automatically', 
      switch: true, 
      value: startHalftimeAutomatically, 
      onToggle: setStartHalftimeAutomatically 
    },
    { 
      label: 'Rest time to 0 from second half', 
      switch: true, 
      value: resetTimeToZero, 
      onToggle: setResetTimeToZero 
    },
  ];

  const manage: SectionItem[] = [
    { label: 'Manage your template', arrow: true, onPress: () => navigation.navigate('ManageTemplate' as never) },
    { label: 'Manage your watch', arrow: true },
    { label: 'Manage misconduct code', arrow: true, onPress: () => navigation.navigate('ManageMisconductCode' as never) },
    { label: 'Evaluate Questions', arrow: true, onPress: () => navigation.navigate('EvaluateQuestions' as never) },
    { label: 'API Connection Test', arrow: true, onPress: () => navigation.navigate('ApiTest' as never) },
    {
      label: 'Push notification',
      switch: true,
      value: pushNotification,
      onToggle: setPushNotification
    },
    { label: 'Currency', value: '£ GBP', arrow: true },
    { label: 'Language', value: '🇬🇧', arrow: true },
  ];

  const preference: SectionItem[] = [
    { 
      label: 'Dark Mode', 
      switch: true, 
      value: isDarkMode, 
      onToggle: toggleTheme 
    },
    { label: 'Save match calendar', arrow: true },
    { label: 'Units of measure', arrow: true },
    { label: 'Time format', value: '24 hours', arrow: true },
    { label: 'Season start date', value: '25-08-2025', arrow: true },
    { label: 'Custom Sprint map options', arrow: true },
    { 
      label: 'Always ask for goal type e.g. free kick, penalty', 
      switch: true, 
      value: alwaysAskGoalType, 
      onToggle: setAlwaysAskGoalType 
    },
  ];

  return (
    <View style={styles.container}>
      <Header title="Settings" showBackButton={true} />
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <Section title="Timer Settings" items={timerSettings} styles={styles} theme={theme} />
        <Section title="Manage" items={manage} styles={styles} theme={theme} />
        <Section title="Preference" items={preference} styles={styles} theme={theme} />
      </ScrollView>
    </View>
  );
};

const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.background,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 32,
    paddingTop: 16,
  },
  sectionHeader: {
    fontWeight: '700',
    fontSize: 15,
    color: theme.text,
    marginTop: 10,
    marginBottom: 12,
    marginLeft: 4,
  },
  sectionBox: {
    backgroundColor: theme.card,
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingTop: 12,
    paddingBottom: 2,
    marginBottom: 18,
    // Conditional shadow and border
    shadowColor: isDarkMode ? 'transparent' : "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: isDarkMode ? 0 : 0.10,
    shadowRadius: 2.00,
    elevation: isDarkMode ? 0 : 2,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 14,
    paddingHorizontal: 14,
    backgroundColor: theme.card,
    borderWidth: 1,
    borderColor: theme.border1,
    borderRadius: 10,
    marginBottom: 10,
  },
  rowLabel: {
    fontSize: 15,
    color: theme.text,
    fontWeight: '500',
    flex: 1,
    marginRight: 8,
  },
  rowRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rowValue: {
    fontSize: 15,
    color: theme.text,
    opacity: 0.7,
    fontWeight: '500',
    marginRight: 8,
  },
});

export default Settings;