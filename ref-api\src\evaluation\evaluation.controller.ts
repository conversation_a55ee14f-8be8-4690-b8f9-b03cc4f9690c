import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  HttpException,
  HttpStatus,
  UseGuards,
  Request,
} from '@nestjs/common';
import { EvaluationService } from './evaluation.service';
import { CreateEvaluationQuestionsDto } from './dto/create-evaluation-questions.dto';
import { CreateMatchEvaluationDto } from './dto/create-match-evaluation.dto';
import { FirebaseAuthGuard } from '../auth/firebase-auth.guard';

@Controller()
export class EvaluationController {
  constructor(private readonly evaluationService: EvaluationService) {}

  /**
   * GET /evaluation-questions/:userId
   * Get evaluation questions for a specific user
   */
  @UseGuards(FirebaseAuthGuard)
  @Get('evaluation-questions/:userId')
  async getEvaluationQuestions(@Param('userId') userId: string, @Request() req: any) {
    try {
      // Ensure user can only access their own questions
      if (userId !== req.user.id) {
        throw new HttpException(
          {
            success: false,
            error: 'Unauthorized',
            message: 'You can only access your own evaluation questions'
          },
          HttpStatus.FORBIDDEN
        );
      }

      const questions = await this.evaluationService.getEvaluationQuestions(userId);

      return {
        success: true,
        data: questions,
        message: `Found ${questions.length} evaluation questions`
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      console.error('❌ Error fetching evaluation questions:', error);
      throw new HttpException(
        {
          success: false,
          error: 'Internal server error',
          message: 'Failed to fetch evaluation questions'
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * POST /evaluation-questions/:userId
   * Create or update evaluation questions for a specific user
   */
  @UseGuards(FirebaseAuthGuard)
  @Post('evaluation-questions/:userId')
  async createEvaluationQuestions(
    @Param('userId') userId: string,
    @Body() body: { questions: any[] },
    @Request() req: any
  ) {
    try {
      // Ensure user can only update their own questions
      if (userId !== req.user.id) {
        throw new HttpException(
          {
            success: false,
            error: 'Unauthorized',
            message: 'You can only update your own evaluation questions'
          },
          HttpStatus.FORBIDDEN
        );
      }

      const dto: CreateEvaluationQuestionsDto = {
        userId,
        questions: body.questions
      };

      const questions = await this.evaluationService.createEvaluationQuestions(dto);

      return {
        success: true,
        data: questions,
        message: `Successfully saved ${questions.length} questions`
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      console.error('❌ Error saving evaluation questions:', error);
      throw new HttpException(
        {
          success: false,
          error: 'Internal server error',
          message: 'Failed to save evaluation questions'
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * POST /evaluation-answers
   * Create match evaluation with answers
   */
  @UseGuards(FirebaseAuthGuard)
  @Post('evaluation-answers')
  async createMatchEvaluation(@Body() dto: CreateMatchEvaluationDto, @Request() req: any) {
    try {
      // Ensure user can only create evaluations for themselves
      if (dto.userId !== req.user.id) {
        throw new HttpException(
          {
            success: false,
            error: 'Unauthorized',
            message: 'You can only create evaluations for yourself'
          },
          HttpStatus.FORBIDDEN
        );
      }

      const evaluation = await this.evaluationService.createMatchEvaluation(dto);

      return {
        success: true,
        data: evaluation,
        message: 'Match evaluation created successfully'
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      console.error('❌ Error creating match evaluation:', error);
      throw new HttpException(
        {
          success: false,
          error: 'Internal server error',
          message: 'Failed to create match evaluation'
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * GET /evaluation-answers/match/:matchId
   * Get match evaluation by matchId
   */
  @UseGuards(FirebaseAuthGuard)
  @Get('evaluation-answers/match/:matchId')
  async getMatchEvaluation(@Param('matchId') matchId: string, @Request() req: any) {
    try {
      // Note: We don't check ownership here since match results can be viewed
      // But you might want to add ownership check based on your requirements
      
      const evaluation = await this.evaluationService.getMatchEvaluation(matchId);

      if (!evaluation) {
        return {
          success: true,
          data: null,
          message: 'No evaluation found for this match'
        };
      }

      return {
        success: true,
        data: evaluation,
        message: 'Match evaluation retrieved successfully'
      };
    } catch (error) {
      console.error('❌ Error fetching match evaluation:', error);
      throw new HttpException(
        {
          success: false,
          error: 'Internal server error',
          message: 'Failed to fetch match evaluation'
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * DELETE /evaluation-answers/match/:matchId
   * Delete match evaluation (optional cleanup)
   */
  @UseGuards(FirebaseAuthGuard)
  @Delete('evaluation-answers/match/:matchId')
  async deleteMatchEvaluation(@Param('matchId') matchId: string, @Request() req: any) {
    try {
      const deleted = await this.evaluationService.deleteMatchEvaluation(matchId);

      if (!deleted) {
        return {
          success: false,
          message: 'No evaluation found to delete'
        };
      }

      return {
        success: true,
        message: 'Match evaluation deleted successfully'
      };
    } catch (error) {
      console.error('❌ Error deleting match evaluation:', error);
      throw new HttpException(
        {
          success: false,
          error: 'Internal server error',
          message: 'Failed to delete match evaluation'
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}