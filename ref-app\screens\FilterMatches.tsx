import React, { useState, useEffect, ReactNode } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Switch,
  UIManager,
  Platform,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import Svg, { Path, Rect, Text as SvgText } from 'react-native-svg';
import Header from '../components/Header';
import ModalPicker from '../components/ModalPicker';
import { useTheme } from '../contexts/ThemeContext';
import MatchService from '../api/matchService';

// Types
interface Filters {
  competitions?: string[];
  venues?: string[];
  teams?: string[];
  matchOfficials?: string[];
  roles?: string[];
  minYellowCards?: number;
  minRedCards?: number;
  minGoals?: number;
  hadExtraTime?: boolean;
  wasAbandoned?: boolean;
}

type RootStackParamList = {
  Dashboard: { screen?: string; params?: { filters?: Filters } } | undefined;
  FilterMatches: undefined;
};

type FilterMatchesNavigationProp = NativeStackNavigationProp<RootStackParamList, 'FilterMatches'>;
type FilterMatchesRouteProp = RouteProp<RootStackParamList, 'FilterMatches'>;
type DashboardNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Dashboard'>;

// Enable LayoutAnimation for Android
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

// --- SVG ICON DEFINITIONS (Updated to accept props) ---
const RefereeIcon = ({ color }: { color: string }) => ( <Svg height="24" width="24" viewBox="2 2 24 24"><Path d="M12.375 21.375C10.5 21.375 8.90625 20.7188 7.59375 19.4062C6.28125 18.0938 5.625 16.5 5.625 14.625C5.625 14.4187 5.63437 14.2125 5.65312 14.0062C5.67188 13.8 5.7 13.5938 5.7375 13.3875C5.64375 13.425 5.53125 13.4531 5.4 13.4719C5.26875 13.4906 5.15625 13.5 5.0625 13.5C4.275 13.5 3.60938 13.2281 3.06562 12.6844C2.52187 12.1406 2.25 11.475 2.25 10.6875C2.25 9.9 2.50781 9.23438 3.02344 8.69062C3.53906 8.14687 4.19062 7.875 4.97812 7.875C5.59687 7.875 6.15469 8.04844 6.65156 8.39531C7.14844 8.74219 7.5 9.1875 7.70625 9.73125C8.325 9.16875 9.03281 8.71875 9.82969 8.38125C10.6266 8.04375 11.475 7.875 12.375 7.875H24.75V12.375H19.125V14.625C19.125 16.5 18.4688 18.0938 17.1562 19.4062C15.8438 20.7188 14.25 21.375 12.375 21.375ZM5.0625 11.8125C5.38125 11.8125 5.64844 11.7047 5.86406 11.4891C6.07969 11.2734 6.1875 11.0062 6.1875 10.6875C6.1875 10.3687 6.07969 10.1016 5.86406 9.88594C5.64844 9.67031 5.38125 9.5625 5.0625 9.5625C4.74375 9.5625 4.47656 9.67031 4.26094 9.88594C4.04531 10.1016 3.9375 10.3687 3.9375 10.6875C3.9375 11.0062 4.04531 11.2734 4.26094 11.4891C4.47656 11.7047 4.74375 11.8125 5.0625 11.8125ZM12.375 18.5625C13.4625 18.5625 14.3906 18.1781 15.1594 17.4094C15.9281 16.6406 16.3125 15.7125 16.3125 14.625C16.3125 13.5375 15.9281 12.6094 15.1594 11.8406C14.3906 11.0719 13.4625 10.6875 12.375 10.6875C11.2875 10.6875 10.3594 11.0719 9.59062 11.8406C8.82187 12.6094 8.4375 13.5375 8.4375 14.625C8.4375 15.7125 8.82187 16.6406 9.59062 17.4094C10.3594 18.1781 11.2875 18.5625 12.375 18.5625Z" fill={color}/></Svg>);
const AssistantIcon = ({ color }: { color: string }) => ( <Svg height="24" width="24" viewBox="0 0 24 24"><Path d="M9 6H11V4H9V6ZM13 6V4H15V6H13ZM9 14V12H11V14H9ZM17 10V8H19V10H17ZM17 14V12H19V14H17ZM13 14V12H15V14H13ZM17 6V4H19V6H17ZM11 8V6H13V8H11ZM5 20V4H7V6H9V8H7V10H9V12H7V20H5ZM15 12V10H17V12H15ZM11 12V10H13V12H11ZM9 10V8H11V10H9ZM13 10V8H15V10H13ZM15 8V6H17V8H15Z" fill={color}/></Svg>);

// --- CORRECTED: FourthOfficialIcon is now a component that accepts theme ---
const FourthOfficialIcon = ({ theme }: { theme: any }) => (
  <Svg width="24" height="24" viewBox="0 0 24 24">
    <Rect 
      x="2" 
      y="2" 
      width="20" 
      height="20" 
      rx="4"
      stroke={theme.text}
      strokeWidth="1.5"
      fill="none"
    />
    <SvgText
      x="12"
      y="16"
      textAnchor="middle"
      fontSize="12"
      fontWeight="600"
      fill={theme.text}
    >
      4th
    </SvgText>
  </Svg>
);

const PlusIcon: React.FC<{ color?: string; size?: number; }> = ({ color, size = 16 }) => ( <Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Path d="M12 5V19M5 12H19" stroke={color} strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/></Svg> );
const MinusIcon: React.FC<{ color?: string; size?: number; }> = ({ color, size = 16 }) => ( <Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Path d="M5 12H19" stroke={color} strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/></Svg> );

interface SectionCardProps { title: string; children: ReactNode; styles: any; }
interface DropdownRowProps { label?: string; value?: string | null; placeholder: string; onPress?: () => void; disabled?: boolean; styles: any; theme: any; }
interface ToggleRowProps { icon?: ReactNode; label: string; value: boolean; onValueChange: (value: boolean) => void; styles: any; theme: any; isDarkMode: boolean; }
interface NumericStepperRowProps { label: string; value: number; onDecrement: () => void; onIncrement: () => void; styles: any; theme: any; }

// --- REUSABLE COMPONENTS ---
const SectionCard: React.FC<SectionCardProps> = ({ title, children, styles }) => ( <View style={styles.card}><Text style={styles.cardTitle}>{title}</Text>{children}</View> );

const DropdownRow: React.FC<DropdownRowProps> = ({ label, value, placeholder, onPress, disabled = false, styles, theme }) => ( 
  <TouchableOpacity style={styles.row} onPress={onPress} disabled={disabled}>
    {label && <Text style={styles.label}>{label}</Text>}
    <View style={styles.valueContainer}>
      <Text style={[styles.value, !value && styles.placeholder]}>{value || placeholder}</Text>
      <Ionicons name="chevron-down" size={18} color={theme.text} style={{ opacity: 0.6 }} />
    </View>
  </TouchableOpacity> 
);

const ToggleRow: React.FC<ToggleRowProps> = ({ icon, label, value, onValueChange, styles, theme, isDarkMode }) => ( 
  <View style={styles.row}>
    <View style={styles.labelContainer}>
      {icon}
      <Text style={styles.label}>{label}</Text>
    </View>
    <View style={{ transform: [{ scaleX: 0.9 }, { scaleY: 0.9 }] }}>
      <Switch 
        trackColor={{ false: theme.border, true: `${theme.primary}60` }}
        thumbColor={value ? theme.primary : (isDarkMode ? theme.card : '#f4f3f4')} 
        ios_backgroundColor={theme.border} 
        onValueChange={onValueChange} 
        value={value} 
      />
    </View>
  </View> 
);

const NumericStepperRow: React.FC<NumericStepperRowProps> = ({ label, value, onDecrement, onIncrement, styles, theme }) => ( 
  <View style={styles.row}>
    <Text style={styles.label}>{label}</Text>
    <View style={styles.stepperContainer}>
      <TouchableOpacity onPress={onDecrement} style={styles.stepperButton}>
        <MinusIcon size={14} color={theme.text} />
      </TouchableOpacity>
      <Text style={styles.stepperValue}>{value}</Text>
      <TouchableOpacity onPress={onIncrement} style={styles.stepperButton}>
        <PlusIcon size={14} color={theme.text} />
      </TouchableOpacity>
    </View>
  </View> 
);

// --- MAIN FILTER SCREEN COMPONENT ---
const FilterMatches = () => {
  const navigation = useNavigation<FilterMatchesNavigationProp>();
  const route = useRoute<FilterMatchesRouteProp>();
  const { theme, isDarkMode } = useTheme();
  const styles = getStyles(theme, isDarkMode);

  // Filter options from API
  const [filterOptions, setFilterOptions] = useState({
    competitions: [] as string[],
    venues: [] as string[],
    teams: [] as string[],
    matchOfficials: [] as string[],
    roles: [] as string[],
  });
  const [isLoadingOptions, setIsLoadingOptions] = useState(true);

  // Filter selections
  const [selectedCompetitions, setSelectedCompetitions] = useState<string[]>([]);
  const [selectedVenues, setSelectedVenues] = useState<string[]>([]);
  const [selectedTeams, setSelectedTeams] = useState<string[]>([]);
  const [selectedOfficials, setSelectedOfficials] = useState<string[]>([]);
  const [selectedRoles, setSelectedRoles] = useState<string[]>([]);

  // Modal states
  const [modalStates, setModalStates] = useState({
    competitions: false,
    venues: false,
    teams: false,
    officials: false,
    roles: false,
  });

  const [dateOption, setDateOption] = useState('Career');
  const [isDateRangeEnabled, setIsDateRangeEnabled] = useState(false);
  const [roles, setRoles] = useState({ referee: true, assistant: true, fourthOfficial: true, observer: true });
  const [counters, setCounters] = useState({ yellowCards: 0, redCards: 0, goals: 0 });
  const [toggles, setToggles] = useState({ extraTime: false, PK: false, abandoned: false });
  
  useEffect(() => { setIsDateRangeEnabled(dateOption === 'Custom'); }, [dateOption]);

  // Load filter options on mount
  useEffect(() => {
    loadFilterOptions();
  }, []);

  const loadFilterOptions = async () => {
    try {
      setIsLoadingOptions(true);
      console.log('🔄 Loading filter options...');
      const response = await MatchService.getFilterOptions();
      
      console.log('📊 Filter options response:', response);
      
      if (response.success && response.data) {
        console.log('✅ Filter options loaded:', response.data);
        setFilterOptions(response.data);
      } else {
        console.log('❌ Failed to get filter options:', response);
      }
    } catch (error) {
      console.error('💥 Error loading filter options:', error);
    } finally {
      setIsLoadingOptions(false);
    }
  };

  const handleDateOptionPress = () => {
    const options = ["Career", "Year", "Season", "Month", "Custom"];
    const currentIndex = options.indexOf(dateOption);
    const nextIndex = (currentIndex + 1) % options.length;
    setDateOption(options[nextIndex]);
  };

  const handleReset = () => {
    setDateOption('Career');
    setRoles({ referee: true, assistant: true, fourthOfficial: true, observer: true });
    setCounters({ yellowCards: 0, redCards: 0, goals: 0 });
    setToggles({ extraTime: false, PK: false, abandoned: false });
    setSelectedCompetitions([]);
    setSelectedVenues([]);
    setSelectedTeams([]);
    setSelectedOfficials([]);
    setSelectedRoles([]);
    navigation.navigate('Dashboard', { 
      screen: 'DashboardHome', 
      params: { filters: undefined } 
    });
  };

  const handleApply = () => {
    // Build active roles from toggles
    const activeRoles = [];
    if (roles.referee) activeRoles.push('Referee');
    if (roles.assistant) activeRoles.push('AR1', 'AR2', 'Assistant Referee 1', 'Assistant Referee 2');
    if (roles.fourthOfficial) activeRoles.push('4th Official', 'Fourth Official');
    if (roles.observer) activeRoles.push('Observer');

    // Combine toggle roles with selected roles
    const allSelectedRoles = [...new Set([...activeRoles, ...selectedRoles])];

    // Build filters object - undefined means no restriction for that criteria
    const filters = {
      // Dropdown selections - empty array means no restriction
      competitions: selectedCompetitions.length > 0 ? selectedCompetitions : undefined,
      venues: selectedVenues.length > 0 ? selectedVenues : undefined,
      teams: selectedTeams.length > 0 ? selectedTeams : undefined,
      matchOfficials: selectedOfficials.length > 0 ? selectedOfficials : undefined,
      
      // Role restrictions - only apply if not all roles are selected
      roles: allSelectedRoles.length > 0 && allSelectedRoles.length < 8 ? allSelectedRoles : undefined, // 8 = total possible roles
      
      // Match statistics - only apply if user set values > 0 or toggled ON
      minYellowCards: counters.yellowCards > 0 ? counters.yellowCards : undefined,
      minRedCards: counters.redCards > 0 ? counters.redCards : undefined,
      minGoals: counters.goals > 0 ? counters.goals : undefined,
      hadExtraTime: toggles.extraTime ? true : undefined,
      wasAbandoned: toggles.abandoned ? true : undefined,
      
      // Flag to indicate statistics filters should only apply to previous matches
      applyStatsToAll: false,
    };

    console.log('🔍 Applying filters:', filters);

    navigation.navigate('Dashboard', { 
      screen: 'DashboardHome', 
      params: { filters } 
    });
  };
  
  const createCounterHandler = (key: keyof typeof counters, delta: number) => () => {
    setCounters(prev => ({ ...prev, [key]: Math.max(0, prev[key] + delta) }));
  };

  // Helper functions for modals
  const openModal = (type: keyof typeof modalStates) => {
    setModalStates(prev => ({ ...prev, [type]: true }));
  };

  const closeModal = (type: keyof typeof modalStates) => {
    setModalStates(prev => ({ ...prev, [type]: false }));
  };

  const getDisplayText = (items: string[], singular: string) => {
    if (items.length === 0) return `Select ${singular}`;
    if (items.length === 1) return items[0];
    return `${items.length} ${singular}s selected`;
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="Filter Matches"
        leftComponent={<TouchableOpacity onPress={handleReset}><Text style={styles.headerActionText}>Reset</Text></TouchableOpacity>}
        rightComponent={<TouchableOpacity onPress={handleApply}><Text style={styles.headerActionText}>Apply</Text></TouchableOpacity>}
      />
      <ScrollView contentContainerStyle={styles.scrollContainer} keyboardShouldPersistTaps="handled">
        <SectionCard title="Date" styles={styles}>
          <DropdownRow label="Options" value={dateOption} placeholder="Select option" onPress={handleDateOptionPress} styles={styles} theme={theme}/>
        </SectionCard>
        
        {isDateRangeEnabled && ( 
          <SectionCard title="Date range" styles={styles}>
            <DropdownRow label="From" placeholder="Select start date" styles={styles} theme={theme} />
            <View style={styles.divider} />
            <DropdownRow label="To" placeholder="Select end date" styles={styles} theme={theme} />
          </SectionCard> 
        )}
        
        <SectionCard title="Competition" styles={styles}>
          <DropdownRow 
            value={getDisplayText(selectedCompetitions, 'Competition')} 
            placeholder="Select Competition" 
            onPress={() => openModal('competitions')}
            styles={styles} 
            theme={theme} 
          />
        </SectionCard>
        
        <SectionCard title="Venue" styles={styles}>
          <DropdownRow 
            value={getDisplayText(selectedVenues, 'Venue')} 
            placeholder="Select Venue" 
            onPress={() => openModal('venues')}
            styles={styles} 
            theme={theme} 
          />
        </SectionCard>
        
        <SectionCard title="Teams" styles={styles}>
          <DropdownRow 
            value={getDisplayText(selectedTeams, 'Team')} 
            placeholder="Select Team" 
            onPress={() => openModal('teams')}
            styles={styles} 
            theme={theme} 
          />
        </SectionCard>
        
        <SectionCard title="Match Officials" styles={styles}>
          <DropdownRow 
            value={getDisplayText(selectedOfficials, 'Official')} 
            placeholder="Select Official" 
            onPress={() => openModal('officials')}
            styles={styles} 
            theme={theme} 
          />
        </SectionCard>
        
        <SectionCard title="Official role" styles={styles}>
          <ToggleRow label="Referee" value={roles.referee} onValueChange={() => setRoles(s => ({ ...s, referee: !s.referee }))} icon={<View style={styles.iconWrapper}><RefereeIcon color={theme.primary} /></View>} styles={styles} theme={theme} isDarkMode={isDarkMode} />
          <View style={styles.divider} />
          <ToggleRow label="Assistant" value={roles.assistant} onValueChange={() => setRoles(s => ({ ...s, assistant: !s.assistant }))} icon={<View style={styles.iconWrapper}><AssistantIcon color={theme.primary} /></View>} styles={styles} theme={theme} isDarkMode={isDarkMode} />
          <View style={styles.divider} />
          {/* --- CORRECTED: Pass theme to FourthOfficialIcon --- */}
          <ToggleRow label="4th Official" value={roles.fourthOfficial} onValueChange={() => setRoles(s => ({ ...s, fourthOfficial: !s.fourthOfficial }))} icon={<View style={styles.iconWrapper}><FourthOfficialIcon theme={theme} /></View>} styles={styles} theme={theme} isDarkMode={isDarkMode} />
          <View style={styles.divider} />
          <ToggleRow label="Observer" value={roles.observer} onValueChange={() => setRoles(s => ({ ...s, observer: !s.observer }))} icon={<MaterialCommunityIcons name="account-supervisor" size={22} color={theme.text} style={styles.icon} />} styles={styles} theme={theme} isDarkMode={isDarkMode} />
        </SectionCard>

        <SectionCard title="Show previous matches with" styles={styles}>
          <NumericStepperRow label="Yellow Cards" value={counters.yellowCards} onDecrement={createCounterHandler('yellowCards', -1)} onIncrement={createCounterHandler('yellowCards', 1)} styles={styles} theme={theme} />
          <View style={styles.divider} />
          <NumericStepperRow label="Red Cards" value={counters.redCards} onDecrement={createCounterHandler('redCards', -1)} onIncrement={createCounterHandler('redCards', 1)} styles={styles} theme={theme} />
          <View style={styles.divider} />
          <NumericStepperRow label="Goals" value={counters.goals} onDecrement={createCounterHandler('goals', -1)} onIncrement={createCounterHandler('goals', 1)} styles={styles} theme={theme} />
          <View style={styles.divider} />
          <ToggleRow label="Extra time" value={toggles.extraTime} onValueChange={() => setToggles(s => ({...s, extraTime: !s.extraTime}))} styles={styles} theme={theme} isDarkMode={isDarkMode} />
          <View style={styles.divider} />
          <ToggleRow label="PK" value={toggles.PK} onValueChange={() => setToggles(s => ({...s, PK: !s.PK}))} styles={styles} theme={theme} isDarkMode={isDarkMode} />
          <View style={styles.divider} />
          <ToggleRow label="Abandoned" value={toggles.abandoned} onValueChange={() => setToggles(s => ({...s, abandoned: !s.abandoned}))} styles={styles} theme={theme} isDarkMode={isDarkMode} />
        </SectionCard>
      </ScrollView>

      {/* Modal Pickers */}
      <ModalPicker
        visible={modalStates.competitions}
        title="Select Competitions"
        options={filterOptions.competitions}
        selectedOptions={selectedCompetitions}
        onSelectionChange={setSelectedCompetitions}
        onClose={() => closeModal('competitions')}
        multiSelect={true}
      />

      <ModalPicker
        visible={modalStates.venues}
        title="Select Venues"
        options={filterOptions.venues}
        selectedOptions={selectedVenues}
        onSelectionChange={setSelectedVenues}
        onClose={() => closeModal('venues')}
        multiSelect={true}
      />

      <ModalPicker
        visible={modalStates.teams}
        title="Select Teams"
        options={filterOptions.teams}
        selectedOptions={selectedTeams}
        onSelectionChange={setSelectedTeams}
        onClose={() => closeModal('teams')}
        multiSelect={true}
      />

      <ModalPicker
        visible={modalStates.officials}
        title="Select Match Officials"
        options={filterOptions.matchOfficials}
        selectedOptions={selectedOfficials}
        onSelectionChange={setSelectedOfficials}
        onClose={() => closeModal('officials')}
        multiSelect={true}
      />
    </SafeAreaView>
  );
};

// --- DYNAMIC STYLESHEETS ---
const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  container: { flex: 1, backgroundColor: theme.card, }, 
  scrollContainer: { padding: 12, paddingBottom: 30, backgroundColor: theme.background, },
  headerActionText: { color: theme.primary, fontSize: 15, fontWeight: '600', paddingHorizontal: 4, },
  card: { 
    backgroundColor: theme.card, 
    borderRadius: 10, 
    paddingHorizontal: 12, 
    paddingVertical: 8, 
    marginBottom: 12, 
    shadowColor: isDarkMode ? 'transparent' : "#000", 
    shadowOffset: { width: 0, height: 1 }, 
    shadowOpacity: isDarkMode ? 0 : 0.08, 
    shadowRadius: 1.5, 
    elevation: isDarkMode ? 0 : 2, 
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
  },
  cardTitle: { color: theme.text, fontSize: 15, fontWeight: 'bold', marginBottom: 6, },
  row: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', minHeight: 46, },
  labelContainer: { flexDirection: 'row', alignItems: 'center', },
  label: { color: theme.text, fontSize: 13, fontWeight: '500', marginLeft: 12, },
  valueContainer: { flexDirection: 'row', alignItems: 'center', flex: 1, justifyContent: 'flex-end', },
  value: { color: theme.text, opacity: 0.7, fontSize: 13, marginRight: 5, },
  placeholder: { color: '#A0A0A0', },
  divider: { height: 1, backgroundColor: theme.border, marginVertical: 4, },
  stepperContainer: { flexDirection: 'row', alignItems: 'center', },
  stepperButton: { width: 28, height: 28, borderRadius: 14, justifyContent: 'center', alignItems: 'center', marginHorizontal: 8, borderWidth: 1, borderColor: theme.border, },
  stepperValue: { color: theme.text, fontSize: 14, fontWeight: '600', minWidth: 25, textAlign: 'center', },
  icon: { marginRight: 12, },
  iconWrapper: { width: 24, height: 24, justifyContent: 'center', alignItems: 'center' },
});

export default FilterMatches;