import React, { useState, useRef, useEffect, ReactNode } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Dimensions,
  Platform,
  UIManager,
  LayoutChangeEvent,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import Svg, { Path, Defs, ClipPath, G, Rect, Circle, Ellipse, Text as SvgText } from 'react-native-svg';
import Header from '../components/Header';
import { useTheme } from '../contexts/ThemeContext'; // Ensure this path is correct
import MatchService from '../api/matchService'; // Add this import
import { SquadService } from '../api/squadService'; // Add squad service import
import { SoccerBallIcon } from '../components/icons';
import EvaluationService from '../api/evaluationService';

// --- Setup and Icons ---
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}
const { width: SCREEN_WIDTH } = Dimensions.get('window');

// --- SVG ICONS ---
const RefereeIcon = ({ color }: { color: string }) => ( <Svg height="32" width="32" viewBox="2 2 24 24"><Path d="M12.375 21.375C10.5 21.375 8.90625 20.7188 7.59375 19.4062C6.28125 18.0938 5.625 16.5 5.625 14.625C5.625 14.4187 5.63437 14.2125 5.65312 14.0062C5.67188 13.8 5.7 13.5938 5.7375 13.3875C5.64375 13.425 5.53125 13.4531 5.4 13.4719C5.26875 13.4906 5.15625 13.5 5.0625 13.5C4.275 13.5 3.60938 13.2281 3.06562 12.6844C2.52187 12.1406 2.25 11.475 2.25 10.6875C2.25 9.9 2.50781 9.23438 3.02344 8.69062C3.53906 8.14687 4.19062 7.875 4.97812 7.875C5.59687 7.875 6.15469 8.04844 6.65156 8.39531C7.14844 8.74219 7.5 9.1875 7.70625 9.73125C8.325 9.16875 9.03281 8.71875 9.82969 8.38125C10.6266 8.04375 11.475 7.875 12.375 7.875H24.75V12.375H19.125V14.625C19.125 16.5 18.4688 18.0938 17.1562 19.4062C15.8438 20.7188 14.25 21.375 12.375 21.375ZM5.0625 11.8125C5.38125 11.8125 5.64844 11.7047 5.86406 11.4891C6.07969 11.2734 6.1875 11.0062 6.1875 10.6875C6.1875 10.3687 6.07969 10.1016 5.86406 9.88594C5.64844 9.67031 5.38125 9.5625 5.0625 9.5625C4.74375 9.5625 4.47656 9.67031 4.26094 9.88594C4.04531 10.1016 3.9375 10.3687 3.9375 10.6875C3.9375 11.0062 4.04531 11.2734 4.26094 11.4891C4.47656 11.7047 4.74375 11.8125 5.0625 11.8125ZM12.375 18.5625C13.4625 18.5625 14.3906 18.1781 15.1594 17.4094C15.9281 16.6406 16.3125 15.7125 16.3125 14.625C16.3125 13.5375 15.9281 12.6094 15.1594 11.8406C14.3906 11.0719 13.4625 10.6875 12.375 10.6875C11.2875 10.6875 10.3594 11.0719 9.59062 11.8406C8.82187 12.6094 8.4375 13.5375 8.4375 14.625C8.4375 15.7125 8.82187 16.6406 9.59062 17.4094C10.3594 18.1781 11.2875 18.5625 12.375 18.5625Z" fill={color}/></Svg>);
const AssistantIcon = ({ color }: { color: string }) => ( <Svg height="28" width="28" viewBox="0 0 24 24"><Path d="M9 6H11V4H9V6ZM13 6V4H15V6H13ZM9 14V12H11V14H9ZM17 10V8H19V10H17ZM17 14V12H19V14H17ZM13 14V12H15V14H13ZM17 6V4H19V6H17ZM11 8V6H13V8H11ZM5 20V4H7V6H9V8H7V10H9V12H7V20H5ZM15 12V10H17V12H15ZM11 12V10H13V12H11ZM9 10V8H11V10H9ZM13 10V8H15V10H13ZM15 8V6H17V8H15Z" fill={color}/></Svg>);
const FourthOfficialIcon = ({ color }: { color: string }) => ( 
  <View style={{ width: 28, height: 28, borderRadius: 6, borderWidth: 1.5, borderColor: color, justifyContent: 'center', alignItems: 'center' }}>
    <Text style={{ fontSize: 11, fontWeight: '600', color: color }}>4th</Text>
  </View>
);
interface StripedTshirtProps { baseColor: string; stripeColor: string; size?: number; }
const StripedTshirt: React.FC<StripedTshirtProps> = ({ baseColor, stripeColor, size = 28 }) => ( <Svg width={size} height={size} viewBox="-1 0 19 19"><Defs><ClipPath id="matchDetailTshirtClip"><Path d="m15.867 7.593-1.534.967a.544.544 0 0 1-.698-.118l-.762-.957v7.256a.476.476 0 0 1-.475.475h-7.79a.476.476 0 0 1-.475-.475V7.477l-.769.965a.544.544 0 0 1-.697.118l-1.535-.967a.387.387 0 0 1-.083-.607l2.245-2.492a2.814 2.814 0 0 1 2.092-.932h.935a2.374 2.374 0 0 0 4.364 0h.934a2.816 2.816 0 0 1 2.093.933l2.24 2.49a.388.388 0 0 1-.085.608z" /></ClipPath></Defs><Path d="m15.867 7.593-1.534.967a.544.544 0 0 1-.698-.118l-.762-.957v7.256a.476.476 0 0 1-.475.475h-7.79a.476.476 0 0 1-.475-.475V7.477l-.769.965a.544.544 0 0 1-.697.118l-1.535-.967a.387.387 0 0 1-.083-.607l2.245-2.492a2.814 2.814 0 0 1 2.092-.932h.935a2.374 2.374 0 0 0 4.364 0h.934a2.816 2.816 0 0 1 2.093.933l2.24 2.49a.388.388 0 0 1-.085.608z" fill={baseColor} /><G clipPath="url(#matchDetailTshirtClip)">{[3.5, 6, 8.5, 11, 13.5].map(x => ( <Rect key={x} x={x} y="3" width="1.2" height="16" fill={stripeColor} /> ))}</G></Svg>);
const ShortsIcon = ({ color = "#000", size = 20 }) => ( <Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Path fill={color} d="M22,2H2A1,1,0,0,0,1,3V21a1,1,0,0,0,1,1H8a1,1,0,0,0,.868-.5L12,16.016l3.132,5.48A1,1,0,0,0,16,22h6a1,1,0,0,0,1-1V3A1,1,0,0,0,22,2Zm-1,9.9A5.013,5.013,0,0,1,17.1,8H21ZM21,6H13V4h8ZM3,4h8V6H3ZM3,8H6.9A5.013,5.013,0,0,1,3,11.9ZM16.58,20l-3.712-6.5a1.04,1.04,0,0,0-1.736,0L7.42,20H3V13.92A7,7,0,0,0,8.92,8H11V9a1,1,0,0,0,2,0V8h2.08A7,7,0,0,0,21,13.92V20Z" /></Svg> );
const VarIcon = ({ color, size = 24 }: { color: string; size?: number }) => <Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7z" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" /><Path d="M12 15a3 3 0 100-6 3 3 0 000 6z" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" /></Svg>;
const UserIcon = ({ color, size = 20 }: { color: string; size?: number }) => <Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Path d="M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /><Circle cx="12" cy="7" r="4" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /></Svg>;
const GoalkeeperListIcon = ({color}: {color: string}) => ( <View style={{ width: 18, height: 18, justifyContent: 'center', alignItems: 'center' }}><Svg width="100%" height="100%" viewBox="0 0 84 122" fill="none"><Path d="M44.7721 88.6582C43.8158 88.6582 42.9766 87.9385 42.8273 86.9326L38.0228 54.4824C37.8589 53.3734 38.5974 52.3371 39.6722 52.1672C40.7474 51.9981 41.7521 52.7593 41.9167 53.8692L46.7213 86.3194C46.8851 87.4288 46.1466 88.4655 45.0718 88.6346C44.9714 88.65 44.8706 88.6582 44.7721 88.6582Z" fill={color}/><Path d="M61.108 88.658C61.0092 88.658 60.9091 88.6499 60.8079 88.634C59.7331 88.465 58.9947 87.4283 59.1585 86.3188L63.963 53.8686C64.1277 52.7591 65.1308 51.9963 66.2076 52.1666C67.282 52.3365 68.0204 53.3732 67.857 54.4818L63.0525 86.9321C62.9036 87.9379 62.0643 88.658 61.108 88.658Z" fill={color}/><Path d="M52.9399 88.658C51.8525 88.658 50.9707 87.7485 50.9707 86.6261V54.1758C50.9707 53.0534 51.8525 52.1439 52.9399 52.1439C54.0273 52.1439 54.9092 53.0534 54.9092 54.1758V86.6261C54.9092 87.7485 54.0273 88.658 52.9399 88.658Z" fill={color}/><Path d="M81.8674 24.4976C80.52 22.8261 78.528 21.8678 76.4004 21.8678C69.8744 21.8678 69.2548 28.3693 69.2308 28.5152C69.3683 24.9044 69.551 20.6097 69.7909 15.5058C69.9157 12.8427 69.1312 10.4519 67.5818 8.77355C66.238 7.31786 64.3786 6.48273 62.4818 6.48273C59.2373 6.48273 55.7798 8.93285 55.3713 14.4057C55.1811 16.955 54.994 19.7705 54.7987 22.7477C54.714 18.8435 54.5541 7.99694 54.5541 7.99694C54.4816 2.74638 50.8008 0 47.2014 0C43.8765 0 40.3945 2.39851 40.174 6.9834C40.0854 8.82028 40.0215 12.0608 39.9479 15.8138C39.901 18.2262 39.842 21.2111 39.7608 24.3196C39.2902 19.8119 38.8463 15.479 38.5616 12.4786C37.983 6.37422 34.2568 3.64206 30.8205 3.64206C28.9292 3.64206 27.0833 4.45241 25.7564 5.86624C24.311 7.40605 23.5548 9.55788 23.6281 11.9247C23.6675 13.193 25.0211 53.9962 24.8218 62.6519C23.7596 61.5278 16.0335 52.6685 12.7244 49.1862C11.3062 47.6939 9.28534 46.8376 7.18023 46.8376C4.48159 46.8376 2.13387 48.1759 0.900346 50.4175C0.00355629 52.0472 -1.02439 55.6075 2.17326 61.1401C6.79938 69.1448 21.9755 94.7729 21.9755 94.7729C27.3385 103.838 25.1987 119.439 25.1767 119.594C25.0932 120.179 25.2602 120.772 25.6339 121.218C26.0081 121.666 26.5524 121.923 27.1246 121.923H72.5159C73.0629 121.923 73.5848 121.688 73.9574 121.275C74.33 120.862 74.5194 120.308 74.48 119.745L72.6955 94.1837C73.6541 88.3402 80.1928 51.9614 82.6767 38.1425L83.5133 33.4881C84.3557 28.7895 83.0832 26.0049 81.8674 24.4976ZM29.3258 117.859C29.3936 117.071 29.4609 116.11 29.5074 115.02H70.2016L70.3997 117.859H29.3258ZM79.6402 32.7493C79.6402 32.7493 69.5833 88.7019 68.7716 93.7553C68.7464 93.9134 69.9177 110.956 69.9177 110.956H29.5563C29.4515 105.293 28.5653 98.1123 25.3366 92.6553C25.3366 92.6553 10.1703 67.0446 5.55483 59.059C3.96565 56.3086 3.51706 53.8909 4.32405 52.4251C4.84905 51.4717 5.91638 50.902 7.17944 50.902C8.23338 50.902 9.22942 51.314 9.91196 52.0321C13.162 55.4515 15.915 58.6091 18.1272 61.1458C22.914 66.6353 24.3716 68.217 26.1211 68.217C26.9557 68.217 27.7603 67.8382 28.1498 67.0685C29.5696 64.2632 27.6024 13.0622 27.5642 11.7963C27.5244 10.5198 27.8773 9.44775 28.5846 8.6943C29.1742 8.06562 29.9887 7.70596 30.8201 7.70596C32.4305 7.70596 34.0268 9.09825 34.642 12.874C36.2973 23.0313 37.8259 43.4947 37.8743 44.077C37.9621 45.1288 38.816 45.9176 39.8372 45.9176C41.0011 45.9176 41.7663 45.4356 42.0928 44.6643C43.285 41.837 44.0215 8.96211 44.1065 7.18497C44.2176 4.88155 45.8426 4.0639 47.2006 4.0639C48.7504 4.0639 50.5747 5.10832 50.6149 8.05383C50.6149 8.05383 51.5967 44.7037 52.4183 46.3776C52.6069 46.7616 52.9086 47.0746 53.2808 47.2696C53.6506 47.4647 54.0642 47.5387 54.5384 47.5163C55.213 47.5155 55.8463 47.1575 56.2079 46.5585C57.1484 45.0004 57.6257 39.7774 58.6753 23.8047C58.8896 20.5471 59.0916 17.4707 59.2972 14.7174C59.5268 11.6427 61.0924 10.5462 62.4807 10.5462C63.3054 10.5462 64.1246 10.9217 64.7291 11.5764C65.5401 12.4542 65.9296 13.7449 65.8559 15.3087C64.3916 46.4634 65.0104 47.8215 65.42 48.7209C65.7488 49.4422 66.4707 49.8884 67.2013 49.8884C67.2021 49.8884 67.2029 49.8884 67.2037 49.8884C67.2765 49.8957 68.6861 49.9502 69.2887 48.8806C70.1528 47.3464 72.9743 30.055 73.1862 28.7427C73.4993 26.8006 75.0259 25.9313 76.3996 25.9313C76.9601 25.9313 78.0223 26.0829 78.8387 27.0956C79.7969 28.2835 80.0809 30.2911 79.6402 32.7493Z" fill={color}/></Svg></View> );
const TABS = [ { key: 'overview', label: 'Overview' }, { key: 'team', label: 'Team' }, { key: 'match', label: 'Match' }, { key: 'stats', label: 'Stats' }, { key: 'earnings', label: 'Earnings' }, { key: 'evaluate', label: 'Evaluate' },{ key: 'notes', label: 'Notes' } ];
const CaptainBadge = ({styles}: {styles: any}) => ( <View style={styles.captainBadge}><Text style={styles.captainBadgeText}>C</Text></View> );

// --- NEW SUBSTITUTION ICON ---
const SubstitutionIcon = ({ direction }: { direction: 'in' | 'out' }) => {
    const isSubIn = direction === 'in';
    const circleColor = isSubIn ? '#28A745' : '#DC3545'; // Green for 'in', Red for 'out'
    const arrowPath = isSubIn ? "M12 15V9 M9 12L12 9L15 12" : "M12 9V15 M9 12L12 15L15 12";
  
    return (
      <View style={{ width: 18, height: 18, justifyContent: 'center', alignItems: 'center', marginRight: 8 }}>
        <Svg height="100%" width="100%" viewBox="0 0 24 24">
          <Circle cx="12" cy="12" r="11" fill={circleColor} />
          <Path
              d={arrowPath}
              stroke="black"
              strokeWidth="2.5"
              strokeLinecap="round"
              strokeLinejoin="round"
              fill="none"
          />
        </Svg>
      </View>
    );
};
// --- Content Components ---
const OverviewContent = ({ overviewStyles, styles, theme, matchData }: { overviewStyles: any, styles: any, theme: any, matchData: any }) => {
  const DetailRow = ({ label, value }: { label: string, value: string }) => (
    <View style={overviewStyles.detailRow}>
      <Text style={overviewStyles.detailLabel}>{label}</Text>
      <View style={overviewStyles.valueContainer}>
        <Text style={overviewStyles.detailValue}>{value}</Text>
      </View>
    </View>
  );

  const OfficialRow = ({ icon, label, name }: { icon: ReactNode, label?: string, name: string }) => (
    <View style={overviewStyles.officialRow}>
      <View style={overviewStyles.iconContainer}>
        {icon}
        {label && <Text style={overviewStyles.iconLabel}>{label}</Text>}
      </View>
      <Text style={overviewStyles.officialName}>{name}</Text>
    </View>
  );

  const getOfficialIcon = (role: string) => {
    switch (role.toLowerCase()) {
      case 'referee':
        return <RefereeIcon color={theme.primary} />;
      case 'ar1':
      case 'ar 1':
      case 'assistant referee 1':
      case 'assistant 1':
        return <AssistantIcon color={theme.primary} />;
      case 'ar2':
      case 'ar 2':
      case 'assistant referee 2':
      case 'assistant 2':
        return <AssistantIcon color={theme.primary} />;
      case '4th':
      case 'fourth':
      case 'fourth official':
      case '4th official':
        return <FourthOfficialIcon color={theme.text} />;
      case 'var':
      case 'video assistant referee':
        return <VarIcon color={theme.primary} />;
      case 'avar':
      case 'assistant video assistant referee':
        return <VarIcon color={theme.primary} />;
      case 'observer':
      case 'match observer':
        return <UserIcon color={theme.primary} />;
      default:
        return <UserIcon color={theme.primary} />;
    }
  };

  return (
    <View>
      <View style={styles.card}>
        <Text style={styles.cardTitle}>Match Details</Text>
        <DetailRow label="Played on" value={matchData?.matchDate ? new Date(matchData.matchDate).toLocaleString() : 'N/A'} />
        <DetailRow label="Competition" value={matchData?.competition || matchData?.league || 'N/A'} />
        <DetailRow label="Venue" value={matchData?.venue || 'N/A'} />
        <DetailRow label="Official Role" value={matchData?.officialRole || 'Referee'} />
      </View>

      {/* Match Officials Section */}
      {matchData?.matchOfficials && matchData.matchOfficials.length > 0 && (
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Match Officials</Text>
          {matchData.matchOfficials.map((official: any, index: number) => {
            const icon = getOfficialIcon(official.role || '');
            const role = official.role || '';
            const roleLowerCase = role.toLowerCase();
            const isReferee = roleLowerCase === 'referee';
            const isFourthOfficial = roleLowerCase.includes('4th') || roleLowerCase.includes('fourth');
            const label = !isReferee && !isFourthOfficial ? role : undefined;
            
            return (
              <OfficialRow 
                key={index}
                icon={icon} 
                label={label} 
                name={official.name || 'N/A'}
              />
            );
          })}
        </View>
      )}

      {/* Home Team Officials */}
      {matchData?.homeTeam?.officials && matchData.homeTeam.officials.length > 0 && (
        <View style={styles.card}>
          <Text style={styles.cardTitle}>{matchData.homeTeam.name || 'Home'} Team Officials</Text>
          {matchData.homeTeam.officials.map((official: any, index: number) => (
            <View key={index} style={overviewStyles.detailRow}>
              <Text style={overviewStyles.detailLabel}>{official.role}</Text>
              <View style={overviewStyles.valueContainer}>
                <Text style={overviewStyles.detailValue}>{official.name}</Text>
              </View>
            </View>
          ))}
        </View>
      )}

      {/* Away Team Officials */}
      {matchData?.awayTeam?.officials && matchData.awayTeam.officials.length > 0 && (
        <View style={styles.card}>
          <Text style={styles.cardTitle}>{matchData.awayTeam.name || 'Away'} Team Officials</Text>
          {matchData.awayTeam.officials.map((official: any, index: number) => (
            <View key={index} style={overviewStyles.detailRow}>
              <Text style={overviewStyles.detailLabel}>{official.role}</Text>
              <View style={overviewStyles.valueContainer}>
                <Text style={overviewStyles.detailValue}>{official.name}</Text>
              </View>
            </View>
          ))}
        </View>
      )}
    </View>
  );
};

// --- TEAM SECTION COMPONENTS ---
type CardEventData = { type: 'red' | 'yellow'; details: string };
type SubstitutionEventData = { type: 'sub_in' | 'sub_out'; details: string };
type GoalEventData = { type: 'goal'; details: string };
type PlayerEvent = CardEventData | SubstitutionEventData | GoalEventData;
interface CardEventProps { color: string; text: string; styles: any; }
const CardEvent: React.FC<CardEventProps> = ({ color, text, styles }) => ( <View style={styles.eventContainer}><View style={[styles.cardEventIcon, { backgroundColor: color }]} /><Text style={styles.eventText}>{text}</Text></View> );
interface SubstitutionEventProps { type: 'sub_in' | 'sub_out'; details: string; style?: object; styles: any; }
const SubstitutionEvent: React.FC<SubstitutionEventProps> = ({ type, details, style, styles }) => { const isSubIn = type === 'sub_in'; return ( <View style={[styles.eventContainer, style]}><MaterialIcons name={isSubIn ? "arrow-forward" : "arrow-back"} size={16} color={isSubIn ? 'green' : 'red'} style={styles.substitutionArrow} /><Text style={styles.eventText}>{details}</Text></View> );};
interface PlayerRowProps { number: number; name: string; isGoalkeeper?: boolean; isCaptain?: boolean; events?: PlayerEvent[]; styles: any; theme: any; }
const PlayerRow: React.FC<PlayerRowProps> = ({ number, name, isGoalkeeper, isCaptain, events, styles, theme }) => {
    const substitutionEvent = events?.find(e => e.type === 'sub_in' || e.type === 'sub_out') as SubstitutionEventData | undefined;
    const cardEvents = events?.filter(e => e.type === 'red' || e.type === 'yellow') as CardEventData[] | undefined;
    const goalEvents = events?.filter(e => e.type === 'goal') as GoalEventData[] | undefined;
    let subTime = ''; let subPlayerDetails = '';
    if (substitutionEvent) { const firstSpaceIndex = substitutionEvent.details.indexOf(' '); if (firstSpaceIndex !== -1) { subTime = substitutionEvent.details.substring(0, firstSpaceIndex); subPlayerDetails = substitutionEvent.details.substring(firstSpaceIndex + 1); } }
    return ( <TouchableOpacity style={styles.playerRow} activeOpacity={0.7}><View style={styles.playerInfo}><View style={styles.playerNameContainer}><View style={{ flexDirection: 'row', alignItems: 'center', flexShrink: 1 }}><Text style={styles.rowNumber}>{number}.</Text><Text style={styles.rowName} numberOfLines={1}>{name}</Text></View>{substitutionEvent && ( <SubstitutionEvent type={substitutionEvent.type} details={subTime} styles={styles}/> )}</View>{substitutionEvent && ( <SubstitutionEvent type={substitutionEvent.type === 'sub_out' ? 'sub_in' : 'sub_out'} details={subPlayerDetails} style={styles.substitutionEventRow} styles={styles}/> )}</View><View style={styles.playerIcons}>{goalEvents?.map((event, index) => <View key={`goal-${index}`} style={{marginRight: 4}}><SoccerBallIcon color={theme.text} size={16} /></View>)}{cardEvents?.map((event, index) => ( <CardEvent key={`card-${index}`} color={event.type === 'red' ? '#DC143C' : '#FFD700'} text={event.details} styles={styles}/> ))}{isCaptain && <CaptainBadge styles={styles}/>}{isGoalkeeper && <GoalkeeperListIcon color={theme.text}/>}</View></TouchableOpacity> );
};
interface TeamOfficialRowProps { role: string; name: string; styles: any; }
const TeamOfficialRowDisplay: React.FC<TeamOfficialRowProps> = ({ role, name, styles }) => ( <TouchableOpacity style={styles.playerRow} activeOpacity={0.7}><Text style={styles.rowRole}>{role}</Text><Text style={styles.rowName}>{name}</Text></TouchableOpacity> );
const TeamContent = ({ team, teamType, matchLog, styles, theme }: { team: any, teamType: 'home' | 'away', matchLog: any[], styles: any, theme: any }) => {
  if (!team?.squad?.players) {
    return <View style={styles.card}><Text style={styles.cardTitle}>Squad not available</Text></View>;
  }

  const starters = team.squad.players.filter((p: any) => p.position === 'STARTER');
  const substitutes = team.squad.players.filter((p: any) => p.position === 'SUBSTITUTE');
  const officials = team.squad.officials || [];

  const getPlayerEvents = (playerNumber: number) => {
    const playerEvents: PlayerEvent[] = [];

    matchLog.forEach(event => {
      if (event.data?.team === teamType) {
        if (event.type === 'goal' && event.data.playerNumber === playerNumber) {
          playerEvents.push({ type: 'goal', details: `${event.data.minute}'` });
        }
        if (event.type === 'card' && event.data.playerNumber === playerNumber) {
          playerEvents.push({ type: event.data.cardType, details: `${event.data.minute}' (${event.data.code})` });
        }
        if (event.type === 'substitution') {
          if (event.data.playerOutNumber === playerNumber) {
            playerEvents.push({ type: 'sub_out', details: `${event.data.minute}' ${event.data.playerInName} #${event.data.playerInNumber}` });
          }
          if (event.data.playerInNumber === playerNumber) {
            playerEvents.push({ type: 'sub_in', details: `${event.data.minute}' ${event.data.playerOutName} #${event.data.playerOutNumber}` });
          }
        }
      }
    });
    return playerEvents;
  };

  return (
    <View>
      <View style={styles.teamCard}>
        <Text style={styles.teamCardTitle}>Starting Line Up</Text>
        {starters.map((player: any) => (
          <PlayerRow
            key={player.id}
            number={player.number}
            name={player.name}
            isGoalkeeper={player.isGoalkeeper}
            isCaptain={player.isCaptain}
            events={getPlayerEvents(player.number)}
            styles={styles}
            theme={theme}
          />
        ))}
      </View>
      <View style={styles.teamCard}>
        <Text style={styles.teamCardTitle}>Substitutes</Text>
        {substitutes.map((player: any) => (
          <PlayerRow
            key={player.id}
            number={player.number}
            name={player.name}
            isGoalkeeper={player.isGoalkeeper}
            isCaptain={player.isCaptain}
            events={getPlayerEvents(player.number)}
            styles={styles}
            theme={theme}
          />
        ))}
      </View>
      <View style={styles.teamCard}>
        <Text style={styles.teamCardTitle}>Team Officials</Text>
        {officials.map((official: any) => (
          <TeamOfficialRowDisplay key={official.id} {...official} styles={styles} />
        ))}
      </View>
    </View>
  );
};

// --- OTHER CONTENT COMPONENTS ---
const EarningsContent = ({ overviewStyles, styles, theme, matchData }: { overviewStyles: any, styles: any, theme: any, matchData: any }) => {
  const DetailRow = ({ label, value }: { label: string, value: string }) => (
    <View style={overviewStyles.detailRow}>
      <Text style={overviewStyles.detailLabel}>{label}</Text>
      <View style={overviewStyles.valueContainer}>
        <Text style={overviewStyles.detailValue}>{value}</Text>
      </View>
    </View>
  );

  return (
    <View style={styles.card}>
      <Text style={styles.cardTitle}>Earnings</Text>
      {matchData?.earnings?.fees ? <DetailRow label="Fees" value={matchData.earnings.fees} /> : null}
      {matchData?.earnings?.expenses ? <DetailRow label="Expenses" value={matchData.earnings.expenses} /> : null}
      {matchData?.earnings?.travelTime ? <DetailRow label="Travel Time" value={matchData.earnings.travelTime} /> : null}
      {matchData?.earnings?.mileage ? <DetailRow label="Travel Mileage" value={matchData.earnings.mileage} /> : null}
      {!(matchData?.earnings?.fees || matchData?.earnings?.expenses || matchData?.earnings?.travelTime || matchData?.earnings?.mileage) && (
        <Text style={{paddingHorizontal: 16, paddingBottom: 12, color: theme.textSecondary}}>No earnings information recorded for this match.</Text>
      )}
    </View>
  );
};
const NotesContent = ({ overviewStyles, styles, theme, matchData }: { overviewStyles: any, styles: any, theme: any, matchData: any }) => {
  return (
    <View style={styles.card}>
      <Text style={styles.cardTitle}>Notes</Text>
      {matchData?.notes ? (
        <Text style={overviewStyles.notesText}>{matchData.notes}</Text>
      ) : (
        <Text style={{paddingHorizontal: 16, paddingBottom: 12, color: theme.textSecondary}}>No notes recorded for this match.</Text>
      )}
    </View>
  );
};
interface LogEvent { 
  type: 'end' | 'time_marker' | 'red_card' | 'yellow_card' | 'substitution' | 'goal' | 'period_start' | 'halftime_start' | 'halftime_end' | 'match_abandoned' | 'penalty_shot' | 'penalty_start' | 'penalty_end'; 
  time: string; 
  realTime?: string; 
  score?: string; 
  period?: string;
  kickoffTeam?: string;
  player?: { name: string; number: number; role: string; }; 
  playerIn?: { name: string; number: number; }; 
  playerOut?: { name: string; number: number; }; 
  team?: 'home' | 'away' | 'neutral';
  outcome?: 'goal' | 'miss' | 'save';
  round?: number;
  startingTeam?: string;
  winner?: string;
  homeScore?: number;
  awayScore?: number;
}
const logEvents: LogEvent[] = [ { type: 'end', time: "90'", realTime: '22:47', score: '4 - 1' }, { type: 'time_marker', time: "66'" }, { type: 'red_card', time: "35'", player: { name: 'Dave william', number: 9, role: 'R4' } }, { type: 'substitution', time: "21'", playerIn: { name: 'Dave william', number: 9 }, playerOut: { name: 'Joe bob', number: 43 } } ];

const MatchLogContent = ({ styles, theme, matchId, matchInfo, initialLogs, onDetectedEnd }: { styles: any; theme: any; matchId?: string; matchInfo?: any, initialLogs?: any[], onDetectedEnd?: () => void }) => {
    const [activeLog, setActiveLog] = useState('master');
    const [logEvents, setLogEvents] = useState<LogEvent[]>([]);
    const [loading, setLoading] = useState(true);
    const navigation = useNavigation();
  
    const convertApiLogToUi = (log: any): LogEvent | null => {
      switch (log.type as 'match_completed' | 'card' | 'substitution' | 'time_marker' | 'period_end' | 'goal' | 'penalty_shot' | 'penalty_start' | 'penalty_end' | string) {
                case 'match_completed':
                  return {
                    type: 'end',
            time: `${Math.floor((log.timestamp ?? 0) / 60)}'`,
            realTime: new Date(log.realTime ?? new Date()).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                    score: log.data?.finalScore || '0 - 0',
                    team: 'neutral'
                  };
        case 'period_start':
          return {
            type: 'period_start',
            time: `${Math.floor((log.timestamp ?? 0) / 60)}'`,
            realTime: new Date(log.realTime ?? new Date()).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            period: log.data?.periodName || `${log.data?.period ?? ''}`,
            kickoffTeam: log.data?.teamName || '',
            team: 'neutral'
          };
        case 'card': {
          const isRed = log.data?.cardType === 'red';
          const isYellow = log.data?.cardType === 'yellow';
          const displayTime = log.data?.isPenaltyPhase ? log.data.time : `${Math.floor((log.timestamp ?? 0) / 60)}'`;
          
          if (isRed) {
            return {
              type: 'red_card',
              time: displayTime,
              player: {
                name: log.data?.playerName || 'Unknown',
                number: log.data?.playerNumber || 0,
                role: log.data?.code || ''
              },
              team: (log.data?.team === 'home' || log.data?.team === 'away') ? log.data.team : 'neutral'
            };
          } else if (isYellow) {
                  return {
              type: 'yellow_card',
              time: displayTime,
                    player: {
                      name: log.data?.playerName || 'Unknown',
                      number: log.data?.playerNumber || 0,
                      role: log.data?.code || ''
                    },
                    team: (log.data?.team === 'home' || log.data?.team === 'away') ? log.data.team : 'neutral'
                  };
          }
          return {
            type: 'time_marker',
            time: displayTime,
            team: 'neutral'
          };
        }
                case 'substitution':
                  const subDisplayTime = log.data?.isPenaltyPhase ? log.data.time : `${Math.floor((log.timestamp ?? 0) / 60)}'`;
                  return {
                    type: 'substitution',
                    time: subDisplayTime,
                    playerIn: {
                      name: log.data?.playerInName || 'Unknown',
                      number: log.data?.playerInNumber || 0
                    },
                    playerOut: {
                      name: log.data?.playerOutName || 'Unknown',
                      number: log.data?.playerOutNumber || 0
                    },
                    team: (log.data?.team === 'home' || log.data?.team === 'away') ? log.data.team : 'neutral'
                  };
        case 'goal':
          return {
            type: 'goal',
            time: `${Math.floor((log.timestamp ?? 0) / 60)}'`,
            player: {
              name: log.data?.playerName || 'Unknown',
              number: log.data?.playerNumber || 0,
              role: log.data?.team || ''
            },
            team: (log.data?.team === 'home' || log.data?.team === 'away') ? log.data.team : 'neutral'
          };
        case 'period_end':
          return {
            type: 'time_marker',
            time: `${Math.floor((log.timestamp ?? 0) / 60)}'`,
            realTime: new Date(log.realTime ?? new Date()).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            period: log.data?.periodName || 'Period',
            score: log.data?.score || '0 - 0',
            team: 'neutral'
          };
        case 'halftime_start':
          return {
            type: 'halftime_start',
            time: `${Math.floor((log.timestamp ?? 0) / 60)}'`,
            period: 'Half-time',
            team: 'neutral'
          };
        case 'halftime_end':
          // This event now correctly triggers the display of the 'Start of Period 2' card.
          return null;
        case 'match_abandoned':
          return {
            type: 'match_abandoned',
            time: `${Math.floor((log.timestamp ?? 0) / 60)}'`,
            realTime: new Date(log.realTime ?? new Date()).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            period: log.data?.reason || 'Match Abandoned',
            team: 'neutral'
          };
        case 'penalty_shot':
          return {
            type: 'penalty_shot',
            time: `R ${log.data?.round || 1}`,
            realTime: new Date(log.realTime ?? new Date()).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            player: {
              name: log.data?.playerName || 'Unknown',
              number: log.data?.player || 0,
              role: log.data?.outcome || ''
            },
            outcome: log.data?.outcome,
            round: log.data?.round,
            team: (log.data?.team === 'home' || log.data?.team === 'away') ? log.data.team : 'neutral'
          };
        case 'penalty_start':
          return {
            type: 'penalty_start',
            time: 'Penalty Shootout',
            realTime: new Date(log.realTime ?? new Date()).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            period: 'Penalty Shootout Started',
            startingTeam: log.data?.startingTeam,
            team: 'neutral'
          };
        case 'penalty_end':
          console.log('🔍 Converting penalty_end event:', log);
          const convertedEvent: LogEvent = {
            type: 'penalty_end',
            time: 'Final',
            realTime: new Date(log.realTime ?? new Date()).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            period: 'Penalty Shootout Complete',
            winner: log.data?.winner,
            homeScore: log.data?.homeScore,
            awayScore: log.data?.awayScore,
            score: `${log.data?.homeScore || 0} - ${log.data?.awayScore || 0}`,
            team: 'neutral'
          };
          console.log('✅ Converted penalty_end event:', convertedEvent);
          return convertedEvent;
                default:
                  return {
                    type: 'time_marker',
            time: `${Math.floor((log.timestamp ?? 0) / 60)}'`,
            team: 'neutral'
          };
      }
    };
  
    useEffect(() => {
      const processLogs = async () => {
        if (!matchId && !initialLogs?.length) {
          setLoading(false);
          return;
        }
        try {
          setLoading(true);
          let merged: LogEvent[] = [];
          if (initialLogs?.length) {
            merged = initialLogs.map(convertApiLogToUi).filter(Boolean) as LogEvent[];
          } else if (matchId) {
            const response = await MatchService.getMatchLogs(matchId);
            if (response.success && response.data) {
              console.log('📊 Raw logs from API:', response.data);
              merged = response.data.map(convertApiLogToUi).filter(Boolean) as LogEvent[];
              console.log('🔄 Converted logs:', merged);
            }
          }

          const hasAbandonment = merged.some(log => log.type === 'match_abandoned');

          const sortedLogs = merged.sort((a, b) => {
            const timeA = new Date(a.realTime || 0).getTime();
            const timeB = new Date(b.realTime || 0).getTime();
            return timeA - timeB;
          });

          if (hasAbandonment) {
            setLogEvents(sortedLogs.filter(log => log.type !== 'end'));
          } else {
            setLogEvents(sortedLogs);
          }
        } catch (error) {
          console.error('Error processing match logs:', error);
        } finally {
          setLoading(false);
        }
      };
      processLogs();
    }, [matchId, initialLogs]);
  
    useEffect(() => {
      if (logEvents.some(ev => ev.type === 'end')) {
        onDetectedEnd && onDetectedEnd();
      }
    }, [JSON.stringify(logEvents)]);
  
    if (loading) {
      return (
        <View style={styles.logContainer}>
          <Text>Loading match logs...</Text>
        </View>
      );
    }

    return (
      <View style={styles.logContainer}>
        <View style={styles.logToggleContainer}>
          <TouchableOpacity
            style={[styles.logToggleButton, activeLog === 'master' ? styles.activeLogToggle : {}]}
            onPress={() => setActiveLog('master')}>
            <Text style={[styles.logToggleText, activeLog === 'master' ? styles.activeLogToggleText : styles.inactiveLogToggleText]}>
              Master log
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.logToggleButton, activeLog === 'own' ? styles.activeLogToggle : {}]}
            onPress={() => setActiveLog('own')}>
            <Text style={[styles.logToggleText, activeLog === 'own' ? styles.activeLogToggleText : styles.inactiveLogToggleText]}>
              Own log
            </Text>
          </TouchableOpacity>
        </View>
        <View style={styles.timelineContainer}>
          {logEvents.map((event, index) => {
            const isLast = index === logEvents.length - 1;
            const side: 'left' | 'right' | 'center' = event.team === 'home' ? 'left' : event.team === 'away' ? 'right' : 'center';
            const eventContent = () => {
              switch (event.type) {
                case 'end':
                  return (
                    <View style={styles.endEventCard}>
                      <Text style={styles.endEventTime}>{event.time}</Text>
                      <Text style={styles.endEventStatus}>END</Text>
                      <Text style={styles.endEventRealTime}>{event.realTime}</Text>
                      <Text style={styles.endEventScore}>{event.score}</Text>
                    </View>
                  );
                case 'time_marker':
                  if (event.period) {
                    return (
                      <View style={styles.markerCard}>
                        <Text style={styles.markerCardTime}>{event.time}</Text>
                        <Text style={styles.markerCardText}>{`End of ${event.period}`}</Text>
                        <Text style={styles.markerCardRealTime}>{event.realTime}</Text>
                        <Text style={styles.markerCardScore}>{event.score}</Text>
                      </View>
                    );
                  }
                  return null;
                case 'period_start':
                  return (
                    <View style={styles.markerCard}>
                        <Text style={styles.markerCardTime}>{event.time}</Text>
                        <Text style={styles.markerCardText}>{event.period ? `Start of Period ${event.period}` : 'Kick-off'}</Text>
                        <Text style={styles.markerCardRealTime}>{event.realTime}</Text>
                        {event.kickoffTeam && <Text style={styles.kickoffTeamText}>Kick-off: {event.kickoffTeam}</Text>}
                    </View>
                  );
                case 'halftime_start':
                  return (
                    <View style={styles.markerCard}>
                      <Text style={styles.markerCardText}>Half Time</Text>
                    </View>
                  );
                case 'halftime_end':
                  // This event now correctly triggers the display of the 'Start of Period 2' card.
                  return null;
                case 'match_abandoned':
                  return (
                    <View style={[styles.markerCard, { backgroundColor: '#DC3545' }]}>
                        <Text style={[styles.markerCardText, { color: '#FFFFFF' }]}>Match Abandoned</Text>
                        <Text style={[styles.markerCardRealTime, { color: '#FFFFFF', opacity: 0.9 }]}>{event.period}</Text>
                    </View>
                  );
                case 'red_card':
                  return (
                    <View style={styles.eventContainer}>
                      <View style={styles.timeBubble}>
                        <Text style={styles.timeText}>{event.time}</Text>
                      </View>
                      <View style={[styles.eventDetails, side === 'left' ? styles.eventDetailsLeft : styles.eventDetailsRight]}>
                        <View style={styles.redCard} />
                        <Text style={[styles.playerText, side === 'left' ? styles.playerTextLeft : styles.playerTextRight]}>
                          <Text style={{ fontWeight: 'bold' }}>#{event.player?.number}</Text> {event.player?.name} ({event.player?.role})
                        </Text>
                      </View>
                    </View>
                  );
                case 'yellow_card':
                  return (
                    <View style={styles.eventContainer}>
                      <View style={styles.timeBubble}>
                        <Text style={styles.timeText}>{event.time}</Text>
                      </View>
                      <View style={[styles.eventDetails, side === 'left' ? styles.eventDetailsLeft : styles.eventDetailsRight]}>
                        <View style={styles.yellowCard} />
                        <Text style={[styles.playerText, side === 'left' ? styles.playerTextLeft : styles.playerTextRight]}>
                          <Text style={{ fontWeight: 'bold' }}>#{event.player?.number}</Text> {event.player?.name} ({event.player?.role})
                        </Text>
                      </View>
                    </View>
                  );
                case 'goal':
                  return (
                    <View style={styles.eventContainer}>
                      <View style={styles.timeBubble}>
                        <Text style={styles.timeText}>{event.time}</Text>
                      </View>
                      <View style={[styles.eventDetails, side === 'left' ? styles.eventDetailsLeft : styles.eventDetailsRight]}>
                        <View style={styles.goalIconContainer}>
                          <SoccerBallIcon color={theme.text} size={16} />
                        </View>
                        <Text style={[styles.playerText, side === 'left' ? styles.playerTextLeft : styles.playerTextRight]}>
                          <Text style={{ fontWeight: 'bold' }}>#{event.player?.number}</Text> {event.player?.name} ({event.player?.role})
                        </Text>
                      </View>
                    </View>
                  );
                case 'substitution':
                  return (
                    <View style={styles.eventContainer}>
                      <View style={styles.timeBubble}>
                        <Text style={styles.timeText}>{event.time}</Text>
                      </View>
                      <View style={[styles.eventDetails, side === 'left' ? styles.eventDetailsLeft : styles.eventDetailsRight]}>
                        <View style={[styles.substitutionContainer, side === 'left' ? styles.substitutionLeft : styles.substitutionRight]}>
                          <View style={[styles.substitutionRow, side === 'left' ? styles.rowReverse : null]}>
                            <SubstitutionIcon direction="in" />
                            <Text style={[styles.playerText, side === 'left' ? styles.playerTextLeft : styles.playerTextRight]}>
                              <Text style={{ fontWeight: 'bold' }}>#{event.playerIn?.number}</Text> {event.playerIn?.name}
                            </Text>
                          </View>
                          <View style={[styles.substitutionRow, side === 'left' ? styles.rowReverse : null]}>
                            <SubstitutionIcon direction="out" />
                            <Text style={[styles.playerText, side === 'left' ? styles.playerTextLeft : styles.playerTextRight]}>
                              <Text style={{ fontWeight: 'bold' }}>#{event.playerOut?.number}</Text> {event.playerOut?.name}
                            </Text>
                          </View>
                        </View>
                      </View>
                    </View>
                  );
                case 'penalty_shot':
                  const getOutcomeIcon = () => {
                    switch (event.outcome) {
                      case 'goal':
                        return <SoccerBallIcon color="#4CAF50" size={16} />;
                      case 'save':
                        return <View style={{ width: 16, height: 16, justifyContent: 'center', alignItems: 'center' }}><Text style={{ fontSize: 12, color: '#FF9800' }}>🥅</Text></View>;
                      case 'miss':
                        return <Text style={{ fontSize: 16, color: '#F44336' }}>❌</Text>;
                      default:
                        return <Text style={{ fontSize: 16, color: theme.text }}>⚽</Text>;
                    }
                  };
                  return (
                    <View style={styles.eventContainer}>
                      <View style={styles.timeBubble}>
                        <Text style={styles.timeText}>{event.time}</Text>
                      </View>
                      <View style={[styles.eventDetails, side === 'left' ? styles.eventDetailsLeft : styles.eventDetailsRight]}>
                        <View style={styles.goalIconContainer}>
                          {getOutcomeIcon()}
                        </View>
                        <Text style={[styles.playerText, side === 'left' ? styles.playerTextLeft : styles.playerTextRight]}>
                          <Text style={{ fontWeight: 'bold' }}>#{event.player?.number}</Text> {event.player?.name} ({event.outcome?.toUpperCase()})
                        </Text>
                      </View>
                    </View>
                  );
                case 'penalty_start':
                  return (
                    <View style={styles.markerCard}>
                      <Text style={styles.markerCardTime}>{event.time}</Text>
                      <Text style={styles.markerCardText}>{event.period}</Text>
                      <Text style={styles.markerCardRealTime}>{event.realTime}</Text>
                      {event.startingTeam && <Text style={styles.kickoffTeamText}>Starting team: {event.startingTeam === 'home' ? 'Home' : 'Away'}</Text>}
                    </View>
                  );
                case 'penalty_end':
                  console.log('🎯 Rendering penalty_end event:', event);
                  return (
                    <View style={styles.endEventCard}>
                      <Text style={styles.endEventTime}>{event.time}</Text>
                      <Text style={styles.endEventStatus}>PENALTIES END</Text>
                      <Text style={styles.endEventRealTime}>{event.realTime}</Text>
                      <Text style={styles.endEventScore}>{event.score}</Text>
                      {event.winner && <Text style={styles.kickoffTeamText}>Winner: {event.winner === 'home' ? 'Home' : 'Away'}</Text>}
                    </View>
                  );
              }
            };

            return (
              <View key={index} style={isLast ? styles.timelineEventLast : styles.timelineEvent}>
                {eventContent()}
              </View>
            );
          })}
        </View>
      </View>
    );
  };

const Face1_VeryDissatisfied = ({ size = 24 }: { size?: number }) => ( <Svg height={size} width={size} viewBox="0 0 24 24"><Circle cx="12" cy="12" r="11.5" fill="#FF0000" stroke="black" strokeWidth="0.5" /><Path d="M9 9V11" stroke="black" strokeWidth="1.5" strokeLinecap="round" /><Path d="M15 9V11" stroke="black" strokeWidth="1.5" strokeLinecap="round" /><Path d="M8 16 Q 12 13 16 16" stroke="black" strokeWidth="1.5" fill="none" strokeLinecap="round" /></Svg> );
const Face2_Dissatisfied = ({ size = 24 }: { size?: number }) => ( <Svg height={size} width={size} viewBox="0 0 24 24"><Circle cx="12" cy="12" r="11.5" fill="#EE7D4C" stroke="black" strokeWidth="0.5" /><Path d="M9 9V11" stroke="black" strokeWidth="1.5" strokeLinecap="round" /><Path d="M15 9V11" stroke="black" strokeWidth="1.5" strokeLinecap="round" /><Path d="M8 15 Q 12 14 16 15" stroke="black" strokeWidth="1.5" fill="none" strokeLinecap="round" /></Svg> );
const Face3_Somehow = ({ size = 24 }: { size?: number }) => ( <Svg height={size} width={size} viewBox="0 0 24 24"><Circle cx="12" cy="12" r="11.5" fill="#FFDF0F" stroke="black" strokeWidth="0.5" /><Path d="M9 9V11" stroke="black" strokeWidth="1.5" strokeLinecap="round" /><Path d="M15 9V11" stroke="black" strokeWidth="1.5" strokeLinecap="round" /><Path d="M8 15 H 16" stroke="black" strokeWidth="1.5" fill="none" strokeLinecap="round" /></Svg> );
const Face4_Satisfied = ({ size = 24 }: { size?: number }) => ( <Svg height={size} width={size} viewBox="0 0 24 24"><Circle cx="12" cy="12" r="11.5" fill="#7FD120" stroke="black" strokeWidth="0.5" /><Path d="M9 9V11" stroke="black" strokeWidth="1.5" strokeLinecap="round" /><Path d="M15 9V11" stroke="black" strokeWidth="1.5" strokeLinecap="round" /><Path d="M8 14 Q 12 16 16 14" stroke="black" strokeWidth="1.5" fill="none" strokeLinecap="round" /></Svg> );
const Face5_VerySatisfied = ({ size = 24 }: { size?: number }) => ( <Svg height={size} width={size} viewBox="0 0 24 24"><Circle cx="12" cy="12" r="11.5" fill="#00C42A" stroke="black" strokeWidth="0.5" /><Path d="M9 9V11" stroke="black" strokeWidth="1.5" strokeLinecap="round" /><Path d="M15 9V11" stroke="black" strokeWidth="1.5" strokeLinecap="round" /><Path d="M8 13 Q 12 18 16 13" stroke="black" strokeWidth="1.5" fill="none" strokeLinecap="round" /></Svg> );
const EvaluationFace = ({ level, size=24 }: { level: number, size?: number }) => { switch (level) { case 1: return <Face1_VeryDissatisfied size={size} />; case 2: return <Face2_Dissatisfied size={size} />; case 3: return <Face3_Somehow size={size} />; case 4: return <Face4_Satisfied size={size} />; case 5: return <Face5_VerySatisfied size={size} />; default: return null; }};
const FeelingScaleSVG = () => ( <Svg width="100%" height="100%" viewBox="0 0 214 26" fill="none" preserveAspectRatio="xMidYMid meet"><Rect x="127.75" y="17.75" width="49.5" height="7.5" rx="3.75" fill="#7FD120" stroke="black" strokeWidth="0.5"/><Rect x="170.75" y="17.75" width="42.5" height="7.5" rx="3.75" fill="#00C42A" stroke="black" strokeWidth="0.5"/><Rect x="85.75" y="17.75" width="47.5" height="7.5" rx="3.75" fill="#FFDF0F" stroke="black" strokeWidth="0.5"/><Rect x="42.75" y="17.75" width="48.5" height="7.5" rx="3.75" fill="#EE7D4C" stroke="black" strokeWidth="0.5"/><Rect x="0.75" y="17.75" width="45.5" height="7.5" rx="3.75" fill="#FF0000" stroke="black" strokeWidth="0.5"/><Ellipse cx="24.5" cy="8" rx="7.5" ry="8" fill="#FF0000"/><Ellipse cx="67.5" cy="8" rx="7.5" ry="8" fill="#ED7D4C"/><Ellipse cx="109.5" cy="8" rx="7.5" ry="8" fill="#FFDF0F"/><Ellipse cx="152.5" cy="8" rx="7.5" ry="8" fill="#7FD120"/><Ellipse cx="192.5" cy="8" rx="7.5" ry="8" fill="#00C42A"/><G clipPath="url(#clip4_1136_41)"><Path d="M24.5 0.375C25.9138 0.375 27.2959 0.822198 28.4715 1.66004C29.647 2.49789 30.5632 3.68875 31.1043 5.08204C31.6453 6.47533 31.7869 8.00846 31.5111 9.48756C31.2353 10.9667 30.5544 12.3253 29.5547 13.3917C28.555 14.4581 27.2813 15.1843 25.8946 15.4785C24.5079 15.7727 23.0706 15.6217 21.7644 15.0446C20.4582 14.4675 19.3418 13.4901 18.5563 12.2362C17.7708 10.9823 17.3516 9.50808 17.3516 8C17.3534 5.97833 18.1072 4.04004 19.4474 2.6105C20.7875 1.18097 22.6047 0.376986 24.5 0.375ZM24.5 0C23.0166 0 21.5666 0.469192 20.3332 1.34824C19.0999 2.22729 18.1386 3.47672 17.5709 4.93853C17.0032 6.40034 16.8547 8.00887 17.1441 9.56072C17.4335 11.1126 18.1478 12.538 19.1967 13.6569C20.2456 14.7757 21.582 15.5376 23.0368 15.8463C24.4917 16.155 25.9997 15.9965 27.3701 15.391C28.7406 14.7855 29.9119 13.7602 30.736 12.4446C31.5601 11.129 32 9.58225 32 8C32 5.87827 31.2098 3.84344 29.8033 2.34315C28.3968 0.842855 26.4891 0 24.5 0Z" fill="black"/><Path d="M27.0273 7.66626C26.9807 7.66626 26.936 7.6465 26.903 7.61134C26.8701 7.57618 26.8516 7.52849 26.8516 7.47876V6.35376C26.8516 6.30403 26.8701 6.25634 26.903 6.22118C26.936 6.18601 26.9807 6.16626 27.0273 6.16626C27.074 6.16626 27.1187 6.18601 27.1516 6.22118C27.1846 6.25634 27.2031 6.30403 27.2031 6.35376V7.47876C27.2028 7.52839 27.1842 7.57589 27.1513 7.61098C27.1184 7.64607 27.0739 7.66593 27.0273 7.66626Z" fill="black"/><Path d="M21.9727 7.66626C21.9261 7.66593 21.8816 7.64607 21.8487 7.61098C21.8158 7.57589 21.7972 7.52839 21.7969 7.47876V6.35376C21.7969 6.30403 21.8154 6.25634 21.8484 6.22118C21.8813 6.18601 21.926 6.16626 21.9727 6.16626C22.0193 6.16626 22.064 6.18601 22.097 6.22118C22.1299 6.25634 22.1484 6.30403 22.1484 6.35376V7.47876C22.1484 7.52849 22.1299 7.57618 22.097 7.61134C22.064 7.6465 22.0193 7.66626 21.9727 7.66626Z" fill="black"/><Path d="M24.8137 9.21889C24.9512 9.21592 25.0902 9.21292 25.2343 9.24491C26.3131 9.41017 27.3634 10.2635 28.2738 11.7143C28.2998 11.751 28.3226 11.7996 28.3409 11.857C28.3591 11.9144 28.3724 11.9796 28.38 12.0485C28.3876 12.1175 28.3893 12.1889 28.385 12.2584C28.3807 12.328 28.3705 12.3942 28.3551 12.4533C28.3396 12.5124 28.3192 12.563 28.295 12.6022C28.2708 12.6414 28.2433 12.6683 28.2142 12.6814C28.1851 12.6945 28.155 12.6935 28.1257 12.6784C28.0964 12.6633 28.0684 12.6344 28.0435 12.5936C27.2011 11.25 26.2292 10.4593 25.2308 10.3054C23.962 10.1374 22.7222 10.9922 21.7133 12.7304C21.6905 12.777 21.6636 12.8114 21.6345 12.8316C21.6054 12.8518 21.5746 12.8574 21.5441 12.8478C21.5135 12.8383 21.4839 12.814 21.4572 12.7763C21.4304 12.7387 21.407 12.6886 21.3884 12.6291C21.3698 12.5697 21.3566 12.5022 21.3494 12.431C21.3422 12.3597 21.3412 12.2862 21.3466 12.2151C21.352 12.1441 21.3635 12.0769 21.3805 12.0179C21.3976 11.959 21.4197 11.9094 21.4455 11.8725C22.4178 10.1944 23.5917 9.26963 24.8137 9.21889Z" fill="black"/></G><G clipPath="url(#clip1_1136_41)"><Path d="M67.5 0.375C68.9138 0.375 70.2959 0.822198 71.4715 1.66004C72.647 2.49789 73.5632 3.68875 74.1043 5.08204C74.6453 6.47533 74.7869 8.00846 74.5111 9.48756C74.2353 10.9667 73.5544 12.3253 72.5547 13.3917C71.555 14.4581 70.2813 15.1843 68.8946 15.4785C67.5079 15.7727 66.0706 15.6217 64.7644 15.0446C63.4582 14.4675 62.3418 13.4901 61.5563 12.2362C60.7708 10.9823 60.3516 9.50808 60.3516 8C60.3534 5.97833 61.1072 4.04004 62.4474 2.6105C63.7875 1.18097 65.6047 0.376986 67.5 0.375ZM67.5 0C66.0166 0 64.5666 0.469192 63.3332 1.34824C62.0999 2.22729 61.1386 3.47672 60.5709 4.93853C60.0032 6.40034 59.8547 8.00887 60.1441 9.56072C60.4335 11.1126 61.1478 12.538 62.1967 13.6569C63.2456 14.7757 64.582 15.5376 66.0368 15.8463C67.4917 16.155 68.9997 15.9965 70.3701 15.391C71.7406 14.7855 72.9119 13.7602 73.736 12.4446C74.5601 11.129 75 9.58225 75 8C75 5.87827 74.2098 3.84344 72.8033 2.34315C71.3968 0.842855 69.4891 0 67.5 0Z" fill="black"/><Path d="M70.0273 7.66626C69.9807 7.66626 69.936 7.6465 69.903 7.61134C69.8701 7.57618 69.8516 7.52849 69.8516 7.47876V6.35376C69.8516 6.30403 69.8701 6.25634 69.903 6.22118C69.936 6.18601 69.9807 6.16626 70.0273 6.16626C70.074 6.16626 70.1187 6.18601 70.1516 6.22118C70.1846 6.25634 70.2031 6.30403 70.2031 6.35376V7.47876C70.2028 7.52839 70.1842 7.57589 70.1513 7.61098C70.1184 7.64607 70.0739 7.66593 70.0273 7.66626Z" fill="black"/><Path d="M64.9727 7.66626C64.9261 7.66593 64.8816 7.64607 64.8487 7.61098C64.8158 7.57589 64.7972 7.52839 64.7969 7.47876V6.35376C64.7969 6.30403 64.8154 6.25634 64.8484 6.22118C64.8813 6.18601 64.926 6.16626 64.9727 6.16626C65.0193 6.16626 65.064 6.18601 65.097 6.22118C65.1299 6.25634 65.1484 6.30403 65.1484 6.35376V7.47876C65.1484 7.52849 65.1299 7.57618 65.097 7.61134C65.064 7.6465 65.0193 7.66626 64.9727 7.66626Z" fill="black"/><Path d="M67.0808 10.346C67.1897 10.3436 67.2999 10.3412 67.4138 10.3552C68.2674 10.4251 69.0942 10.8174 69.8066 11.4907C69.827 11.5077 69.8447 11.5303 69.8588 11.557C69.8729 11.5838 69.8831 11.6142 69.8887 11.6465C69.8942 11.6787 69.8951 11.7122 69.8913 11.7448C69.8875 11.7774 69.879 11.8085 69.8664 11.8363C69.8538 11.864 69.8373 11.8879 69.8179 11.9064C69.7985 11.925 69.7765 11.9378 69.7534 11.9441C69.7303 11.9504 69.7065 11.9502 69.6833 11.9433C69.6602 11.9364 69.6382 11.9231 69.6187 11.9042C68.9596 11.2807 68.1945 10.9171 67.4045 10.852C66.4004 10.7822 65.4129 11.1913 64.6029 12.0126C64.5846 12.0346 64.5631 12.0509 64.5399 12.0606C64.5167 12.0702 64.4922 12.073 64.4681 12.0688C64.444 12.0645 64.4207 12.0533 64.3997 12.0359C64.3787 12.0185 64.3605 11.9951 64.3462 11.9674C64.3318 11.9397 64.3217 11.9082 64.3164 11.8749C64.3112 11.8415 64.3109 11.8071 64.3156 11.7738C64.3203 11.7405 64.3298 11.7089 64.3437 11.6812C64.3575 11.6534 64.3754 11.6301 64.3961 11.6126C65.1767 10.8197 66.1123 10.3783 67.0808 10.346Z" fill="black"/></G><G clipPath="url(#clip0_1136_41)"><Path d="M109.5 0.375C110.914 0.375 112.296 0.822198 113.471 1.66004C114.647 2.49789 115.563 3.68875 116.104 5.08204C116.645 6.47533 116.787 8.00846 116.511 9.48756C116.235 10.9667 115.554 12.3253 114.555 13.3917C113.555 14.4581 112.281 15.1843 110.895 15.4785C109.508 15.7727 108.071 15.6217 106.764 15.0446C105.458 14.4675 104.342 13.4901 103.556 12.2362C102.771 10.9823 102.352 9.50808 102.352 8C102.353 5.97833 103.107 4.04004 104.447 2.6105C105.788 1.18097 107.605 0.376986 109.5 0.375ZM109.5 0C108.017 0 106.567 0.469192 105.333 1.34824C104.1 2.22729 103.139 3.47672 102.571 4.93853C102.003 6.40034 101.855 8.00887 102.144 9.56072C102.434 11.1126 103.148 12.538 104.197 13.6569C105.246 14.7757 106.582 15.5376 108.037 15.8463C109.492 16.155 111 15.9965 112.37 15.391C113.741 14.7855 114.912 13.7602 115.736 12.4446C116.56 11.129 117 9.58225 117 8C117 5.87827 116.21 3.84344 114.803 2.34315C113.397 0.842855 111.489 0 109.5 0Z" fill="black"/><Path d="M112.027 7.66626C111.981 7.66626 111.936 7.6465 111.903 7.61134C111.87 7.57618 111.852 7.52849 111.852 7.47876V6.35376C111.852 6.30403 111.87 6.25634 111.903 6.22118C111.936 6.18601 111.981 6.16626 112.027 6.16626C112.074 6.16626 112.119 6.18601 112.152 6.22118C112.185 6.25634 112.203 6.30403 112.203 6.35376V7.47876C112.203 7.52839 112.184 7.57589 112.151 7.61098C112.118 7.64607 112.074 7.66593 112.027 7.66626Z" fill="black"/><Path d="M106.973 7.66626C106.926 7.66593 106.882 7.64607 106.849 7.61098C106.816 7.57589 106.797 7.52839 106.797 7.47876V6.35376C106.797 6.30403 106.815 6.25634 106.848 6.22118C106.881 6.18601 106.926 6.16626 106.973 6.16626C107.019 6.16626 107.064 6.18601 107.097 6.22118C107.13 6.25634 107.148 6.30403 107.148 6.35376V7.47876C107.148 7.52849 107.13 7.57618 107.097 7.61134C107.064 7.6465 107.019 7.66626 106.973 7.66626Z" fill="black"/><Path d="M107.358 11.6161C107.358 11.5663 107.411 11.5187 107.506 11.4837C107.6 11.4487 107.728 11.4291 107.861 11.4293L110.88 11.4336C111.013 11.4338 111.141 11.4537 111.235 11.489C111.33 11.5243 111.383 11.5721 111.383 11.6218C111.383 11.6716 111.329 11.7192 111.235 11.7542C111.141 11.7892 111.013 11.8088 110.879 11.8086L107.861 11.8043C107.728 11.8038 107.6 11.7837 107.506 11.7485C107.412 11.7133 107.359 11.6657 107.358 11.6161Z" fill="black"/></G><G clipPath="url(#clip2_1136_41)"><Path d="M152.5 0.375C153.914 0.375 155.296 0.822198 156.471 1.66004C157.647 2.49789 158.563 3.68875 159.104 5.08204C159.645 6.47533 159.787 8.00846 159.511 9.48756C159.235 10.9667 158.554 12.3253 157.555 13.3917C156.555 14.4581 155.281 15.1843 153.895 15.4785C152.508 15.7727 151.071 15.6217 149.764 15.0446C148.458 14.4675 147.342 13.4901 146.556 12.2362C145.771 10.9823 145.352 9.50808 145.352 8C145.353 5.97833 146.107 4.04004 147.447 2.6105C148.788 1.18097 150.605 0.376986 152.5 0.375ZM152.5 0C151.017 0 149.567 0.469192 148.333 1.34824C147.1 2.22729 146.139 3.47672 145.571 4.93853C145.003 6.40034 144.855 8.00887 145.144 9.56072C145.434 11.1126 146.148 12.538 147.197 13.6569C148.246 14.7757 149.582 15.5376 151.037 15.8463C152.492 16.155 154 15.9965 155.37 15.391C156.741 14.7855 157.912 13.7602 158.736 12.4446C159.56 11.129 160 9.58225 160 8C160 5.87827 159.21 3.84344 157.803 2.34315C156.397 0.842855 154.489 0 152.5 0Z" fill="black"/><Path d="M155.027 7.66626C154.981 7.66626 154.936 7.6465 154.903 7.61134C154.87 7.57618 154.852 7.52849 154.852 7.47876V6.35376C154.852 6.30403 154.87 6.25634 154.903 6.22118C154.936 6.18601 154.981 6.16626 155.027 6.16626C155.074 6.16626 155.119 6.18601 155.152 6.22118C155.185 6.25634 155.203 6.30403 155.203 6.35376V7.47876C155.203 7.52839 155.184 7.57589 155.151 7.61098C155.118 7.64607 155.074 7.66593 155.027 7.66626Z" fill="black"/><Path d="M149.973 7.66626C149.926 7.66593 149.882 7.64607 149.849 7.61098C149.816 7.57589 149.797 7.52839 149.797 7.47876V6.35376C149.797 6.30403 149.815 6.25634 149.848 6.22118C149.881 6.18601 149.926 6.16626 149.973 6.16626C150.019 6.16626 150.064 6.18601 150.097 6.22118C150.13 6.25634 150.148 6.30403 150.148 6.35376V7.47876C150.148 7.52849 150.13 7.57618 150.097 7.61134C150.064 7.6465 150.019 7.66626 149.973 7.66626Z" fill="black"/><Path d="M152.494 12.5714C152.368 12.5714 152.242 12.5714 152.111 12.5489C151.13 12.4279 150.186 11.866 149.381 10.9228C149.358 10.8989 149.338 10.8674 149.322 10.8303C149.306 10.7932 149.295 10.7513 149.29 10.7069C149.284 10.6626 149.284 10.6168 149.289 10.5722C149.294 10.5277 149.304 10.4853 149.319 10.4476C149.335 10.41 149.354 10.3778 149.377 10.353C149.4 10.3282 149.425 10.3113 149.452 10.3033C149.479 10.2953 149.506 10.2963 149.533 10.3064C149.559 10.3165 149.584 10.3354 149.606 10.362C150.351 11.2355 151.224 11.7561 152.132 11.8687C153.287 11.9941 154.433 11.463 155.383 10.362C155.405 10.3325 155.43 10.3107 155.457 10.2982C155.484 10.2856 155.512 10.2825 155.54 10.289C155.567 10.2956 155.594 10.3116 155.618 10.3361C155.641 10.3606 155.662 10.3931 155.678 10.4315C155.694 10.4699 155.705 10.5133 155.71 10.5591C155.715 10.6049 155.715 10.6521 155.709 10.6976C155.703 10.7431 155.691 10.786 155.674 10.8236C155.658 10.8612 155.637 10.8927 155.613 10.916C154.697 11.9789 153.61 12.5558 152.494 12.5714Z" fill="black"/></G><G clipPath="url(#clip3_1136_41)"><Path d="M192.5 0.375C193.914 0.375 195.296 0.822198 196.471 1.66004C197.647 2.49789 198.563 3.68875 199.104 5.08204C199.645 6.47533 199.787 8.00846 199.511 9.48756C199.235 10.9667 198.554 12.3253 197.555 13.3917C196.555 14.4581 195.281 15.1843 193.895 15.4785C192.508 15.7727 191.071 15.6217 189.764 15.0446C188.458 14.4675 187.342 13.4901 186.556 12.2362C185.771 10.9823 185.352 9.50808 185.352 8C185.353 5.97833 186.107 4.04004 187.447 2.6105C188.788 1.18097 190.605 0.376986 192.5 0.375ZM192.5 0C191.017 0 189.567 0.469192 188.333 1.34824C187.1 2.22729 186.139 3.47672 185.571 4.93853C185.003 6.40034 184.855 8.00887 185.144 9.56072C185.434 11.1126 186.148 12.538 187.197 13.6569C188.246 14.7757 189.582 15.5376 191.037 15.8463C192.492 16.155 194 15.9965 195.37 15.391C196.741 14.7855 197.912 13.7602 198.736 12.4446C199.56 11.129 200 9.58225 200 8C200 5.87827 199.21 3.84344 197.803 2.34315C196.397 0.842855 194.489 0 192.5 0Z" fill="black"/><Path d="M195.027 7.66626C194.981 7.66626 194.936 7.6465 194.903 7.61134C194.87 7.57618 194.852 7.52849 194.852 7.47876V6.35376C194.852 6.30403 194.87 6.25634 194.903 6.22118C194.936 6.18601 194.981 6.16626 195.027 6.16626C195.074 6.16626 195.119 6.18601 195.152 6.22118C195.185 6.25634 195.203 6.30403 195.203 6.35376V7.47876C195.203 7.52839 195.184 7.57589 195.151 7.61098C195.118 7.64607 195.074 7.66593 195.027 7.66626Z" fill="black"/><Path d="M189.973 7.66626C189.926 7.66593 189.882 7.64607 189.849 7.61098C189.816 7.57589 189.797 7.52839 189.797 7.47876V6.35376C189.797 6.30403 189.815 6.25634 189.848 6.22118C189.881 6.18601 189.926 6.16626 189.973 6.16626C190.019 6.16626 190.064 6.18601 190.097 6.22118C190.13 6.25634 190.148 6.30403 190.148 6.35376V7.47876C190.148 7.52849 190.13 7.57618 190.097 7.61134C190.064 7.6465 190.019 7.66626 189.973 7.66626Z" fill="black"/><Path d="M192.494 12.5714C192.368 12.5714 192.242 12.5714 192.111 12.5376C191.13 12.3562 190.186 11.5133 189.381 10.0985C189.358 10.0626 189.338 10.0154 189.322 9.95978C189.306 9.90415 189.295 9.8412 189.29 9.77469C189.284 9.70817 189.284 9.63946 189.289 9.57264C189.294 9.50581 189.304 9.44225 189.319 9.38575C189.335 9.32924 189.354 9.28094 189.377 9.24374C189.4 9.20653 189.425 9.18118 189.452 9.16919C189.479 9.1572 189.506 9.15882 189.533 9.17395C189.559 9.18909 189.584 9.21742 189.606 9.25727C190.351 10.5675 191.224 11.3485 192.132 11.5174C193.287 11.7054 194.433 10.9088 195.383 9.25727C195.405 9.21298 195.43 9.18038 195.457 9.16155C195.484 9.14271 195.512 9.13805 195.54 9.14784C195.567 9.15763 195.594 9.18167 195.618 9.21845C195.641 9.25522 195.662 9.30393 195.678 9.36151C195.694 9.41908 195.705 9.48428 195.71 9.55299C195.715 9.62169 195.715 9.69241 195.709 9.7607C195.703 9.82898 195.691 9.89335 195.674 9.94974C195.658 10.0061 195.637 10.0533 195.613 10.0883C194.697 11.6826 193.61 12.548 192.494 12.5714Z" fill="black"/></G><Defs><ClipPath id="clip0_1136_41"><Rect width="15" height="16" fill="white" transform="translate(102)"/></ClipPath><ClipPath id="clip1_1136_41"><Rect width="15" height="16" fill="white" transform="translate(60)"/></ClipPath><ClipPath id="clip2_1136_41"><Rect width="15" height="16" fill="white" transform="translate(145)"/></ClipPath><ClipPath id="clip3_1136_41"><Rect width="15" height="16" fill="white" transform="translate(185)"/></ClipPath><ClipPath id="clip4_1136_41"><Rect width="15" height="16" fill="white" transform="translate(17)"/></ClipPath></Defs></Svg> );
type EvaluationCriterion = 'Positioning & fitness' | 'Confidence under pressure' | 'Game flow control' | 'Focus & concentration' | 'Decision clarity';
const EvaluateContent = ({ styles, theme, matchId }: { styles: any; theme: any; matchId?: string }) => {
    const [evaluations, setEvaluations] = useState<Record<string, { level: number; label: string }>>({});
    const [loading, setLoading] = useState(true);
    const [noEvaluationData, setNoEvaluationData] = useState(false);

    // Load evaluation data when component mounts
    useEffect(() => {
        const loadEvaluationData = async () => {
            if (!matchId) {
                setLoading(false);
                setNoEvaluationData(true);
                return;
            }

            try {
                const evaluation = await EvaluationService.getMatchEvaluation(matchId);
                
                if (evaluation && evaluation.answers && evaluation.answers.length > 0) {
                    // Convert answers to display format
                    const displayFormat = EvaluationService.convertAnswersToDisplayFormat(evaluation.answers);
                    setEvaluations(displayFormat);
                    setNoEvaluationData(false);
                } else {
                    // No evaluation data found
                    setNoEvaluationData(true);
                }
            } catch (error) {
                console.error('Error loading evaluation data:', error);
                setNoEvaluationData(true);
            } finally {
                setLoading(false);
            }
        };

        loadEvaluationData();
    }, [matchId]);

    if (loading) {
        return (
            <View style={styles.evaluateContainer}>
                <Text style={styles.evaluateMainTitle}>Loading evaluation data...</Text>
            </View>
        );
    }

    if (noEvaluationData) {
        return (
            <View style={styles.evaluateContainer}>
                <Text style={styles.evaluateMainTitle}>No evaluation data available</Text>
                <Text style={[styles.evaluateMainTitle, { fontSize: 14, opacity: 0.7 }]}>
                    Evaluation questions are completed after match ends.
                </Text>
            </View>
        );
    }

    const evaluationCriteria = Object.keys(evaluations);
    const feelingLevels = [ { level: 1, label: 'Very\nDissatisfied', cx: 24.5}, { level: 2, label: 'Dissatisfied', cx: 67.5 }, { level: 3, label: 'Somehow', cx: 109.5 }, { level: 4, label: 'Satisfied', cx: 152.5 }, { level: 5, label: 'Very\nSatisfied', cx: 192.5 }, ];
    return (
        <View style={styles.evaluateContainer}>
            <Text style={styles.evaluateMainTitle}>How did you feel after this match</Text>
            <View style={styles.evaluateCard}>
                <View style={styles.feelingScaleContainer}><FeelingScaleSVG /></View>
                <View style={styles.labelsContainer}>
                    {feelingLevels.map(({ level, label, cx }) => ( <View key={level} style={[ styles.labelWrapper, { left: `${(cx / 214) * 100}%` } ]}><Text style={styles.labelNumber}>{level}</Text><Text style={styles.labelText}>{label}</Text></View> ))}
                </View>
                <View style={styles.criteriaContainer}>
                    {evaluationCriteria.map((criterion, index) => {
                        const evaluation = evaluations[criterion];
                        return ( <View key={index} style={styles.criterionRow}><Text style={styles.criterionText}>{criterion}</Text><View style={styles.evaluationResult}><EvaluationFace level={evaluation.level} size={30} /></View></View> );
                    })}
                </View>
            </View>
        </View>
    );
};


// --- MAIN SCREEN COMPONENT ---
const MatchResultsScreen = ({ route }: { route: any }) => {
  const { theme, isDarkMode } = useTheme();
  const styles = getStyles(theme, isDarkMode);
  const overviewStyles = getOverviewStyles(theme);
  const teamStyles = getTeamStyles(theme);
  const matchLogStyles = getMatchLogStyles(theme);
  const evaluateStyles = getEvaluateStyles(theme);
  
  const navigation = useNavigation();
  const [activeTab, setActiveTab] = useState('overview');
  const [activeTeamView, setActiveTeamView] = useState('Home');
  const scrollViewRef = useRef<ScrollView>(null);
  const [tabLayouts, setTabLayouts] = useState<Record<number, {x: number, width: number}>>({});
  const activeTabIndex = TABS.findIndex(tab => tab.key === activeTab);

  const { matchId, initialLogs, matchLog, fromAbandonment } = route.params || {};
  const [matchData, setMatchData] = useState<any>(null);
  const [calculatedScore, setCalculatedScore] = useState({ home: 0, away: 0 });
  const [matchEvents, setMatchEvents] = useState<any[]>([]);

  // Calculate score from match logs or API events
  useEffect(() => {
    const calculateScoreFromLogs = (logs: any[]) => {
      const homeGoals = logs.filter((log: any) => 
        log.type === 'goal' && log.data?.team === 'home'
      ).length;
      const awayGoals = logs.filter((log: any) => 
        log.type === 'goal' && log.data?.team === 'away'
      ).length;
      setCalculatedScore({ home: homeGoals, away: awayGoals });
    };

    if (matchLog && matchLog.length > 0) {
      // If matchLog is provided (from StartMatchScreen), use it
      calculateScoreFromLogs(matchLog);
    } else if (matchEvents && matchEvents.length > 0) {
      // Otherwise, use events fetched from API
      calculateScoreFromLogs(matchEvents);
    }
  }, [matchLog, matchEvents]);

  useEffect(() => {
    const fetchFullMatchData = async () => {
      if (matchId) {
        const response = await MatchService.getMatchById(matchId);
        if (response.success && response.data) {
          const formattedData = MatchService.convertApiResponseToDetailFormat(response.data);
          
          // Fetch squad data for both teams to get team officials
          try {
            const [homeSquadResponse, awaySquadResponse] = await Promise.all([
              SquadService.getSquadByMatch(matchId, 'home'),
              SquadService.getSquadByMatch(matchId, 'away')
            ]);

            // Add squad data (including officials) to teams
            if (homeSquadResponse.success && homeSquadResponse.data) {
              formattedData.homeTeam = {
                ...formattedData.homeTeam,
                squad: homeSquadResponse.data,
                officials: homeSquadResponse.data.officials || []
              };
            }
            if (awaySquadResponse.success && awaySquadResponse.data) {
              formattedData.awayTeam = {
                ...formattedData.awayTeam,
                squad: awaySquadResponse.data,
                officials: awaySquadResponse.data.officials || []
              };
            }
          } catch (squadError) {
            console.error('Error fetching squad data:', squadError);
            // Continue without squad data
          }
          
          setMatchData(formattedData);
        }

        // Fetch match events/logs from API if not provided in route params
        if (!matchLog || matchLog.length === 0) {
          try {
            const logsResponse = await MatchService.getMatchLogs(matchId);
            if (logsResponse.success && logsResponse.data) {
              console.log('Fetched match logs from API:', logsResponse.data);
              setMatchEvents(logsResponse.data);
            }
          } catch (error) {
            console.error('Error fetching match logs:', error);
          }
        }
      }
    };
    fetchFullMatchData();
  }, [matchId]);


  const TeamDisplay = ({ kit, name, color }: { kit: { base: string; stripe: string, shorts: string }, name: string, color: string }) => (
    <View style={styles.teamDisplayContainer}>
      <StripedTshirt baseColor={kit.base} stripeColor={kit.stripe} size={48} />
      <ShortsIcon color={kit.shorts} size={26} />
      <Text style={[styles.teamName, { color: color }]}>{name}</Text>
    </View>
  );

  const renderContent = () => {
    if (activeTab === 'match') { 
      return <MatchLogContent styles={matchLogStyles} theme={theme} matchId={matchId} matchInfo={matchData} initialLogs={initialLogs} />; 
    }
    if (activeTab === 'earnings') { return <EarningsContent overviewStyles={overviewStyles} styles={styles} theme={theme} matchData={matchData} />; }
    if (activeTab === 'notes') { return <NotesContent overviewStyles={overviewStyles} styles={styles} theme={theme} matchData={matchData} />; }
    if (activeTab === 'overview') { return <OverviewContent overviewStyles={overviewStyles} styles={styles} theme={theme} matchData={matchData} />; }
    if (activeTab === 'evaluate') { return <EvaluateContent styles={evaluateStyles} theme={theme} matchId={matchId}/>; }
    if (activeTab === 'team') {
      const logs = matchLog || matchEvents;
      return (
        <View>
          <View style={styles.homeAwayToggle}>
            <TouchableOpacity style={[styles.toggleButton, activeTeamView === 'Home' && styles.activeToggleButton]} onPress={() => setActiveTeamView('Home')}>
              <Text style={[styles.toggleButtonText, activeTeamView === 'Home' && styles.activeToggleButtonText]}>HOME</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.toggleButton, activeTeamView === 'Away' && styles.activeToggleButton]} onPress={() => setActiveTeamView('Away')}>
              <Text style={[styles.toggleButtonText, activeTeamView === 'Away' && styles.activeToggleButtonText]}>AWAY</Text>
            </TouchableOpacity>
          </View>
          {activeTeamView === 'Home' && <TeamContent team={matchData.homeTeam} teamType="home" matchLog={logs} styles={teamStyles} theme={theme} /> }
          {activeTeamView === 'Away' && <TeamContent team={matchData.awayTeam} teamType="away" matchLog={logs} styles={teamStyles} theme={theme} /> }
        </View>
      );
    }
    return <View style={styles.card}><Text style={styles.cardTitle}>{TABS.find(t=>t.key === activeTab)?.label} Content</Text></View>;
  };

  // Show loading state until data is ready
  if (!matchData) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <Header 
          title="Loading..." 
          onBackPress={fromAbandonment ? () => navigation.navigate('DashboardHome' as never) : undefined}
        />
        <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
          <Text style={{ color: theme.text }}>Loading match data...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <Header 
        title={`Match Result - ${calculatedScore.home} - ${calculatedScore.away}`} 
        onBackPress={fromAbandonment ? () => navigation.navigate('DashboardHome' as never) : undefined}
      />
      <ScrollView style={styles.container} contentContainerStyle={styles.scrollContent} nestedScrollEnabled={true}>
        <View style={styles.card}>
          <Text style={styles.leagueTitle}>{matchData?.league || matchData?.competition || 'Match'}</Text>
          <View style={styles.separator} />
          <View style={styles.teamsRow}>
            <TeamDisplay kit={matchData?.homeTeam?.kit || { base: '#004d98', stripe: '#ed2939', shorts: '#004d98' }} name={matchData?.homeTeam?.name || 'Home'} color={theme.text} />
            <View style={styles.scoreContainer}>
              <Text style={styles.scoreText}>{calculatedScore.home}</Text>
              <Text style={styles.scoreText}>-</Text>
              <Text style={styles.scoreText}>{calculatedScore.away}</Text>
            </View>
            <TeamDisplay kit={matchData?.awayTeam?.kit || { base: '#FFFFFF', stripe: '#db0007', shorts: '#FFFFFF' }} name={matchData?.awayTeam?.name || 'Away'} color={theme.text} />
          </View>
        </View>
        <View style={styles.tabsContainer}>
          <ScrollView ref={scrollViewRef} horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.tabsScrollContainer} bounces={false}>{TABS.map((tab, index) => ( <TouchableOpacity key={tab.key} onLayout={(event: any) => { const { x, width } = event.nativeEvent.layout; setTabLayouts((prev) => ({ ...prev, [index]: { x, width } })); }} style={[styles.tab, activeTab === tab.key && styles.activeTab]} onPress={() => setActiveTab(tab.key)} activeOpacity={0.8}><Text style={[styles.tabText, activeTab === tab.key && styles.activeTabText]}>{tab.label}</Text></TouchableOpacity> ))}</ScrollView>
        </View>
        {renderContent()}
      </ScrollView>
    </SafeAreaView>
  );
};

// --- DYNAMIC STYLESHEETS ---
const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  safeArea: { flex: 1, backgroundColor: theme.card },
  container: { flex: 1, backgroundColor: theme.background },
  scrollContent: { paddingHorizontal: 16, paddingTop:16, paddingBottom: 80 },
  card: { backgroundColor: theme.card, borderRadius: 16, paddingVertical: 12, marginBottom: 16, shadowColor: isDarkMode ? 'transparent' : "#000", shadowOffset: { width: 0, height: 1 }, shadowOpacity: 0.05, shadowRadius: 4, elevation: 2, borderWidth: isDarkMode ? 1 : 0, borderColor: theme.border },
  cardTitle: { fontSize: 18, fontWeight: 'bold', color: theme.text, marginBottom: 12, paddingHorizontal: 16, paddingTop: 4 },
  leagueTitle: { fontSize: 16, fontWeight: '600', color: theme.text, textAlign: 'center', paddingBottom: 12, },
  separator: { height: 1, backgroundColor: theme.border, },
  teamsRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-start', paddingTop: 20, paddingBottom: 12, paddingHorizontal: 24 },
  teamDisplayContainer: { alignItems: 'center', flex: 1 },
  teamName: { marginTop: 8, fontSize: 16, fontWeight: 'bold' },
  scoreContainer: { flexDirection: 'row', alignItems: 'center', justifyContent: 'center', flex: 1.5, paddingTop: 10 },
  scoreText: { fontSize: 32, fontWeight: 'bold', color: theme.text, marginHorizontal: 8 },
  tabsContainer: { backgroundColor: theme.background, paddingVertical: 8, paddingHorizontal: 0, marginBottom: 8, marginHorizontal: -16 },
  tabsScrollContainer: { paddingHorizontal: 16, },
  tab: { paddingHorizontal: 14, paddingVertical: 6, backgroundColor: theme.card, borderRadius: 20, marginRight: 8, borderWidth: 1, borderColor: theme.border, alignItems: 'center', justifyContent: 'center', },
  activeTab: { backgroundColor: theme.primary, borderColor: theme.primary, },
  tabText: { fontSize: 14, fontWeight: '600', textAlign: 'center', color: theme.text, },
  activeTabText: { color: theme.card, },
  homeAwayToggle: { flexDirection: 'row', backgroundColor: theme.border, borderRadius: 10, padding: 4, marginBottom: 16, },
  toggleButton: { flex: 1, paddingVertical: 10, borderRadius: 8, alignItems: 'center', },
  activeToggleButton: { backgroundColor: theme.primary, },
  toggleButtonText: { color: theme.text, fontWeight: 'bold', fontSize: 13, },
  activeToggleButtonText: { color: theme.card, },
});

const getOverviewStyles = (theme: any) => StyleSheet.create({
    detailRow: { flexDirection: 'row', alignItems: 'center', backgroundColor: theme.background, borderRadius: 10, padding: 14, marginBottom: 8, marginHorizontal: 16, },
    detailLabel: { fontSize: 14, color: theme.text, fontWeight: '600', marginRight: 8, },
    valueContainer: { flex: 1, alignItems: 'flex-end', },
    detailValue: { fontSize: 13, color: theme.text, opacity: 0.7, fontWeight: '500', textAlign: 'right', },
    officialRow: { flexDirection: 'row', alignItems: 'center', backgroundColor: theme.background, borderRadius: 10, paddingVertical: 10, paddingHorizontal: 14, marginBottom: 8, marginHorizontal: 16, },
    iconContainer: { width: 32, alignItems: 'center', justifyContent: 'center' },
    iconLabel: { fontSize: 9, fontWeight: 'bold', color: theme.text, marginTop: 2 },
    officialName: { flex: 1, fontSize: 14, color: theme.text, fontWeight: '600', marginLeft: 12, },
    teamSectionWrapper: { paddingHorizontal: 16, marginBottom: 16 },
    teamSectionHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 },
    teamTag: { borderRadius: 15, paddingHorizontal: 10, paddingVertical: 5, },
    teamTagText: { color: '#FFFFFF', fontSize: 10, fontWeight: 'bold' },
    addButton: { padding: 6, borderRadius: 20, backgroundColor: `${theme.primary}20` },
    teamOfficialRow: { flexDirection: 'row', alignItems: 'center', backgroundColor: theme.background, borderRadius: 10, paddingVertical: 12, paddingHorizontal: 12, marginBottom: 8, minHeight: 56, },
    teamOfficialKitContainer: { flexDirection: 'column', alignItems: 'center', justifyContent: 'center', marginRight: 12, width: 32, },
    teamOfficialInfoContainer: { flex: 1, marginRight: 12, minWidth: 0, },
    teamOfficialManagerName: { fontSize: 13, color: theme.text, fontWeight: '500', flexWrap: 'wrap', lineHeight: 16, },
});

const getTeamStyles = (theme: any) => StyleSheet.create({
    teamCard: { backgroundColor: theme.card, borderRadius: 16, paddingVertical: 16, paddingHorizontal: 12, marginBottom: 16, shadowColor: "#000000", shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.1, shadowRadius: 4, elevation: 3, borderWidth: 1, borderColor: theme.border, },
    teamCardTitle: { fontSize: 18, fontWeight: 'bold', color: theme.text, marginBottom: 12, paddingHorizontal: 4, },
    playerRow: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', backgroundColor: theme.background, borderRadius: 12, paddingVertical: 12, paddingHorizontal: 16, marginBottom: 8, },
    playerInfo: { flex: 1, flexDirection: 'column', alignItems: 'flex-start', marginRight: 8, },
    playerNameContainer: { flexDirection: 'row', alignItems: 'center', },
    rowNumber: { fontSize: 14, color: theme.text, opacity: 0.7, minWidth: 25, },
    rowName: { fontSize: 14, color: theme.text, fontWeight: '500', },
    rowRole: { fontSize: 14, color: theme.text, opacity: 0.7, fontWeight: '500', },
    playerIcons: { flexDirection: 'row', alignItems: 'center', },
    captainBadge: { backgroundColor: theme.text, borderRadius: 4, width: 20, height: 20, justifyContent: 'center', alignItems: 'center', marginLeft: 8,},
    captainBadgeText: { color: theme.card,  marginTop: -2.7, fontWeight: 'bold', fontSize: 12, transform: [{ translateY: Platform.OS === 'ios' ? 1 : 0 }], },
    eventContainer: { flexDirection: 'row', alignItems: 'center', marginRight: 8, },
    cardEventIcon: { width: 10, height: 15, borderRadius: 2, marginRight: 4, },
    substitutionArrow: { marginRight: 4, },
    eventText: { fontSize: 14, color: theme.text, opacity: 0.7, fontWeight: '500', },
    substitutionEventRow: { marginTop: 4, marginRight: 0, paddingLeft: 25, }
});

const getMatchLogStyles = (theme: any) => StyleSheet.create({
    logContainer: { alignItems: 'center' },
    logToggleContainer: { flexDirection: 'row', backgroundColor: theme.card, borderRadius: 20, padding: 4, alignSelf: 'center', borderWidth: 1, borderColor: theme.border },
    logToggleButton: { paddingVertical: 8, paddingHorizontal: 24, borderRadius: 16 },
    activeLogToggle: { backgroundColor: theme.primary },
    logToggleText: { fontSize: 11, fontWeight: 'bold' },
    activeLogToggleText: { color: theme.card },
    inactiveLogToggleText: { color: theme.text },
    timelineContainer: { width: '100%', alignItems: 'center', marginTop: 17, flexDirection: 'column-reverse' },
    timelineEvent: { width: '100%', alignItems: 'center', position: 'relative' },
    timelineEventLast: { width: '100%', alignItems: 'center', position: 'relative' },
    timelineConnector: { width: 2, height: 30, backgroundColor: theme.card, opacity: 0.3, marginVertical: 6 },
    endEventCard: { width: '100%', backgroundColor: theme.card, borderRadius: 16, paddingVertical: 13, paddingHorizontal: 13, alignItems: 'center', borderWidth: 1, borderColor: theme.border },
    endEventTime: { fontSize: 16, fontWeight: '600', color: theme.text },
    endEventStatus: { fontSize: 20, fontWeight: 'bold', color: theme.text, marginVertical: 2 },
    endEventRealTime: { fontSize: 10, color: theme.text, opacity: 0.7, marginBottom: 8 },
    endEventScore: { fontSize: 35, fontWeight: 'bold', color: theme.text },
    eventContainer: { flexDirection: 'row', width: '100%', alignItems: 'center', justifyContent: 'center', minHeight: 60, position: 'relative' },
    timeBubble: { backgroundColor: theme.card, borderRadius: 15, paddingHorizontal: 12, paddingVertical: 4, zIndex: 2 },
    timeText: { fontSize: 16, fontWeight: 'bold', color: theme.text },
    eventDetails: { position: 'absolute', top: 0, bottom: 0, flexDirection: 'row', alignItems: 'center', maxWidth: '70%' },
    eventDetailsRight: { left: '50%', paddingLeft: 35 },
    eventDetailsLeft: { right: '50%', paddingRight: 30, flexDirection: 'row-reverse' },
    markerCard: { width: '100%', backgroundColor: theme.card, borderRadius: 12, paddingVertical: 10, paddingHorizontal: 13, alignItems: 'center', borderWidth: 1, borderColor: theme.border, marginVertical: 10 },
    markerCardText: { fontSize: 16, fontWeight: 'bold', color: theme.text, marginVertical: 1 },
    markerCardTime: { fontSize: 14, fontWeight: '600', color: theme.text, opacity: 0.9 },
    markerCardRealTime: { fontSize: 10, color: theme.text, opacity: 0.7, marginTop: 2, marginBottom: 4 },
    markerCardScore: { fontSize: 28, fontWeight: 'bold', color: theme.text },
    kickoffTeamText: { fontSize: 12, fontWeight: '500', color: theme.text, opacity: 0.8, marginTop: 4 },
    redCard: { width: 12, height: 18, backgroundColor: '#DC143C', borderRadius: 3, marginRight: 8 },
    yellowCard: { width: 12, height: 18, backgroundColor: '#FFD700', borderRadius: 3, marginRight: 8 },
    goalIconContainer: { marginRight: 8, alignItems: 'center', justifyContent: 'center' },
    playerText: { fontSize: 11, fontWeight: '500', color: theme.text, flexShrink: 1 },
    playerTextLeft: { textAlign: 'right', paddingRight: 10 },
    playerTextRight: { textAlign: 'left' },
    substitutionContainer: { justifyContent: 'space-around', height: '100%', marginBottom:-3, flexShrink: 1 },
    substitutionRow: { flexDirection: 'row', alignItems: 'center', flexShrink: 1, maxWidth: '100%' },
    substitutionLeft: {},
    substitutionRight: {},
    rowReverse: { flexDirection: 'row-reverse' },
});

const getEvaluateStyles = (theme: any) => StyleSheet.create({
    evaluateContainer: { alignItems: 'center', width: '100%' },
    evaluateMainTitle: { fontSize: 15, fontWeight: '600', color: theme.text, marginBottom: 16, },
    evaluateCard: { backgroundColor: theme.card, borderRadius: 16, width: '100%', padding: 16, marginBottom: 24, shadowColor: "#000", shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.1, shadowRadius: 5, elevation: 4, borderWidth: 1, borderColor: theme.border},
    feelingScaleContainer: { width: '100%', height: 40, marginBottom: 4, },
    labelsContainer: { height: 50, marginTop: 4, position: 'relative',  },
    labelWrapper: { position: 'absolute', width: 80,  alignItems: 'center', transform: [{ translateX: -40 }], },
    labelNumber: { fontSize: 8, color: theme.text, opacity: 0.7, fontWeight: '600', },
    labelText: { fontSize: 6, color: theme.text, textAlign: 'center', marginTop: 2, },
    criteriaContainer: { marginTop: 24, },
    criterionRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', backgroundColor: theme.card, borderWidth: 1, borderColor: theme.border, borderRadius: 12, paddingVertical: 12, paddingHorizontal: 16, marginBottom: 10, },
    criterionText: { fontSize: 15, fontWeight: '600', color: theme.text, flex: 1, marginRight: 8, },
    evaluationResult: { flexDirection: 'row', alignItems: 'center', },
});

export default MatchResultsScreen;