import io from 'socket.io-client';
import { MatchAction } from './broadcastService';

export interface ActionNotification {
  id: string;
  type: string;
  matchId: string;
  fromRole: string;
  requiresApproval: boolean;
  data: any;
  timestamp: number;
}

class WebSocketService {
  private socket: ReturnType<typeof io> | null = null;
  private userId: string | null = null;
  private matchId: string | null = null;
  private listeners: Map<string, Function[]> = new Map();

  /**
   * Connect to WebSocket server
   */
  connect(serverUrl: string = 'http://192.168.1.101:3000'): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.socket = io(serverUrl, {
          transports: ['websocket'],
          timeout: 10000,
        });

        this.socket.on('connect', () => {
          console.log('🔌 WebSocket connected');
          resolve();
        });

        this.socket.on('disconnect', () => {
          console.log('🔌 WebSocket disconnected');
          this.emit('disconnected');
        });

        this.socket.on('connect_error', (error: any) => {
          console.error('❌ WebSocket connection error:', error);
          reject(error);
        });

        this.socket.on('error', (error: any) => {
          console.error('❌ WebSocket error:', error);
          this.emit('error', error);
        });

        // Listen for notifications
        this.socket.on('notification', (notification: any) => {
          console.log('🔔 Received notification:', notification);
          this.emit('notification', notification);
        });

        // Listen for sync invitations
        this.socket.on('sync_invite', (invitation: any) => {
          console.log('📧 Received sync invitation:', invitation);
          this.emit('sync_invite', invitation);
        });

        // Listen for action broadcasts
        this.socket.on('action_received', (action: ActionNotification) => {
          console.log('📡 Received action broadcast:', action);
          this.emit('action_received', action);
        });

        // Listen for match events
        this.socket.on('match_started', (data: any) => {
          console.log('🏁 Match started:', data);
          this.emit('match_started', data);
        });

        this.socket.on('authenticated', (data: any) => {
          if (data.success) {
            console.log('✅ WebSocket authenticated');
            this.emit('authenticated');
          } else {
            console.error('❌ WebSocket authentication failed:', data.error);
            this.emit('auth_failed', data.error);
          }
        });

        this.socket.on('match_joined', (data: any) => {
          console.log('🏟️ Joined match room:', data.matchId);
          this.emit('match_joined', data);
        });

      } catch (error) {
        console.error('❌ Failed to create WebSocket connection:', error);
        reject(error);
      }
    });
  }

  /**
   * Authenticate with user ID
   */
  authenticate(userId: string, token?: string): void {
    if (!this.socket) {
      throw new Error('WebSocket not connected');
    }

    this.userId = userId;
    this.socket.emit('authenticate', { userId, token });
  }

  /**
   * Join match room for real-time sync
   */
  joinMatch(matchId: string): void {
    if (!this.socket) {
      throw new Error('WebSocket not connected');
    }

    this.matchId = matchId;
    this.socket.emit('join_match', { matchId });
  }

  /**
   * Broadcast match action to all participants
   */
  broadcastAction(action: MatchAction): void {
    if (!this.socket) {
      throw new Error('WebSocket not connected');
    }

    console.log('📡 Broadcasting action:', action);
    this.socket.emit('broadcast_action', action);
  }

  /**
   * Respond to action (accept/decline)
   */
  respondToAction(actionId: string, response: 'accept' | 'decline', reason?: string): void {
    if (!this.socket) {
      throw new Error('WebSocket not connected');
    }

    console.log(`✅ Responding to action ${actionId}: ${response}`);
    this.socket.emit('action_response', { actionId, response, reason });
  }

  /**
   * Disconnect from WebSocket
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.userId = null;
      this.matchId = null;
    }
  }

  /**
   * Add event listener
   */
  on(event: string, callback: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  /**
   * Remove event listener
   */
  off(event: string, callback?: Function): void {
    if (!this.listeners.has(event)) return;

    if (callback) {
      const callbacks = this.listeners.get(event)!;
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    } else {
      this.listeners.delete(event);
    }
  }

  /**
   * Emit event to listeners
   */
  private emit(event: string, data?: any): void {
    if (this.listeners.has(event)) {
      this.listeners.get(event)!.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`❌ Error in ${event} listener:`, error);
        }
      });
    }
  }

  /**
   * Check if connected
   */
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  /**
   * Get current user ID
   */
  getUserId(): string | null {
    return this.userId;
  }

  /**
   * Get current match ID
   */
  getMatchId(): string | null {
    return this.matchId;
  }
}

// Export singleton instance
export default new WebSocketService();
