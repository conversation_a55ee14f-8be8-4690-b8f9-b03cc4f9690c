import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  TextInput,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';

interface ModalPickerProps {
  visible: boolean;
  title: string;
  options: string[];
  selectedOptions: string[];
  onSelectionChange: (selected: string[]) => void;
  onClose: () => void;
  multiSelect?: boolean;
  placeholder?: string;
}

const ModalPicker: React.FC<ModalPickerProps> = ({
  visible,
  title,
  options,
  selectedOptions,
  onSelectionChange,
  onClose,
  multiSelect = true,
  placeholder = 'Search...',
}) => {
  const { theme, isDarkMode } = useTheme();
  const [searchText, setSearchText] = useState('');
  const [filteredOptions, setFilteredOptions] = useState(options);

  const styles = getStyles(theme, isDarkMode);

  useEffect(() => {
    const filtered = options.filter(option =>
      option.toLowerCase().includes(searchText.toLowerCase())
    );
    setFilteredOptions(filtered);
  }, [searchText, options]);

  const handleOptionPress = (option: string) => {
    if (multiSelect) {
      const isSelected = selectedOptions.includes(option);
      let newSelection;
      
      if (isSelected) {
        newSelection = selectedOptions.filter(item => item !== option);
      } else {
        newSelection = [...selectedOptions, option];
      }
      
      onSelectionChange(newSelection);
    } else {
      onSelectionChange([option]);
      onClose();
    }
  };

  const handleSelectAll = () => {
    if (selectedOptions.length === filteredOptions.length) {
      onSelectionChange([]);
    } else {
      onSelectionChange([...filteredOptions]);
    }
  };

  const handleClearAll = () => {
    onSelectionChange([]);
  };

  const renderOption = ({ item }: { item: string }) => {
    const isSelected = selectedOptions.includes(item);
    
    return (
      <TouchableOpacity
        style={[styles.optionRow, isSelected && styles.selectedOption]}
        onPress={() => handleOptionPress(item)}
      >
        <Text style={[styles.optionText, isSelected && styles.selectedOptionText]} numberOfLines={1}>
          {item}
        </Text>
        {multiSelect && (
          <View style={[styles.checkbox, isSelected && styles.checkedBox]}>
            {isSelected && <Ionicons name="checkmark" size={16} color={theme.card} />}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={theme.text} />
          </TouchableOpacity>
          <Text style={styles.title}>{title}</Text>
          <View style={{ width: 24 }} />
        </View>

        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color={theme.text} style={{ opacity: 0.6 }} />
          <TextInput
            style={styles.searchInput}
            placeholder={placeholder}
            placeholderTextColor={theme.text + '80'}
            value={searchText}
            onChangeText={setSearchText}
          />
          {searchText.length > 0 && (
            <TouchableOpacity onPress={() => setSearchText('')}>
              <Ionicons name="close-circle" size={20} color={theme.text} style={{ opacity: 0.6 }} />
            </TouchableOpacity>
          )}
        </View>

        {multiSelect && (
          <View style={styles.actionButtons}>
            <TouchableOpacity 
              style={styles.actionButton} 
              onPress={handleSelectAll}
            >
              <Text style={styles.actionButtonText}>
                {selectedOptions.length === filteredOptions.length ? 'Deselect All' : 'Select All'}
              </Text>
            </TouchableOpacity>
            {selectedOptions.length > 0 && (
              <TouchableOpacity 
                style={styles.actionButton} 
                onPress={handleClearAll}
              >
                <Text style={styles.actionButtonText}>Clear All</Text>
              </TouchableOpacity>
            )}
          </View>
        )}

        <Text style={styles.countText}>
          {multiSelect 
            ? `${selectedOptions.length} of ${filteredOptions.length} selected`
            : `${filteredOptions.length} options`
          }
        </Text>

        <FlatList
          data={filteredOptions}
          renderItem={renderOption}
          keyExtractor={(item, index) => `${item}_${index}`}
          style={styles.list}
          showsVerticalScrollIndicator={false}
        />

        <View style={styles.footer}>
          <TouchableOpacity style={styles.doneButton} onPress={onClose}>
            <Text style={styles.doneButtonText}>Done</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.border,
  },
  closeButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.text,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.card,
    margin: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 10,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: theme.text,
    paddingVertical: 4,
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  actionButton: {
    marginRight: 16,
  },
  actionButtonText: {
    color: theme.primary,
    fontSize: 14,
    fontWeight: '600',
  },
  countText: {
    fontSize: 14,
    color: theme.text,
    opacity: 0.7,
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  list: {
    flex: 1,
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.border,
  },
  selectedOption: {
    backgroundColor: theme.primary + '10',
  },
  optionText: {
    fontSize: 16,
    color: theme.text,
    flex: 1,
    marginRight: 12,
  },
  selectedOptionText: {
    color: theme.primary,
    fontWeight: '500',
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: theme.border,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkedBox: {
    backgroundColor: theme.primary,
    borderColor: theme.primary,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: theme.border,
  },
  doneButton: {
    backgroundColor: theme.primary,
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: 'center',
  },
  doneButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ModalPicker;
