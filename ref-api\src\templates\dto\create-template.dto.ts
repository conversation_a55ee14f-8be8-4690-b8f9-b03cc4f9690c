import { Is<PERSON><PERSON>, <PERSON>NotEmpty, <PERSON><PERSON><PERSON><PERSON>, IsBoolean, IsArray, IsOptional } from 'class-validator';

export class CreateTemplateDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  userId: string;

  @IsNumber()
  @IsOptional()
  teamSize?: number;

  @IsBoolean()
  @IsOptional()
  rollOnRollOff?: boolean;

  @IsNumber()
  @IsOptional()
  maxSubstitute?: number;

  @IsNumber()
  @IsOptional()
  numberOfPeriods?: number;

  @IsArray()
  @IsOptional()
  periodLengths?: number[];

  @IsNumber()
  @IsOptional()
  halfTime?: number;

  @IsBoolean()
  @IsOptional()
  extraTime?: boolean;

  @IsArray()
  @IsOptional()
  extraTimeLengths?: [number, number];

  @IsBoolean()
  @IsOptional()
  substituteOpportunities?: boolean;

  @IsNumber()
  @IsOptional()
  substituteOpportunitiesAllowance?: number;

  @IsBoolean()
  @IsOptional()
  extraTimeSubOpportunities?: boolean;

  @IsNumber()
  @IsOptional()
  extraTimeSubOpportunitiesAllowance?: number;

  @IsBoolean()
  @IsOptional()
  extraTimeAdditionalSub?: boolean;

  @IsNumber()
  @IsOptional()
  extraTimeAdditionalSubAllowance?: number;

  @IsBoolean()
  @IsOptional()
  penalties?: boolean;

  @IsString()
  @IsOptional()
  misconductCode?: string;

  @IsBoolean()
  @IsOptional()
  temporaryDismissals?: boolean;

  @IsNumber()
  @IsOptional()
  temporaryDismissalsTime?: number;

  @IsBoolean()
  @IsOptional()
  injuryTimeAllowance?: boolean;

  @IsNumber()
  @IsOptional()
  injuryTimeSubs?: number;

  @IsNumber()
  @IsOptional()
  injuryTimeSanctions?: number;

  @IsNumber()
  @IsOptional()
  injuryTimeGoals?: number;

  
}