import { Injectable, Logger, BadRequestException, NotFoundException, Inject, forwardRef } from '@nestjs/common';
import { InjectRedis } from '@nestjs-modules/ioredis';
import Redis from 'ioredis';
import { PrismaService } from '../prisma.service';
import { BroadcastService } from './broadcast.service';
import { UsersService } from '../users/users.service';
import { MatchesService } from '../matches/matches.service';

export interface SyncInvitation {
  matchId: string;
  invitedBy: string;
  role: string;
  invitedUserEmail?: string;
  invitedUserUsername?: string;
}

export interface MatchParticipant {
  id: string;
  userId: string;
  role: string;
  status: string;
  joinedAt?: Date | null;
  user: {
    id: string;
    name: string;
    email: string;
    displayName?: string | null;
  };
}

@Injectable()
export class MatchSyncService {
  private readonly logger = new Logger(MatchSyncService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly broadcastService: BroadcastService,
    private readonly usersService: UsersService,
    private readonly matchesService: MatchesService,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  /**
   * Invite user to sync with match by email or username
   */
  async inviteUserToMatch(invitation: SyncInvitation): Promise<void> {
    try {
      // Find user by email or username
      let invitedUser;
      if (invitation.invitedUserEmail) {
        invitedUser = await this.usersService.findByEmail(invitation.invitedUserEmail);
      } else if (invitation.invitedUserUsername) {
        invitedUser = await this.prisma.user.findFirst({
          where: { username: invitation.invitedUserUsername }
        });
      }

      if (!invitedUser) {
        throw new NotFoundException('User not found');
      }

      // Check if user is already a participant
      const existingParticipant = await this.prisma.matchParticipant.findUnique({
        where: {
          matchId_userId: {
            matchId: invitation.matchId,
            userId: invitedUser.id
          }
        }
      });

      if (existingParticipant) {
        throw new BadRequestException('User is already invited to this match');
      }

      // Get match details
      const match = await this.matchesService.findOne(invitation.matchId);

      // Don't create match yet - wait until invitation is accepted
      // This ensures the match is only created when the user actually joins

      // Check if this is the first participant being added
      const existingParticipants = await this.prisma.matchParticipant.findMany({
        where: { matchId: invitation.matchId }
      });

      // If no participants exist, add the referee (match creator) as a participant first
      if (existingParticipants.length === 0) {
        this.logger.log(`📋 Adding match creator as participant for match ${invitation.matchId}`);
        await this.prisma.matchParticipant.create({
          data: {
            matchId: invitation.matchId,
            userId: invitation.invitedBy, // The referee who is creating the invitation
            role: 'Referee',
            status: 'joined', // Referee is automatically joined
            joinedAt: new Date()
          }
        });
        this.logger.log(`✅ Added match creator as Referee participant`);
      }

      // Create participant record for the invited user
      await this.prisma.matchParticipant.create({
        data: {
          matchId: invitation.matchId,
          userId: invitedUser.id,
          role: invitation.role,
          status: 'invited'
        }
      });

      // Send notification to invited user
      const notificationData = {
        type: 'sync_invite' as const,
        matchId: invitation.matchId,
        fromUserId: invitation.invitedBy,
        data: {
          matchDetails: {
            competition: match.competition,
            venue: match.venue,
            homeTeam: match.homeTeamName,
            awayTeam: match.awayTeamName,
            matchDate: match.matchDate
          },
          role: invitation.role,
          invitedByName: (await this.usersService.findById(invitation.invitedBy)).name
        }
      };

      // Try to send via WebSocket first, then save to Redis
      await this.broadcastService.sendUserNotification(invitedUser.id, notificationData);

      this.logger.log(`📧 Invited user ${invitedUser.email} to match ${invitation.matchId} as ${invitation.role}`);
    } catch (error) {
      this.logger.error(`❌ Failed to invite user: ${error.message}`);
      throw error;
    }
  }

  /**
   * Accept sync invitation
   */
  async acceptInvitation(matchId: string, userId: string): Promise<void> {
    try {
      this.logger.log(`🔄 Processing invitation acceptance: matchId=${matchId}, userId=${userId}`);

      const participant = await this.prisma.matchParticipant.findUnique({
        where: {
          matchId_userId: { matchId, userId }
        }
      });

      if (!participant) {
        this.logger.error(`❌ Invitation not found: matchId=${matchId}, userId=${userId}`);
        throw new NotFoundException('Invitation not found');
      }

      if (participant.status !== 'invited') {
        this.logger.error(`❌ Invitation already processed: status=${participant.status}`);
        throw new BadRequestException('Invitation already processed');
      }

      this.logger.log(`📧 Found invitation: role=${participant.role}, status=${participant.status}`);

      // Map participant role to official role (use exact dropdown values)
      const officialRoleMap = {
        'Referee': 'Referee',
        'AR1': 'Ar 1',
        'AR2': 'Ar 2',
        '4th': '4th Official'
      };

      const officialRole = officialRoleMap[participant.role] || 'Assistant';

      // Create match for user with the correct official role
      this.logger.log(`📋 Creating match for user with role: ${officialRole}`);
      await this.createMatchForUserWithRole(matchId, userId, officialRole);
      this.logger.log(`📋 Match creation completed`);

      // Update participant status
      this.logger.log(`📧 Updating participant status to 'joined'`);
      await this.prisma.matchParticipant.update({
        where: { id: participant.id },
        data: {
          status: 'joined',
          joinedAt: new Date()
        }
      });

      this.logger.log(`✅ User ${userId} accepted invitation to match ${matchId} as ${participant.role}`);
      this.logger.log(`📋 Updated official role to: ${officialRole}`);

      // Broadcast sync update to all participants
      await this.broadcastSyncUpdate(matchId, userId, participant.role, 'joined');

      // Verify match was created by checking if user has any match with the same details
      const createdMatch = await this.prisma.match.findFirst({
        where: {
          refereeId: userId,
          competition: (await this.matchesService.findOne(matchId)).competition,
          matchDate: (await this.matchesService.findOne(matchId)).matchDate
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      if (createdMatch) {
        this.logger.log(`✅ Match verified in database: ${createdMatch.id} with role ${createdMatch.officialRole}`);
      } else {
        this.logger.error(`❌ Match not found in database after creation!`);
      }
    } catch (error) {
      this.logger.error(`❌ Failed to accept invitation: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get match participants
   */
  async getMatchParticipants(matchId: string): Promise<MatchParticipant[]> {
    try {
      this.logger.log(`🔍 Getting participants for match: ${matchId}`);

      // Check if this is a copied match (invited user's match)
      const match = await this.prisma.match.findUnique({
        where: { id: matchId },
        select: { notes: true, refereeId: true }
      });

      let originalMatchId = matchId;

      // Extract original match ID from notes if this is a copied match
      if (match?.notes && match.notes.includes('ORIGINAL_MATCH_ID:')) {
        const originalIdMatch = match.notes.match(/ORIGINAL_MATCH_ID:([^\s\n]+)/);
        if (originalIdMatch) {
          originalMatchId = originalIdMatch[1];
          this.logger.log(`🔗 This is a copied match, getting participants from original: ${originalMatchId}`);
        }
      }

      const participants = await this.prisma.matchParticipant.findMany({
        where: { matchId: originalMatchId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              displayName: true
            }
          }
        },
        orderBy: { createdAt: 'asc' }
      });

      this.logger.log(`✅ Found ${participants.length} participants for match ${originalMatchId}`);
      participants.forEach(p => {
        this.logger.log(`  - ${p.user.name} (${p.user.email}) - Role: ${p.role}, Status: ${p.status}`);
      });

      return participants.map(p => ({
        id: p.id,
        userId: p.userId,
        role: p.role,
        status: p.status,
        joinedAt: p.joinedAt || undefined,
        user: {
          id: p.user.id,
          name: p.user.name,
          email: p.user.email,
          displayName: p.user.displayName || undefined
        }
      }));
    } catch (error) {
      this.logger.error(`❌ Failed to get match participants: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get user's pending notifications (invitations)
   */
  async getUserNotifications(userId: string): Promise<any[]> {
    try {
      const pendingInvitations = await this.prisma.matchParticipant.findMany({
        where: {
          userId,
          status: 'invited'
        },
        include: {
          match: {
            select: {
              id: true,
              competition: true,
              venue: true,
              homeTeamName: true,
              awayTeamName: true,
              matchDate: true,
              referee: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        }
      });

      // Transform to notification format
      const notifications = pendingInvitations.map(invitation => ({
        id: `invite_${invitation.matchId}_${invitation.userId}`,
        type: 'sync_invite',
        matchId: invitation.matchId,
        fromUserId: invitation.match.referee.id,
        data: {
          matchDetails: {
            competition: invitation.match.competition,
            venue: invitation.match.venue,
            homeTeam: invitation.match.homeTeamName,
            awayTeam: invitation.match.awayTeamName,
            matchDate: invitation.match.matchDate
          },
          role: invitation.role,
          invitedByName: invitation.match.referee.name
        },
        timestamp: invitation.joinedAt?.getTime() || Date.now()
      }));

      this.logger.log(`📧 Found ${notifications.length} pending notifications for user ${userId}`);
      return notifications;
    } catch (error) {
      this.logger.error(`❌ Failed to get user notifications: ${error.message}`);
      return [];
    }
  }

  /**
   * Check if user is participant in match
   */
  async isUserParticipant(matchId: string, userId: string): Promise<boolean> {
    try {
      this.logger.log(`🔍 Checking if user ${userId} is participant in match ${matchId}`);

      // Check if user is the match owner (referee)
      const match = await this.prisma.match.findUnique({
        where: { id: matchId },
        select: { refereeId: true }
      });

      this.logger.log(`🔍 Match referee: ${match?.refereeId}, checking user: ${userId}`);

      if (match?.refereeId === userId) {
        this.logger.log(`✅ User ${userId} is the referee for match ${matchId}`);
        return true; // Match owner has access
      }

      // Check if user is a participant
      const participant = await this.prisma.matchParticipant.findUnique({
        where: {
          matchId_userId: { matchId, userId }
        }
      });

      this.logger.log(`🔍 Participant found: ${participant ? 'Yes' : 'No'}, Status: ${participant?.status}`);

      const isParticipant = participant?.status === 'joined';
      this.logger.log(`✅ User ${userId} is participant in match ${matchId}: ${isParticipant}`);

      return isParticipant;
    } catch (error) {
      this.logger.error(`❌ Failed to check participant status: ${error.message}`);
      return false;
    }
  }

  /**
   * Get user's role in match
   */
  async getUserRole(matchId: string, userId: string): Promise<string | null> {
    try {
      const participant = await this.prisma.matchParticipant.findUnique({
        where: {
          matchId_userId: { matchId, userId }
        }
      });

      return participant?.status === 'joined' ? participant.role : null;
    } catch (error) {
      this.logger.error(`❌ Failed to get user role: ${error.message}`);
      return null;
    }
  }

  /**
   * Handle action response (accept/decline)
   */
  async handleActionResponse(
    matchId: string,
    userId: string,
    actionId: string,
    response: 'accept' | 'decline',
    reason?: string
  ): Promise<void> {
    try {
      // TODO: Implement action response logic
      // This could involve:
      // 1. Recording the response in database
      // 2. If accepted, adding to user's personal match log
      // 3. Notifying other participants of the decision
      
      this.logger.log(`📝 User ${userId} ${response}ed action ${actionId} in match ${matchId}`);
    } catch (error) {
      this.logger.error(`❌ Failed to handle action response: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create match copy for invited user with specific role
   */
  private async createMatchForUserWithRole(originalMatchId: string, userId: string, officialRole: string): Promise<void> {
    try {
      // Check if user already has this match
      const existingMatch = await this.prisma.match.findFirst({
        where: {
          id: originalMatchId,
          refereeId: userId
        }
      });

      if (existingMatch) {
        // Update existing match with correct role
        await this.prisma.match.update({
          where: { id: existingMatch.id },
          data: { officialRole }
        });
        this.logger.log(`📋 Updated existing match role for user ${userId} to ${officialRole}`);
        return;
      }

      // Get original match
      const originalMatch = await this.matchesService.findOne(originalMatchId);

      // Create match copy for the invited user with ALL original data
      // Note: We can't use the same ID because it would conflict with the original match
      // Instead, we create a new match with a new ID but link it via participant table
      const newMatch = await this.prisma.match.create({
        data: {
          // Don't use originalMatchId - let Prisma generate a new ID
          // Basic match info
          competition: originalMatch.competition,
          venue: originalMatch.venue,
          matchDate: originalMatch.matchDate,
          officialRole: officialRole, // Use the assigned role (ONLY field we change)

          // Team info
          homeTeamName: originalMatch.homeTeamName,
          homeTeamShortName: originalMatch.homeTeamShortName,
          homeKitBase: originalMatch.homeKitBase,
          homeKitStripe: originalMatch.homeKitStripe,
          awayTeamName: originalMatch.awayTeamName,
          awayTeamShortName: originalMatch.awayTeamShortName,
          awayKitBase: originalMatch.awayKitBase,
          awayKitStripe: originalMatch.awayKitStripe,

          // Match format
          teamSize: originalMatch.teamSize,
          rollOnRollOff: originalMatch.rollOnRollOff,
          maxSubstitute: originalMatch.maxSubstitute,
          numberOfPeriods: originalMatch.numberOfPeriods,
          periodLengths: originalMatch.periodLengths as any,
          halfTime: originalMatch.halfTime,

          // Extra time settings
          extraTime: originalMatch.extraTime,
          extraTimeLengths: originalMatch.extraTimeLengths as any,
          substituteOpportunities: originalMatch.substituteOpportunities,
          substituteOpportunitiesAllowance: originalMatch.substituteOpportunitiesAllowance,
          extraTimeSubOpportunities: originalMatch.extraTimeSubOpportunities,
          extraTimeSubOpportunitiesAllowance: originalMatch.extraTimeSubOpportunitiesAllowance,
          extraTimeAdditionalSub: originalMatch.extraTimeAdditionalSub,
          extraTimeAdditionalSubAllowance: originalMatch.extraTimeAdditionalSubAllowance,

          // Match rules
          penalties: originalMatch.penalties,
          misconductCode: originalMatch.misconductCode,
          temporaryDismissals: originalMatch.temporaryDismissals,
          temporaryDismissalsTime: originalMatch.temporaryDismissalsTime,
          injuryTimeAllowance: originalMatch.injuryTimeAllowance,
          injuryTimeSubs: originalMatch.injuryTimeSubs,
          injuryTimeSanctions: originalMatch.injuryTimeSanctions,
          injuryTimeGoals: originalMatch.injuryTimeGoals,

          // Officials and other data
          matchOfficials: originalMatch.matchOfficials as any,
          fees: originalMatch.fees,
          expenses: originalMatch.expenses,
          mileage: originalMatch.mileage,
          travelTime: originalMatch.travelTime,
          notes: originalMatch.notes,
          templateName: originalMatch.templateName,
          summary: originalMatch.summary as any,
          status: originalMatch.status,

          // Assign to the invited user
          refereeId: userId
        }
      });

      this.logger.log(`📋 Created new match ${newMatch.id} for user ${userId} (synced with original ${originalMatchId})`);

      // Copy squad sheets (players and officials) from original match
      await this.copySquadSheets(originalMatchId, newMatch.id);

      // Store the original match ID in the new match for participant linking
      await this.prisma.match.update({
        where: { id: newMatch.id },
        data: {
          // Store original match ID in notes or a custom field for participant queries
          notes: `${newMatch.notes || ''}\nORIGINAL_MATCH_ID:${originalMatchId}`.trim()
        }
      });

      this.logger.log(`📋 Created match copy for user ${userId} with role ${officialRole}`);
    } catch (error) {
      this.logger.error(`❌ Failed to create match for user ${userId}: ${error.message}`);
      throw error;
    }
  }



  /**
   * Broadcast match start to all synced participants
   */
  async broadcastMatchStart(matchId: string, startData: any): Promise<void> {
    try {
      this.logger.log(`📡 Broadcasting match start for match ${matchId}`);

      // Get all participants for this match
      const participants = await this.prisma.matchParticipant.findMany({
        where: {
          matchId,
          status: 'joined'
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      });

      this.logger.log(`📡 Found ${participants.length} participants to notify`);

      // Send match start notification to each participant (excluding the referee who started the match)
      for (const participant of participants) {
        // Skip sending notification to the referee who started the match
        if (participant.userId === startData.refereeId) {
          this.logger.log(`📡 Skipping match start notification to referee ${participant.userId} (match initiator)`);
          continue;
        }

        const notification = {
          type: 'match_start' as const,
          matchId,
          fromUserId: startData.refereeId,
          data: {
            kickoffTeam: startData.kickoffTeam,
            period: startData.period,
            matchData: startData.matchData
          }
        };

        // Send via WebSocket
        if (this.broadcastService) {
          await this.broadcastService.sendUserNotification(participant.userId, notification);
        }

        this.logger.log(`📡 Sent match start notification to ${participant.user.name} (${participant.role})`);
      }

      this.logger.log(`✅ Match start broadcasted to all ${participants.length} participants`);
    } catch (error) {
      this.logger.error(`❌ Failed to broadcast match start: ${error.message}`);
      throw error;
    }
  }

  /**
   * Copy squad sheets from original match to new match
   */
  private async copySquadSheets(originalMatchId: string, newMatchId: string): Promise<void> {
    try {
      // Get original squads with players and officials
      const originalSquads = await this.prisma.squad.findMany({
        where: { matchId: originalMatchId },
        include: {
          players: true,
          officials: true
        }
      });

      this.logger.log(`📋 Found ${originalSquads.length} squads to copy`);

      // Copy each squad
      for (const originalSquad of originalSquads) {
        // Create new squad
        const newSquad = await this.prisma.squad.create({
          data: {
            matchId: newMatchId,
            teamType: originalSquad.teamType
          }
        });

        // Copy players
        if (originalSquad.players.length > 0) {
          await this.prisma.squadPlayer.createMany({
            data: originalSquad.players.map(player => ({
              squadId: newSquad.id,
              number: player.number,
              name: player.name,
              position: player.position,
              isCaptain: player.isCaptain,
              isGoalkeeper: player.isGoalkeeper
            }))
          });
          this.logger.log(`📋 Copied ${originalSquad.players.length} players for ${originalSquad.teamType} team`);
        }

        // Copy officials
        if (originalSquad.officials.length > 0) {
          await this.prisma.squadOfficial.createMany({
            data: originalSquad.officials.map(official => ({
              squadId: newSquad.id,
              role: official.role,
              name: official.name
            }))
          });
          this.logger.log(`📋 Copied ${originalSquad.officials.length} officials for ${originalSquad.teamType} team`);
        }
      }

      this.logger.log(`📋 Successfully copied all squad sheets to new match ${newMatchId}`);
    } catch (error) {
      this.logger.error(`❌ Failed to copy squad sheets: ${error.message}`);
      // Don't throw - squad copying is not critical for match creation
    }
  }

  /**
   * Create match copy for invited user (legacy method)
   */
  private async createMatchForUser(originalMatchId: string, userId: string): Promise<void> {
    try {
      // Check if user already has this match
      const existingMatch = await this.prisma.match.findFirst({
        where: {
          id: originalMatchId,
          refereeId: userId
        }
      });

      if (existingMatch) {
        return; // User already has this match
      }

      // Get original match
      const originalMatch = await this.matchesService.findOne(originalMatchId);

      // Create match copy for the invited user with only essential fields
      await this.prisma.match.create({
        data: {
          id: originalMatchId, // Same ID so they sync to the same match
          competition: originalMatch.competition,
          venue: originalMatch.venue,
          matchDate: originalMatch.matchDate,
          officialRole: 'Assistant', // Default role, will be updated by participant role
          homeTeamName: originalMatch.homeTeamName,
          homeTeamShortName: originalMatch.homeTeamShortName,
          homeKitBase: originalMatch.homeKitBase,
          homeKitStripe: originalMatch.homeKitStripe,
          awayTeamName: originalMatch.awayTeamName,
          awayTeamShortName: originalMatch.awayTeamShortName,
          awayKitBase: originalMatch.awayKitBase,
          awayKitStripe: originalMatch.awayKitStripe,
          teamSize: originalMatch.teamSize,
          numberOfPeriods: originalMatch.numberOfPeriods,
          periodLengths: originalMatch.periodLengths as any,
          halfTime: originalMatch.halfTime,
          extraTime: originalMatch.extraTime,
          extraTimeLengths: originalMatch.extraTimeLengths as any,
          penalties: originalMatch.penalties,
          matchOfficials: originalMatch.matchOfficials as any,
          fees: originalMatch.fees,
          expenses: originalMatch.expenses,
          mileage: originalMatch.mileage,
          travelTime: originalMatch.travelTime,
          notes: originalMatch.notes,
          templateName: originalMatch.templateName,
          summary: originalMatch.summary as any,
          status: originalMatch.status,
          refereeId: userId // Assign to the invited user
        }
      });

      this.logger.log(`📋 Created match copy for user ${userId}`);
    } catch (error) {
      if (error.code === 'P2002') {
        // Match already exists for this user, which is fine
        this.logger.log(`📋 Match already exists for user ${userId}`);
        return;
      }
      this.logger.error(`❌ Failed to create match for user: ${error.message}`);
      throw error;
    }
  }

  /**
   * Broadcast match action to synced participants
   */
  async broadcastAction(matchId: string, actionData: {
    refereeId: string;
    actionType: string;
    actionData: any;
    timestamp: string;
  }) {
    try {
      this.logger.log(`📡 Broadcasting action to synced participants: ${actionData.actionType}`);

      // Get match participants
      const participants = await this.prisma.matchParticipant.findMany({
        where: {
          matchId,
          status: 'joined'
        },
        include: {
          user: true
        }
      });

      if (participants.length === 0) {
        this.logger.log(`📡 No synced participants found for match: ${matchId}`);
        return { actionId: null, participantCount: 0 };
      }

      // Generate unique action ID
      const actionId = `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Create notification for each participant
      const notification = {
        type: 'action_broadcast' as const,
        actionId,
        matchId,
        fromUserId: actionData.refereeId,
        data: {
          actionType: actionData.actionType,
          actionData: actionData.actionData,
          timestamp: actionData.timestamp,
          refereeId: actionData.refereeId
        }
      };

      // Send to Redis Stream for each participant (excluding the referee who initiated the action)
      for (const participant of participants) {
        // Skip sending notification to the referee who initiated the action
        if (participant.userId === actionData.refereeId) {
          this.logger.log(`📡 Skipping notification to referee ${participant.userId} (action initiator)`);
          continue;
        }

        const streamKey = `notifications:${participant.userId}`;

        await this.redis.xadd(
          streamKey,
          '*',
          'type', notification.type,
          'actionId', actionId,
          'matchId', matchId,
          'fromUserId', actionData.refereeId,
          'data', JSON.stringify(notification.data),
          'timestamp', new Date().toISOString()
        );

        this.logger.log(`📡 Action sent to user ${participant.userId} via Redis Stream`);
      }

      // Also broadcast via WebSocket (excluding the referee who initiated the action)
      for (const participant of participants) {
        // Skip sending notification to the referee who initiated the action
        if (participant.userId === actionData.refereeId) {
          continue;
        }

        await this.broadcastService.sendUserNotification(participant.userId, {
          type: 'action_broadcast',
          matchId,
          fromUserId: actionData.refereeId,
          data: notification.data
        });
      }

      this.logger.log(`✅ Action broadcasted to ${participants.length} participants`);

      return {
        actionId,
        participantCount: participants.length,
        participants: participants.map(p => ({
          userId: p.userId,
          role: p.role,
          name: p.user.name
        }))
      };

    } catch (error) {
      this.logger.error(`❌ Error broadcasting action: ${error.message}`);
      throw error;
    }
  }

  /**
   * Respond to a broadcasted action
   */
  async respondToAction(responseData: {
    actionId: string;
    userId: string;
    response: 'accept' | 'decline';
    matchId: string;
  }) {
    try {
      this.logger.log(`📝 Processing action response: ${responseData.response} for ${responseData.actionId}`);

      if (responseData.response === 'accept') {
        // If accepted, we would insert the action into the user's match log
        // This would require the original action data to be stored temporarily
        // For now, we'll just log the acceptance
        this.logger.log(`✅ User ${responseData.userId} accepted action ${responseData.actionId}`);

        // TODO: Insert action into user's personal match log
        // This would require storing the original action data temporarily
        // and then inserting it into the match results/log table

      } else {
        this.logger.log(`❌ User ${responseData.userId} declined action ${responseData.actionId}`);
      }

      // Notify the referee about the response
      const notification = {
        type: 'action_response' as const,
        actionId: responseData.actionId,
        matchId: responseData.matchId,
        fromUserId: responseData.userId,
        data: {
          response: responseData.response,
          userId: responseData.userId,
          actionId: responseData.actionId
        }
      };

      // Send response notification to referee via Redis Stream
      // We need to find the referee (match creator) for this match
      const match = await this.prisma.match.findUnique({
        where: { id: responseData.matchId },
        select: { refereeId: true }
      });

      if (match?.refereeId) {
        const streamKey = `notifications:${match.refereeId}`;

        await this.redis.xadd(
          streamKey,
          '*',
          'type', notification.type,
          'actionId', responseData.actionId,
          'matchId', responseData.matchId,
          'fromUserId', responseData.userId,
          'data', JSON.stringify(notification.data),
          'timestamp', new Date().toISOString()
        );

        // Also broadcast via WebSocket
        await this.broadcastService.sendUserNotification(match.refereeId, {
          type: 'action_broadcast',
          matchId: responseData.matchId,
          fromUserId: responseData.userId,
          data: notification.data
        });

        this.logger.log(`📡 Response notification sent to referee ${match.refereeId}`);
      }

      return {
        success: true,
        response: responseData.response,
        actionId: responseData.actionId
      };

    } catch (error) {
      this.logger.error(`❌ Error processing action response: ${error.message}`);
      throw error;
    }
  }

  /**
   * Broadcast sync update to all participants
   */
  async broadcastSyncUpdate(matchId: string, userId: string, role: string, status: string): Promise<void> {
    try {
      this.logger.log(`📡 Broadcasting sync update for match ${matchId}`);

      // Get all participants for this match
      const participants = await this.prisma.matchParticipant.findMany({
        where: { matchId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              displayName: true
            }
          }
        }
      });

      // Get match data to find referee
      const match = await this.prisma.match.findUnique({
        where: { id: matchId },
        select: { refereeId: true }
      });

      if (!match) {
        this.logger.error(`❌ Match ${matchId} not found for sync update`);
        return;
      }

      // Create notification for sync update
      const notification = {
        type: 'sync_update' as const,
        matchId,
        fromUserId: userId,
        data: {
          updatedUserId: userId,
          role,
          status,
          participants: participants.map(p => ({
            id: p.id,
            userId: p.userId,
            role: p.role,
            status: p.status,
            user: p.user
          }))
        }
      };

      // Send to all participants (excluding the user who just joined)
      const notifyUsers = participants
        .filter(p => p.userId !== userId)
        .map(p => p.userId);

      // Also notify the referee if they're not already in the participants list
      if (!notifyUsers.includes(match.refereeId)) {
        notifyUsers.push(match.refereeId);
      }

      // Send notifications
      for (const targetUserId of notifyUsers) {
        await this.broadcastService.sendUserNotification(targetUserId, notification);
      }

      this.logger.log(`📡 Sync update broadcasted to ${notifyUsers.length} users`);
    } catch (error) {
      this.logger.error('❌ Failed to broadcast sync update:', error);
    }
  }
}
