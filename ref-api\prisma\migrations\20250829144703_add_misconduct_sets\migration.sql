/*
  Warnings:

  - You are about to alter the column `competition` on the `matches` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(200)`.
  - You are about to alter the column `venue` on the `matches` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(200)`.
  - You are about to alter the column `officialRole` on the `matches` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(50)`.
  - You are about to alter the column `homeTeamName` on the `matches` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(100)`.
  - You are about to alter the column `homeTeamShortName` on the `matches` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(10)`.
  - You are about to alter the column `homeKitBase` on the `matches` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(7)`.
  - You are about to alter the column `homeKitStripe` on the `matches` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(7)`.
  - You are about to alter the column `awayTeamName` on the `matches` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(100)`.
  - You are about to alter the column `awayTeamShortName` on the `matches` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(10)`.
  - You are about to alter the column `awayKitBase` on the `matches` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(7)`.
  - You are about to alter the column `awayKitStripe` on the `matches` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(7)`.
  - You are about to alter the column `misconductCode` on the `matches` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(50)`.
  - You are about to alter the column `fees` on the `matches` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(50)`.
  - You are about to alter the column `expenses` on the `matches` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(50)`.
  - You are about to alter the column `mileage` on the `matches` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(50)`.
  - You are about to alter the column `travelTime` on the `matches` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(50)`.
  - You are about to alter the column `templateName` on the `matches` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(100)`.
  - You are about to alter the column `role` on the `squad_officials` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(50)`.
  - You are about to alter the column `name` on the `squad_officials` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(100)`.
  - You are about to alter the column `name` on the `squad_players` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(100)`.
  - You are about to alter the column `email` on the `users` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(255)`.
  - You are about to alter the column `name` on the `users` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(100)`.
  - You are about to alter the column `displayName` on the `users` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(100)`.
  - You are about to alter the column `photoURL` on the `users` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(500)`.
  - You are about to alter the column `firebaseUid` on the `users` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(128)`.
  - You are about to alter the column `provider` on the `users` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(50)`.
  - You are about to alter the column `country` on the `users` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(100)`.
  - You are about to alter the column `countryFA` on the `users` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(100)`.
  - You are about to alter the column `height` on the `users` table. The data in that column could be lost. The data in that column will be cast from `DoublePrecision` to `Real`.
  - The `refereeLevel` column on the `users` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to alter the column `username` on the `users` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(50)`.
  - You are about to alter the column `weight` on the `users` table. The data in that column could be lost. The data in that column will be cast from `DoublePrecision` to `Real`.

*/
-- CreateEnum
CREATE TYPE "PlayerType" AS ENUM ('PLAYERS', 'OFFICIALS');

-- AlterTable
ALTER TABLE "matches" ALTER COLUMN "competition" SET DATA TYPE VARCHAR(200),
ALTER COLUMN "venue" SET DATA TYPE VARCHAR(200),
ALTER COLUMN "officialRole" SET DEFAULT 'Referee',
ALTER COLUMN "officialRole" SET DATA TYPE VARCHAR(50),
ALTER COLUMN "homeTeamName" SET DATA TYPE VARCHAR(100),
ALTER COLUMN "homeTeamShortName" SET DATA TYPE VARCHAR(10),
ALTER COLUMN "homeKitBase" SET DATA TYPE VARCHAR(7),
ALTER COLUMN "homeKitStripe" SET DATA TYPE VARCHAR(7),
ALTER COLUMN "awayTeamName" SET DATA TYPE VARCHAR(100),
ALTER COLUMN "awayTeamShortName" SET DATA TYPE VARCHAR(10),
ALTER COLUMN "awayKitBase" SET DATA TYPE VARCHAR(7),
ALTER COLUMN "awayKitStripe" SET DATA TYPE VARCHAR(7),
ALTER COLUMN "misconductCode" SET DATA TYPE VARCHAR(50),
ALTER COLUMN "fees" SET DATA TYPE VARCHAR(50),
ALTER COLUMN "expenses" SET DATA TYPE VARCHAR(50),
ALTER COLUMN "mileage" SET DATA TYPE VARCHAR(50),
ALTER COLUMN "travelTime" SET DATA TYPE VARCHAR(50),
ALTER COLUMN "templateName" SET DATA TYPE VARCHAR(100);

-- AlterTable
ALTER TABLE "squad_officials" ALTER COLUMN "role" SET DATA TYPE VARCHAR(50),
ALTER COLUMN "name" SET DATA TYPE VARCHAR(100);

-- AlterTable
ALTER TABLE "squad_players" ALTER COLUMN "name" SET DATA TYPE VARCHAR(100);

-- AlterTable
ALTER TABLE "users" ALTER COLUMN "email" SET DATA TYPE VARCHAR(255),
ALTER COLUMN "name" SET DATA TYPE VARCHAR(100),
ALTER COLUMN "displayName" SET DATA TYPE VARCHAR(100),
ALTER COLUMN "photoURL" SET DATA TYPE VARCHAR(500),
ALTER COLUMN "firebaseUid" SET DATA TYPE VARCHAR(128),
ALTER COLUMN "provider" SET DATA TYPE VARCHAR(50),
ALTER COLUMN "country" SET DATA TYPE VARCHAR(100),
ALTER COLUMN "countryFA" SET DATA TYPE VARCHAR(100),
ALTER COLUMN "height" SET DATA TYPE REAL,
DROP COLUMN "refereeLevel",
ADD COLUMN     "refereeLevel" VARCHAR(50),
ALTER COLUMN "username" SET DATA TYPE VARCHAR(50),
ALTER COLUMN "weight" SET DATA TYPE REAL;

-- DropEnum
DROP TYPE "RefereeLevel";

-- CreateTable
CREATE TABLE "misconduct_sets" (
    "id" TEXT NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "playerType" "PlayerType" NOT NULL,
    "yellowCodes" JSONB NOT NULL,
    "redCodes" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "userId" TEXT NOT NULL,

    CONSTRAINT "misconduct_sets_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "misconduct_sets" ADD CONSTRAINT "misconduct_sets_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
