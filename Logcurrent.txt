

here is the loop  LOG  🔔 Processing auto period end for current match
 LOG  📋 Match Log: period_end {"data": {"finalTime": "0'", "period": 1, "periodName": "Period 1", "score": "0 - 0"}, "id": "log-1758018830260-0.23344736439659555", "realTime": 2025-09-16T10:33:50.261Z, "timestamp": 0, "type": "period_end"}
 LOG  📡 Broadcasting action to synced users: period_end
 LOG  📡 Broadcasting action: period_end for match: cmfmer7c00001mq0yc9r4u5o6
 LOG  🏁 Period End Debug: {"currentETPhase": null, "currentPeriod": 1, "extraTimeType": "undefined", "extraTimeValue": undefined, "hasExtraTime": false, "isExtraTime": false, "totalPeriods": 2}
 LOG  🕐 Starting half-time after Period 1
 LOG  📋 Match Log: halftime_start {"data": {"previousPeriod": 1, "type": "regular-halftime"}, "id": "log-1758018830389-0.7751152900392235", "realTime": 2025-09-16T10:33:50.389Z, "timestamp": 0, "type": "halftime_start"}        
 LOG  📡 Broadcasting action to synced users: halftime_start
 LOG  📡 Broadcasting action: halftime_start for match: cmfmer7c00001mq0yc9r4u5o6
 LOG  🚀 POST /broadcast/action 🔑    
 LOG  🚀 POST /broadcast/action 🔑    
 LOG  📧 Received sync invitation: {"data": {"actionData": {"eventId": "log-1758018802226-0.9655331303701923", "finalTime": "0'", "period": 1, "periodName": "Period 1", "realTime": "2025-09-16T10:33:22.226Z", "score": "0 - 0", "timestamp": 0}, "actionType": "period_end", "refereeId": "cmfh65j950000mq0ykmu7i19v", "timestamp": "2025-09-16T10:33:22.262Z"}, "fromUserId": "cmfh65j950000mq0ykmu7i19v", "matchId": "cmfmer7c00001mq0yc9r4u5o6", "type": "action_broadcast"}
 LOG  🔔 WebSocket sync_invite event received: {"data": {"actionData": {"eventId": "log-1758018802226-0.9655331303701923", "finalTime": "0'", "period": 
1, "periodName": "Period 1", "realTime": "2025-09-16T10:33:22.226Z", "score": "0 - 0", "timestamp": 0}, "actionType": "period_end", "refereeId": "cmfh65j950000mq0ykmu7i19v", "timestamp": "2025-09-16T10:33:22.262Z"}, "fromUserId": "cmfh65j950000mq0ykmu7i19v", "matchId": "cmfmer7c00001mq0yc9r4u5o6", "type": "action_broadcast"}
 LOG  📧 Received sync invitation: {"data": {"actionData": {"eventId": "log-1758018802226-0.9655331303701923", "finalTime": "0'", "period": 1, "periodName": "Period 1", "realTime": "2025-09-16T10:33:22.226Z", "score": "0 - 0", "timestamp": 0}, "actionType": "period_end", "refereeId": "cmfh65j950000mq0ykmu7i19v", "timestamp": "2025-09-16T10:33:22.262Z"}, "fromUserId": "cmfh65j950000mq0ykmu7i19v", "matchId": "cmfmer7c00001mq0yc9r4u5o6", "type": "action_broadcast"}
 LOG  🔔 NotificationService received 
notification: {"data": {"actionData": 
{"eventId": "log-1758018802226-0.9655331303701923", "finalTime": "0'", "period": 1, "periodName": "Period 1", "realTime": "2025-09-16T10:33:22.226Z", "score": "0 - 0", "timestamp": 0}, "actionType": "period_end", "refereeId": "cmfh65j950000mq0ykmu7i19v", "timestamp": "2025-09-16T10:33:22.262Z"}, "fromUserId": "cmfh65j950000mq0ykmu7i19v", "matchId": "cmfmer7c00001mq0yc9r4u5o6", 
"type": "action_broadcast"}
 LOG  🔔 Active listeners count: 1    
 LOG  🔔 Calling listener 0 (app)     
 LOG  📱 App received notification: {"data": {"actionData": {"eventId": "log-1758018802226-0.9655331303701923", "finalTime": "0'", "period": 1, "periodName": "Period 1", "realTime": "2025-09-16T10:33:22.226Z", "score": "0 - 0", 
"timestamp": 0}, "actionType": "period_end", "refereeId": "cmfh65j950000mq0ykmu7i19v", "timestamp": "2025-09-16T10:33:22.262Z"}, "fromUserId": "cmfh65j950000mq0ykmu7i19v", "matchId": "cmfmer7c00001mq0yc9r4u5o6", "type": "action_broadcast"}
 LOG  📱 Notification type: action_broadcast
 LOG  📱 Current modal state: false   
 LOG  📱 Action broadcast notification received
 LOG  📱 Action data: {"actionData": {"eventId": "log-1758018802226-0.9655331303701923", "finalTime": "0'", "period": 1, "periodName": "Period 1", "realTime": "2025-09-16T10:33:22.226Z", "score": "0 - 0", "timestamp": 0}, "actionType": "period_end", "refereeId": "cmfh65j950000mq0ykmu7i19v", "timestamp": "2025-09-16T10:33:22.262Z"}
 LOG  📱 Period end notification - handling automatically
 LOG  🔔 Auto period end received: {"actionData": {"eventId": "log-1758018802226-0.9655331303701923", "finalTime": "0'", "period": 1, "periodName": "Period 1", "realTime": "2025-09-16T10:33:22.226Z", "score": "0 - 0", "timestamp": 0}, "matchId": "cmfmer7c00001mq0yc9r4u5o6"}
 LOG  🔔 Processing auto period end for current match
