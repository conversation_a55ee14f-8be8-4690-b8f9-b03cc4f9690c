import React, { useState, Dispatch, SetStateAction, useEffect, ReactNode, useRef } from 'react';
import {
  View, Text, ScrollView, TouchableOpacity, StyleSheet, Platform,
  TextInput, LayoutAnimation, UIManager, SafeAreaView, KeyboardTypeOptions,
  Modal, KeyboardAvoidingView, Alert, ActivityIndicator,
} from 'react-native';
import Svg, { Path, Circle, Defs, ClipPath, G, Rect } from 'react-native-svg';
import { useNavigation } from '@react-navigation/native';
import Header from '../components/Header';
import { useTheme } from '../contexts/ThemeContext';
import { useUser } from '../contexts/UserContext';
import MatchService from '../api/matchService';
import TemplateService from '../api/templateService';

// --- Setup and Helper Definitions ---

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

// Type Definitions
interface SvgIconProps { color?: string; size?: number; }
interface Official { id: number; role: string; name: string; }
interface KitColors { base: string; stripe: string; }
interface StripedTshirtProps { baseColor: string; stripeColor: string; size?: number; }

// SVG Components
const ChevronDownIcon: React.FC<SvgIconProps> = ({ color, size = 18 }) => (<Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Path d="M6 9L12 15L18 9" stroke={color} strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" /></Svg>);
const ChevronUpIcon: React.FC<SvgIconProps> = ({ color, size = 18 }) => (<Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Path d="M18 15L12 9L6 15" stroke={color} strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" /></Svg>);
const PlusIcon: React.FC<SvgIconProps> = ({ color, size = 16 }) => (<Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Path d="M12 5V19M5 12H19" stroke={color} strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" /></Svg>);
const MinusIcon: React.FC<SvgIconProps> = ({ color, size = 16 }) => (<Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Path d="M5 12H19" stroke={color} strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" /></Svg>);
const UserPlusIcon: React.FC<SvgIconProps> = ({ color, size = 22 }) => (<Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Path d="M16 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /><Circle cx="8.5" cy="7" r="4" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /><Path d="M20 8v6M23 11h-6" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" /></Svg>);
const CloseCircleIcon: React.FC<SvgIconProps> = ({ color, size = 20 }) => (<Svg width={size} height={size} viewBox="0 0 24 24" fill="none"><Circle cx="12" cy="12" r="10" stroke={color} strokeWidth="1.5" /><Path d="M15 9L9 15M9 9L15 15" stroke={color} strokeWidth="1.5" strokeLinecap="round" /></Svg>);
const StripedTshirt: React.FC<StripedTshirtProps> = ({ baseColor, stripeColor, size = 36 }) => (
  <Svg width={size} height={size} viewBox="-1 0 19 19">
    <Defs><ClipPath id="tshirtClip"><Path d="m15.867 7.593-1.534.967a.544.544 0 0 1-.698-.118l-.762-.957v7.256a.476.476 0 0 1-.475.475h-7.79a.476.476 0 0 1-.475-.475V7.477l-.769.965a.544.544 0 0 1-.697.118l-1.535-.967a.387.387 0 0 1-.083-.607l2.245-2.492a2.814 2.814 0 0 1 2.092-.932h.935a2.374 2.374 0 0 0 4.364 0h.934a2.816 2.816 0 0 1 2.093.933l2.24 2.49a.388.388 0 0 1-.085.608z" /></ClipPath></Defs>
    <Path d="m15.867 7.593-1.534.967a.544.544 0 0 1-.698-.118l-.762-.957v7.256a.476.476 0 0 1-.475.475h-7.79a.476.476 0 0 1-.475-.475V7.477l-.769.965a.544.544 0 0 1-.697.118l-1.535-.967a.387.387 0 0 1-.083-.607l2.245-2.492a2.814 2.814 0 0 1 2.092-.932h.935a2.374 2.374 0 0 0 4.364 0h.934a2.816 2.816 0 0 1 2.093.933l2.24 2.49a.388.388 0 0 1-.085.608z" fill={baseColor} />
    <G clipPath="url(#tshirtClip)">{[3.5, 6, 8.5, 11, 13.5].map(x => (<Rect key={x} x={x} y="3" width="1.2" height="13" fill={stripeColor} />))}</G>
  </Svg>
);

// Constants
const colorPalette = ['#FFFFFF', '#000000', '#FF0000', '#0000FF', '#008000', '#FFFF00', '#FFA500', '#800080', '#FFC0CB', '#A52A2A', '#808080', '#00FFFF'];
let nextOfficialId = 0;
const generateId = () => { nextOfficialId += 1; return nextOfficialId; };
const CREATE_NEW_TEMPLATE = '__CREATE_NEW__';
// Templates are now loaded dynamically from the API

// Prop Interfaces
interface FormFieldRowProps { label: string; value?: string | number | null; placeholder?: string; children?: React.ReactNode; showArrow?: boolean; onPress?: () => void; isEditable?: boolean; onChangeText?: (text: string) => void; keyboardType?: KeyboardTypeOptions; autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters'; arrowIcon?: React.ReactNode; isAccordionHeader?: boolean; styles: any; theme: any; }
interface TeamColourSelectorProps { teamLabel: string; kitColors: KitColors; onPress: () => void; styles: any; }
interface KitColorPickerModalProps { visible: boolean; onClose: () => void; onSave: (colors: KitColors) => void; initialColors: KitColors; styles: any; modalStyles: any; }
interface PickerLikeRowProps { label: string; value: string; options?: string[]; onValueChange: (value: string) => void; styles: any; theme: any; }
interface NumericInputRowProps { label: string; value: number; onValueChange: (val: number) => void; styles: any; theme: any; }
interface DateTimePickerModalProps { visible: boolean; onClose: () => void; onSave: (date: Date, time: { hours: number; minutes: number }) => void; initialDate: Date; initialTime: { hours: number; minutes: number }; styles: any; modalStyles: any; }

// Reusable Components
const FormFieldRow: React.FC<FormFieldRowProps> = ({ label, value, placeholder, children, showArrow, onPress, isEditable, onChangeText, keyboardType, autoCapitalize, arrowIcon, isAccordionHeader, styles, theme }) => (<TouchableOpacity style={[styles.inputField, isAccordionHeader && styles.accordionHeaderField]} activeOpacity={onPress && !isEditable ? 0.7 : 1} onPress={isEditable ? undefined : onPress}><Text style={styles.inputLabel}>{label}</Text><View style={styles.inputValueContainer}>{isEditable ? (<TextInput style={styles.textInput} value={value?.toString()} onChangeText={onChangeText} placeholder={placeholder} placeholderTextColor="#A0A0A0" keyboardType={keyboardType} autoCapitalize={autoCapitalize} />) : children ? children : (<Text style={value || value === 0 ? styles.inputValueText : styles.inputPlaceholderTextStatic}>{value !== undefined && value !== null ? String(value) : placeholder}</Text>)}{(showArrow || isAccordionHeader) && (arrowIcon || <ChevronDownIcon color={theme.text} />)}</View></TouchableOpacity>);
const PickerLikeRow: React.FC<PickerLikeRowProps> = ({ label, value, options = ['Yes', 'No'], onValueChange, styles, theme }) => { const cycleValue = () => { const currentIndex = options.indexOf(value); const nextIndex = (currentIndex + 1) % options.length; onValueChange(options[nextIndex]); }; return <FormFieldRow label={label} value={value} onPress={cycleValue} showArrow={true} styles={styles} theme={theme} />; };

// Dropdown Component
interface DropdownRowProps {
  label: string;
  value: string;
  options: string[];
  onValueChange: (value: string) => void;
  styles: any;
  theme: any;
}

const DropdownRow: React.FC<DropdownRowProps> = ({ label, value, options, onValueChange, styles, theme }) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleSelect = (selectedValue: string) => {
    onValueChange(selectedValue);
    setIsOpen(false);
  };

  return (
    <View>
      <TouchableOpacity
        style={styles.inputField}
        onPress={() => setIsOpen(!isOpen)}
        activeOpacity={0.7}
      >
        <Text style={styles.inputLabel}>{label}</Text>
        <View style={styles.inputValueContainer}>
          <Text style={styles.inputValueText}>{value}</Text>
          <ChevronDownIcon color={theme.text} />
        </View>
      </TouchableOpacity>

      {isOpen && (
        <View style={styles.dropdownContainer}>
          {options.map((option, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.dropdownItem,
                index === options.length - 1 && styles.dropdownItemLast
              ]}
              onPress={() => handleSelect(option)}
              activeOpacity={0.7}
            >
              <Text style={[
                styles.dropdownItemText,
                value === option && styles.dropdownItemTextSelected
              ]}>
                {option}
              </Text>
              {value === option && (
                <Text style={styles.dropdownCheckmark}>✓</Text>
              )}
            </TouchableOpacity>
          ))}
        </View>
      )}
    </View>
  );
};
const NumericInputRow: React.FC<NumericInputRowProps> = ({ label, value, onValueChange, styles, theme }) => (<View style={styles.inputField}><Text style={styles.inputLabel}>{label}</Text><View style={styles.numericInputControls}><TouchableOpacity onPress={() => onValueChange(value - 1)} style={styles.numericButton}><MinusIcon size={14} color={theme.text} /></TouchableOpacity><Text style={styles.numericValueText}>{value}</Text><TouchableOpacity onPress={() => onValueChange(value + 1)} style={styles.numericButton}><PlusIcon size={14} color={theme.text} /></TouchableOpacity></View></View>);
const TeamColourSelector: React.FC<TeamColourSelectorProps> = ({ teamLabel, kitColors, onPress, styles }) => (<View style={styles.inputField}><Text style={styles.inputLabel}>{teamLabel}</Text><TouchableOpacity style={styles.kitIconWrapper} onPress={onPress}><StripedTshirt baseColor={kitColors.base} stripeColor={kitColors.stripe} size={28} /></TouchableOpacity></View>);
const KitColorPickerModal: React.FC<KitColorPickerModalProps> = ({ visible, onClose, onSave, initialColors, styles, modalStyles }) => {
  const [baseColor, setBaseColor] = useState(initialColors.base);
  const [stripeColor, setStripeColor] = useState(initialColors.stripe);
  const handleSave = () => { onSave({ base: baseColor, stripe: stripeColor }); onClose(); };
  useEffect(() => { setBaseColor(initialColors.base); setStripeColor(initialColors.stripe); }, [initialColors]);
  const renderColorOptions = (setColor: Dispatch<SetStateAction<string>>, selectedColor: string) => (<View style={modalStyles.colorGrid}>{colorPalette.map(color => (<TouchableOpacity key={color} style={[modalStyles.colorSwatch, { backgroundColor: color, borderWidth: selectedColor === color ? 2 : 1, borderColor: selectedColor === color ? styles.theme.primary : styles.theme.border }]} onPress={() => setColor(color)} />))}</View>);
  return (<Modal animationType="fade" transparent={true} visible={visible} onRequestClose={onClose}><View style={modalStyles.modalOverlay}><View style={modalStyles.modalContent}><Text style={modalStyles.modalTitle}>Select Kit Colours</Text><View style={modalStyles.pickerContainer}><View style={modalStyles.previewContainer}><StripedTshirt baseColor={baseColor} stripeColor={stripeColor} size={120} /></View><View style={modalStyles.palettesContainer}><Text style={modalStyles.paletteTitle}>Base Color</Text>{renderColorOptions(setBaseColor, baseColor)}<Text style={modalStyles.paletteTitle}>Stripe Color</Text>{renderColorOptions(setStripeColor, stripeColor)}</View></View><View style={modalStyles.buttonContainer}><TouchableOpacity style={[modalStyles.button, modalStyles.cancelButton]} onPress={onClose}><Text style={modalStyles.cancelButtonText}>Cancel</Text></TouchableOpacity><TouchableOpacity style={[modalStyles.button, modalStyles.saveButton]} onPress={handleSave}><Text style={modalStyles.saveButtonText}>Save</Text></TouchableOpacity></View></View></View></Modal>);
};

// Scrollable Picker Component
const ScrollablePicker: React.FC<{
  items: string[];
  selectedIndex: number;
  onValueChange: (index: number) => void;
  itemHeight: number;
  visibleItems: number;
  modalStyles: any;
}> = ({ items, selectedIndex, onValueChange, itemHeight, visibleItems, modalStyles }) => {
  const scrollViewRef = useRef<ScrollView>(null);
  const [isScrolling, setIsScrolling] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const y = selectedIndex * itemHeight;
    // Debounce the scroll-to action to avoid issues on initial render
    const timer = setTimeout(() => {
      scrollViewRef.current?.scrollTo({ y, animated: true });
    }, 50); // Small delay to allow the UI to update

    return () => clearTimeout(timer);
  }, [selectedIndex, itemHeight]);

  const handleMomentumScrollEnd = (event: any) => {
    const y = event.nativeEvent.contentOffset.y;
    const index = Math.round(y / itemHeight);

    if (index >= 0 && index < items.length && index !== selectedIndex) {
      onValueChange(index);
    }
    setIsScrolling(false);
  };

  const handleScrollBeginDrag = () => {
    setIsScrolling(true);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  const handleScrollEndDrag = (event: any) => {
    // A timeout to reset isScrolling if momentum doesn't start
    timeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
    }, 100);
  };

  const containerHeight = visibleItems * itemHeight;
  const paddingVertical = (containerHeight - itemHeight) / 2;

  return (
    <View style={[modalStyles.scrollablePickerContainer, { height: containerHeight }]}>
      <View style={[modalStyles.pickerOverlay, {
        top: paddingVertical,
        height: itemHeight,
      }]} pointerEvents="none" />
      <ScrollView
        ref={scrollViewRef}
        style={modalStyles.pickerScrollView}
        contentContainerStyle={{
          paddingVertical: paddingVertical
        }}
        showsVerticalScrollIndicator={false}
        decelerationRate="fast"
        snapToInterval={itemHeight}
        snapToAlignment="center"
        onMomentumScrollBegin={() => setIsScrolling(true)}
        onMomentumScrollEnd={handleMomentumScrollEnd}
        onScrollBeginDrag={handleScrollBeginDrag}
        onScrollEndDrag={handleScrollEndDrag}
        scrollEventThrottle={16} // Fires event at most once every 16ms
      >
        {items.map((item, index) => (
          <TouchableOpacity
            key={index}
            style={[
              modalStyles.pickerItem,
              { height: itemHeight },
            ]}
            onPress={() => {
              if (!isScrolling) {
                onValueChange(index);
              }
            }}
            activeOpacity={0.7}
          >
            <Text
              style={[
                modalStyles.pickerItemText,
                selectedIndex === index && modalStyles.pickerItemTextSelected
              ]}
            >
              {item}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const DateTimePickerModal: React.FC<DateTimePickerModalProps> = ({ visible, onClose, onSave, initialDate, initialTime, styles, modalStyles }) => {
  const [selectedDay, setSelectedDay] = useState(initialDate.getDate());
  const [selectedMonth, setSelectedMonth] = useState(initialDate.getMonth());
  const [selectedYear, setSelectedYear] = useState(initialDate.getFullYear());
  const [selectedHour, setSelectedHour] = useState(initialTime.hours);
  const [selectedMinute, setSelectedMinute] = useState(initialTime.minutes);

  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    setSelectedDay(initialDate.getDate());
    setSelectedMonth(initialDate.getMonth());
    setSelectedYear(initialDate.getFullYear());
    setSelectedHour(initialTime.hours);
    setSelectedMinute(initialTime.minutes);
  }, [initialDate, initialTime]);

  const handleSave = () => {
    const newDate = new Date(selectedYear, selectedMonth, selectedDay);
    const newTime = { hours: selectedHour, minutes: selectedMinute };
    onSave(newDate, newTime);
    onClose();
  };

  // Generate arrays for pickers
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 10 }, (_, i) => (currentYear + i).toString());
  const months = [
    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
  ];

  const getDaysInMonth = (month: number, year: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const daysInMonth = getDaysInMonth(selectedMonth, selectedYear);
  const days = Array.from({ length: daysInMonth }, (_, i) => (i + 1).toString());

  const hours = Array.from({ length: 24 }, (_, i) => {
    const hour = i === 0 ? 12 : i > 12 ? i - 12 : i;
    const period = i < 12 ? 'AM' : 'PM';
    return `${hour} ${period}`;
  });

  const minutes = Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0'));

  // Adjust selected day if it's beyond the days in the selected month
  useEffect(() => {
    const maxDays = getDaysInMonth(selectedMonth, selectedYear);
    if (selectedDay > maxDays) {
      setSelectedDay(maxDays);
    }
  }, [selectedMonth, selectedYear, selectedDay]);

  const formatPreview = () => {
    const date = new Date(selectedYear, selectedMonth, selectedDay);
    const dateStr = date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
    const timeStr = hours[selectedHour];
    const minuteStr = minutes[selectedMinute];
    return `${dateStr}, ${timeStr.split(' ')[0]}:${minuteStr} ${timeStr.split(' ')[1]}`;
  };

  return (
    <Modal animationType="slide" transparent={true} visible={visible} onRequestClose={onClose}>
      <View style={modalStyles.modalOverlay}>
        <View style={modalStyles.dateTimeModalContent}>
          <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
            <Text style={modalStyles.modalTitle}>Select Date & Time</Text>

            {/* Preview */}
            <View style={modalStyles.previewSection}>
              <Text style={modalStyles.previewText}>{formatPreview()}</Text>
            </View>

            {/* Date Pickers */}
            <View style={modalStyles.datePickersContainer}>
              <Text style={modalStyles.sectionTitle}>Date</Text>
              <View style={modalStyles.datePickersRow}>
                <View style={modalStyles.pickerColumn}>
                  <Text style={modalStyles.pickerLabel}>Day</Text>
                  <ScrollablePicker
                    items={days}
                    selectedIndex={selectedDay - 1}
                    onValueChange={(index) => setSelectedDay(index + 1)}
                    itemHeight={35}
                    visibleItems={3}
                    modalStyles={modalStyles}
                  />
                </View>

                <View style={modalStyles.pickerColumn}>
                  <Text style={modalStyles.pickerLabel}>Month</Text>
                  <ScrollablePicker
                    items={months}
                    selectedIndex={selectedMonth}
                    onValueChange={setSelectedMonth}
                    itemHeight={35}
                    visibleItems={3}
                    modalStyles={modalStyles}
                  />
                </View>

                <View style={modalStyles.pickerColumn}>
                  <Text style={modalStyles.pickerLabel}>Year</Text>
                  <ScrollablePicker
                    items={years}
                    selectedIndex={selectedYear - currentYear}
                    onValueChange={(index) => setSelectedYear(currentYear + index)}
                    itemHeight={35}
                    visibleItems={3}
                    modalStyles={modalStyles}
                  />
                </View>
              </View>
            </View>

            {/* Time Pickers */}
            <View style={modalStyles.timePickersContainer}>
              <Text style={modalStyles.sectionTitle}>Time</Text>
              <View style={modalStyles.timePickersRow}>
                <View style={modalStyles.pickerColumn}>
                  <Text style={modalStyles.pickerLabel}>Hour</Text>
                  <ScrollablePicker
                    items={hours}
                    selectedIndex={selectedHour}
                    onValueChange={setSelectedHour}
                    itemHeight={35}
                    visibleItems={3}
                    modalStyles={modalStyles}
                  />
                </View>

                <View style={modalStyles.pickerColumn}>
                  <Text style={modalStyles.pickerLabel}>Minutes</Text>
                  <ScrollablePicker
                    items={minutes}
                    selectedIndex={selectedMinute}
                    onValueChange={setSelectedMinute}
                    itemHeight={35}
                    visibleItems={3}
                    modalStyles={modalStyles}
                  />
                </View>
              </View>
            </View>
          </ScrollView>

          <View style={modalStyles.buttonContainer}>
            <TouchableOpacity style={[modalStyles.button, modalStyles.cancelButton]} onPress={onClose}>
              <Text style={modalStyles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[modalStyles.button, modalStyles.saveButton]} onPress={handleSave}>
              <Text style={modalStyles.saveButtonText}>Save</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

// --- Form Content Component ---
interface MatchFormProps {
  editMode?: boolean;
  duplicateMode?: boolean;
  initialMatchData?: any;
  matchId?: string;
}

const MatchForm: React.FC<MatchFormProps> = ({
  editMode = false,
  duplicateMode = false,
  initialMatchData = null,
  matchId = null
}) => {
  const { theme, isDarkMode } = useTheme();
  const { userProfile } = useUser();
  const navigation = useNavigation();
  const styles = getStyles(theme, isDarkMode);
  const modalStyles = getModalStyles(theme);

  // State
  const [competition, setCompetition] = useState('');
  const [venue, setVenue] = useState('');
  const [matchDate, setMatchDate] = useState(new Date(Date.now() + 24 * 60 * 60 * 1000)); // Default to tomorrow
  const [matchTime, setMatchTime] = useState({ hours: 15, minutes: 0 }); // Default to 3:00 PM
  const [isDateTimePickerVisible, setIsDateTimePickerVisible] = useState(false);
  const [homeTeamName, setHomeTeamName] = useState('');
  const [homeTeamShortName, setHomeTeamShortName] = useState('');
  const [awayTeamName, setAwayTeamName] = useState('');
  const [awayTeamShortName, setAwayTeamShortName] = useState('');
  const [homeKitColors, setHomeKitColors] = useState<KitColors>({ base: '#FF0000', stripe: '#00FFFF' });
  const [awayKitColors, setAwayKitColors] = useState<KitColors>({ base: '#FFFFFF', stripe: '#0000FF' });
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingKitFor, setEditingKitFor] = useState<'home' | 'away' | null>(null);
  const [isTemplateSectionExpanded, setIsTemplateSectionExpanded] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [newTemplateName, setNewTemplateName] = useState('');
  const [isTemplatePickerVisible, setTemplatePickerVisible] = useState(false);
  const [availableTemplates, setAvailableTemplates] = useState<any[]>([]);
  const [loadingTemplates, setLoadingTemplates] = useState(false);
  const [numberOfPeriods, setNumberOfPeriods] = useState(2);
  const [periodLengths, setPeriodLengths] = useState<number[]>([45, 45]);
  const [extraTimeLengths, setExtraTimeLengths] = useState<[number, number]>([15, 15]);
  const [teamSize, setTeamSize] = useState(11);
  const [maxSubstitute, setMaxSubstitute] = useState(5);
  const [halfTime, setHalfTime] = useState(0);
  const [extraTime, setExtraTime] = useState('No');
  const [SubstituteOpportunities, setSubstituteOpportunities] = useState('No');
  const [SubstituteOpportunitiesAllowance, setSubstituteOpportunitiesAllowance] = useState(1);
  const [rollOnRollOff, setRollOnRollOff] = useState('No');
  const [extraTimeSubOpportunities, setExtraTimeSubOpportunities] = useState('No');
  const [extraTimeSubOpportunitiesAllowance, setExtraTimeSubOpportunitiesAllowance] = useState(1);
  const [extraTimeAdditionalSub, setExtraTimeAdditionalSub] = useState('No');
  const [extraTimeAdditionalSubAllowance, setExtraTimeAdditionalSubAllowance] = useState(1);
  const [penalties, setPenalties] = useState('No');
  const [misconductCode, setMisconductCode] = useState('Wales');
  const [misconductSets, setMisconductSets] = useState<string[]>(['Wales', 'England with FA Codes']);
  const [temporaryDismissals, setTemporaryDismissals] = useState('No');
  const [temporaryDismissalsTime, setTemporaryDismissalsTime] = useState(10);
  const [isEarningsExpanded, setIsEarningsExpanded] = useState(false);
  const [fees, setFees] = useState('');
  const [expenses, setExpenses] = useState('');
  const [mileage, setMileage] = useState('');
  const [travelTime, setTravelTime] = useState('');
  const [isNoteExpanded, setIsNoteExpanded] = useState(false);
  const [noteContent, setNoteContent] = useState('');
  const [isMatchOfficialsExpanded, setIsMatchOfficialsExpanded] = useState(false);
  const [officialRole, setOfficialRole] = useState('Referee');
  const [matchOfficialsList, setMatchOfficialsList] = useState<Official[]>([{ id: generateId(), role: 'Referee', name: 'Dave John' }, { id: generateId(), role: 'Ar 1', name: 'Example' }, { id: generateId(), role: 'Ar 2', name: 'Example' }, { id: generateId(), role: '4th', name: 'example' },]);
  const [injuryTimeAllowance, setInjuryTimeAllowance] = useState('No');
  const [injuryTime, setInjuryTime] = useState({ subs: 0, sanctions: 0, goals: 0 });

  // Loading state for form submission
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load misconduct sets and templates
  useEffect(() => {
    console.log('🔄 useEffect triggered for loading templates and misconduct sets');
    console.log('User profile ID:', userProfile?.id);
    if (userProfile?.id) {
      console.log('👤 User profile loaded, starting to load data...');
      loadMisconductSets();
      loadTemplates();
    } else {
      console.log('⚠️ User profile not loaded yet');
    }
  }, [userProfile?.id]);

  // Initialize template after templates are loaded and initial data is available
  useEffect(() => {
    if ((editMode || duplicateMode) && initialMatchData && availableTemplates.length > 0) {
      if (initialMatchData.templateName && !selectedTemplate) {
        setSelectedTemplate(initialMatchData.templateName);
      }
    }
  }, [editMode, duplicateMode, initialMatchData, availableTemplates, selectedTemplate]);

  // Apply template configuration when editing a match and template is available
  useEffect(() => {
    if ((editMode || duplicateMode) && selectedTemplate && availableTemplates.length > 0 && !loadingTemplates) {
      const template = availableTemplates.find(t => t.name === selectedTemplate);
      if (template) {
        console.log(`🎯 Applying template "${selectedTemplate}" configuration in edit mode`);
        
        // Don't apply template if it was manually applied via dropdown (avoid double application)
        // This prevents template from being applied when user manually selects it
        setTimeout(() => {
          const formData = TemplateService.applyTemplateToForm(template);

          // Apply template settings to form (only template-specific settings, not match details)
          setTeamSize(formData.teamSize);
          setMaxSubstitute(formData.maxSubstitute);
          setNumberOfPeriods(formData.numberOfPeriods);
          setPeriodLengths(formData.periodLengths);
          setHalfTime(formData.halfTime);
          setExtraTime(formData.extraTime);
          setExtraTimeLengths(formData.extraTimeLengths);
          setSubstituteOpportunities(formData.SubstituteOpportunities);
          setSubstituteOpportunitiesAllowance(formData.SubstituteOpportunitiesAllowance);
          setExtraTimeSubOpportunities(formData.extraTimeSubOpportunities);
          setExtraTimeSubOpportunitiesAllowance(formData.extraTimeSubOpportunitiesAllowance);
          setExtraTimeAdditionalSub(formData.extraTimeAdditionalSub || 'No');
          setExtraTimeAdditionalSubAllowance(formData.extraTimeAdditionalSubAllowance || 1);
          setRollOnRollOff(formData.rollOnRollOff || 'No');
          setPenalties(formData.penalties);
          setMisconductCode(formData.misconductCode);
          setTemporaryDismissals(formData.temporaryDismissals);
          setTemporaryDismissalsTime(formData.temporaryDismissalsTime);
          setInjuryTimeAllowance(formData.injuryTimeAllowance);
          setInjuryTime(formData.injuryTime);

          console.log(`✅ Template "${selectedTemplate}" configuration applied successfully`);
        }, 100); // Small delay to ensure form is initialized
      }
    }
  }, [editMode, duplicateMode, selectedTemplate, availableTemplates, loadingTemplates]);

  // Remove auto-update - templates should only be updated when creating/updating matches

  const loadMisconductSets = async () => {
    try {
      // Import the misconduct service
      const { default: MisconductService } = await import('../api/misconductService');
      const sets = await MisconductService.getAllMisconductSets(userProfile?.id);
      const setNames = sets.map(set => set.name);
      setMisconductSets(setNames);

      // If current misconduct code is not in the loaded sets, reset to first available
      if (setNames.length > 0 && !setNames.includes(misconductCode)) {
        setMisconductCode(setNames[0]);
      }
    } catch (error) {
      console.error('Error loading misconduct sets:', error);
      // Keep default sets if loading fails
      const defaultSets = ['Wales', 'England with FA Codes'];
      setMisconductSets(defaultSets);
      if (!defaultSets.includes(misconductCode)) {
        setMisconductCode(defaultSets[0]);
      }
    }
  };

  const loadTemplates = async () => {
    try {
      console.log('🔄 Loading templates for user:', userProfile?.id);
      setLoadingTemplates(true);
      const templates = await TemplateService.getAllTemplates(userProfile?.id);
      console.log('📝 Loaded templates:', templates.length, 'templates');
      console.log('📋 Template details:', templates.map(t => ({ id: t.id, name: t.name, userId: t.userId })));
      setAvailableTemplates(templates);
    } catch (error) {
      console.error('❌ Error loading templates:', error);
      setAvailableTemplates([]);
    } finally {
      setLoadingTemplates(false);
    }
  };

  const applyTemplate = (templateName: string) => {
    console.log(`📦 Attempting to apply template: "${templateName}"`);
    console.log('📋 Available templates:', availableTemplates.map(t => t.name));
    
    const template = availableTemplates.find(t => t.name === templateName);
    if (template) {
      console.log(`📦 Template found:`, template);
      const formData = TemplateService.applyTemplateToForm(template);
      console.log('📋 Form data from template:', formData);

      // Apply all template settings to form
      console.log('📄 Applying template settings...');
      setTeamSize(formData.teamSize);
      setMaxSubstitute(formData.maxSubstitute);
      setNumberOfPeriods(formData.numberOfPeriods);
      setPeriodLengths(formData.periodLengths);
      setHalfTime(formData.halfTime);
      setExtraTime(formData.extraTime);
      setExtraTimeLengths(formData.extraTimeLengths);
      setSubstituteOpportunities(formData.SubstituteOpportunities);
      setSubstituteOpportunitiesAllowance(formData.SubstituteOpportunitiesAllowance);
      setExtraTimeSubOpportunities(formData.extraTimeSubOpportunities);
      setExtraTimeSubOpportunitiesAllowance(formData.extraTimeSubOpportunitiesAllowance);
      setExtraTimeAdditionalSub(formData.extraTimeAdditionalSub || 'No');
      setExtraTimeAdditionalSubAllowance(formData.extraTimeAdditionalSubAllowance || 1);
      setRollOnRollOff(formData.rollOnRollOff || 'No');
      setPenalties(formData.penalties);
      setMisconductCode(formData.misconductCode);
      setTemporaryDismissals(formData.temporaryDismissals);
      setTemporaryDismissalsTime(formData.temporaryDismissalsTime);
      setInjuryTimeAllowance(formData.injuryTimeAllowance);
      setInjuryTime(formData.injuryTime);

      setSelectedTemplate(templateName);
      setTemplatePickerVisible(false);
      console.log(`✅ Template "${templateName}" applied successfully`);
      
      // Log the final state values being set
      console.log('🔍 Final applied values:');
      console.log('- Team Size:', formData.teamSize);
      console.log('- Max Substitute:', formData.maxSubstitute);
      console.log('- Number of Periods:', formData.numberOfPeriods);
      console.log('- Period Lengths:', formData.periodLengths);
      console.log('- Half Time:', formData.halfTime);
    } else {
      console.error(`❌ Template "${templateName}" not found in available templates`);
    }
  };

  const updateExistingTemplate = async () => {
    if (!selectedTemplate || !userProfile?.id || selectedTemplate === CREATE_NEW_TEMPLATE) {
      return;
    }

    const template = availableTemplates.find(t => t.name === selectedTemplate);
    if (!template || template.userId === 'system') {
      // Can't update system templates
      return;
    }

    try {
      const currentFormData = {
        teamSize,
        maxSubstitute,
        numberOfPeriods,
        periodLengths,
        halfTime,
        extraTime,
        extraTimeLengths,
        SubstituteOpportunities,
        SubstituteOpportunitiesAllowance,
        extraTimeSubOpportunities,
        extraTimeSubOpportunitiesAllowance,
        extraTimeAdditionalSub,
        extraTimeAdditionalSubAllowance,
        rollOnRollOff,
        penalties,
        misconductCode,
        temporaryDismissals,
        temporaryDismissalsTime,
        injuryTimeAllowance,
        injuryTime,
      };

      const templateData = TemplateService.convertFormDataToTemplate(
        currentFormData,
        selectedTemplate,
        userProfile.id
      );

      // Remove name and userId from update data
      const { name, userId, ...updateData } = templateData;

      // Use template ID instead of name to avoid URL encoding issues
      const response = await TemplateService.updateTemplate(template.id, updateData);

      // Handle both ApiResponse format and direct data format
      let isSuccess = false;
      if (response.success !== undefined) {
        isSuccess = response.success;
        if (!isSuccess) {
          throw new Error(response.error || 'Failed to update template');
        }
      } else {
        isSuccess = true;
      }

      if (isSuccess) {
        // Reload templates to get updated data
        await loadTemplates();
      }
    } catch (error) {
      console.error('Error updating template:', error);
      Alert.alert('Error', 'Failed to update template');
    }
  };

  const saveTemplate = async () => {
    console.log('💾 Attempting to save template:', newTemplateName);
    
    if (!newTemplateName.trim()) {
      console.log('❌ Template name is empty');
      Alert.alert('Error', 'Please enter a template name');
      return;
    }

    if (!userProfile?.id) {
      console.log('❌ User profile not loaded');
      Alert.alert('Error', 'User profile not loaded');
      return;
    }

    try {
      const currentFormData = {
        teamSize,
        maxSubstitute,
        numberOfPeriods,
        periodLengths,
        halfTime,
        extraTime,
        extraTimeLengths,
        SubstituteOpportunities,
        SubstituteOpportunitiesAllowance,
        extraTimeSubOpportunities,
        extraTimeSubOpportunitiesAllowance,
        extraTimeAdditionalSub,
        extraTimeAdditionalSubAllowance,
        rollOnRollOff,
        penalties,
        misconductCode,
        temporaryDismissals,
        temporaryDismissalsTime,
        injuryTimeAllowance,
        injuryTime,
      };

      console.log('📋 Current form data for template:', currentFormData);

      const templateData = TemplateService.convertFormDataToTemplate(
        currentFormData,
        newTemplateName,
        userProfile.id
      );

      console.log('📦 Template data to be sent:', templateData);

      const response = await TemplateService.createTemplate(templateData);
      console.log('💾 Template creation response:', response);

      // Handle both ApiResponse format and direct data format
      let isSuccess = false;
      if (response.success !== undefined) {
        // ApiResponse format
        isSuccess = response.success;
        console.log('💾 ApiResponse format, success:', isSuccess);
        if (!isSuccess) {
          throw new Error(response.error || 'Failed to save template');
        }
      } else {
        // Direct data format (response is the data itself)
        isSuccess = true;
        console.log('💾 Direct data format, assuming success');
      }

      if (isSuccess) {
        console.log('✅ Template saved successfully!');
        Alert.alert('Success', 'Template saved successfully!');
        setNewTemplateName('');
        setSelectedTemplate(newTemplateName);
        setIsTemplateSectionExpanded(false);

        // Reload templates
        console.log('🔄 Reloading templates after save...');
        await loadTemplates();
      }
    } catch (error) {
      console.error('❌ Error saving template:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      Alert.alert('Error', `Failed to save template: ${errorMessage}`);
    }
  };

  // Initialize form with existing match data for edit mode
  useEffect(() => {
    if ((editMode || duplicateMode) && initialMatchData) {
      console.log('Form initialization - initialMatchData:', initialMatchData);
      console.log('Form initialization - templateName:', initialMatchData.templateName);
      
      setCompetition(initialMatchData.competition || '');
      setVenue(initialMatchData.venue || '');

      // Set date and time from existing match data
      if (initialMatchData.matchDate) {
        const existingDate = new Date(initialMatchData.matchDate);
        setMatchDate(existingDate);
        setMatchTime({
          hours: existingDate.getHours(),
          minutes: existingDate.getMinutes()
        });
      }

      setHomeTeamName(initialMatchData.homeTeam?.name || '');
      setHomeTeamShortName(initialMatchData.homeTeam?.shortName || '');
      setAwayTeamName(initialMatchData.awayTeam?.name || '');
      setAwayTeamShortName(initialMatchData.awayTeam?.shortName || '');
      setHomeKitColors({
        base: initialMatchData.homeTeam?.kit?.base || '#FF0000',
        stripe: initialMatchData.homeTeam?.kit?.stripe || '#00FFFF'
      });
      setAwayKitColors({
        base: initialMatchData.awayTeam?.kit?.base || '#FFFFFF',
        stripe: initialMatchData.awayTeam?.kit?.stripe || '#0000FF'
      });
      setOfficialRole(initialMatchData.officialRole || 'Referee');
      setTeamSize(initialMatchData.teamSize || 11);
      setMaxSubstitute(initialMatchData.maxSubstitute || 5);
      setNumberOfPeriods(initialMatchData.numberOfPeriods || 2);
      setPeriodLengths(initialMatchData.periodLengths || [45, 45]);
      setHalfTime(initialMatchData.halfTime || 0);

      // Set all template-related settings
      setExtraTime(initialMatchData.extraTime ? 'Yes' : 'No');
      setExtraTimeLengths(initialMatchData.extraTimeLengths || [15, 15]);
      setSubstituteOpportunities(initialMatchData.substituteOpportunities ? 'Yes' : 'No');
      setSubstituteOpportunitiesAllowance(initialMatchData.substituteOpportunitiesAllowance || 1);
      setExtraTimeSubOpportunities(initialMatchData.extraTimeSubOpportunities ? 'Yes' : 'No');
      setExtraTimeSubOpportunitiesAllowance(initialMatchData.extraTimeSubOpportunitiesAllowance || 1);
      setExtraTimeAdditionalSub(initialMatchData.extraTimeAdditionalSub ? 'Yes' : 'No');
      setExtraTimeAdditionalSubAllowance(initialMatchData.extraTimeAdditionalSubAllowance || 1);
      setRollOnRollOff(initialMatchData.rollOnRollOff ? 'Yes' : 'No');
      setPenalties(initialMatchData.penalties ? 'Yes' : 'No');
      setTemporaryDismissals(initialMatchData.temporaryDismissals ? 'Yes' : 'No');
      setTemporaryDismissalsTime(initialMatchData.temporaryDismissalsTime || 10);
      setInjuryTimeAllowance(initialMatchData.injuryTimeAllowance ? 'Yes' : 'No');
      
      // Set injury time settings
      if (initialMatchData.injuryTime) {
        setInjuryTime({
          subs: initialMatchData.injuryTime.subs || 0,
          sanctions: initialMatchData.injuryTime.sanctions || 0,
          goals: initialMatchData.injuryTime.goals || 0,
        });
      }

      // Set misconduct code if available
      if (initialMatchData.misconductCode) {
        setMisconductCode(initialMatchData.misconductCode);
      }

      // Set match officials if available
      if (initialMatchData.matchOfficials && initialMatchData.matchOfficials.length > 0) {
        const officials = initialMatchData.matchOfficials.map((official: any, index: number) => ({
          id: generateId(),
          role: official.role || '',
          name: official.name || ''
        }));
        setMatchOfficialsList(officials);
      }

      // Set earnings data
      if (initialMatchData.earnings) {
        setFees(initialMatchData.earnings.fees || '');
        setExpenses(initialMatchData.earnings.expenses || '');
        setMileage(initialMatchData.earnings.mileage || '');
        setTravelTime(initialMatchData.earnings.travelTime || '');
      }

      // Set notes
      setNoteContent(initialMatchData.notes || '');
    }
  }, [editMode, duplicateMode, initialMatchData]);

  useEffect(() => {
    const count = numberOfPeriods;
    if (count > 0 && count !== periodLengths.length) {
      const newLengths = Array.from({ length: count }, (_, i) => periodLengths[i] || 45);
      setPeriodLengths(newLengths);
    }
  }, [numberOfPeriods]);

  // Handlers
  const openColorPicker = (team: 'home' | 'away') => { setEditingKitFor(team); setIsModalVisible(true); };
  const handleSaveKitColors = (newColors: KitColors) => { if (editingKitFor === 'home') { setHomeKitColors(newColors); } else if (editingKitFor === 'away') { setAwayKitColors(newColors); } };
  const openDateTimePicker = () => { setIsDateTimePickerVisible(true); };
  const handleSaveDateTime = (date: Date, time: { hours: number; minutes: number }) => { setMatchDate(date); setMatchTime(time); };
  const formatDateTimeDisplay = () => {
    const dateStr = matchDate.toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit', year: 'numeric' });
    const period = matchTime.hours >= 12 ? 'PM' : 'AM';
    const displayHours = matchTime.hours === 0 ? 12 : matchTime.hours > 12 ? matchTime.hours - 12 : matchTime.hours;
    const timeStr = `${displayHours}:${matchTime.minutes.toString().padStart(2, '0')}${period}`;
    return `${dateStr}, ${timeStr}`;
  };
  const toggleSection = (expandedSetter: Dispatch<SetStateAction<boolean>>, isCurrentlyExpanded: boolean) => { LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut); expandedSetter(!isCurrentlyExpanded); };
  const handleAddOfficial = () => { LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut); setMatchOfficialsList([...matchOfficialsList, { id: generateId(), role: '', name: '' }]); };
  const handleRemoveOfficial = (idToRemove: number) => { LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut); setMatchOfficialsList(matchOfficialsList.filter(official => official.id !== idToRemove)); };
  const handleOfficialChange = (id: number, field: keyof Omit<Official, 'id'>, value: string) => { setMatchOfficialsList(matchOfficialsList.map(official => (official.id === id ? { ...official, [field]: value } : official))); };
  const getInitialColorsForModal = (): KitColors => { if (editingKitFor === 'home') return homeKitColors; if (editingKitFor === 'away') return awayKitColors; return { base: '#FFFFFF', stripe: '#000000' }; };

  // Form validation
  const validateForm = (): boolean => {
    if (!competition.trim()) {
      Alert.alert('Validation Error', 'Please enter a competition name');
      return false;
    }
    if (!venue.trim()) {
      Alert.alert('Validation Error', 'Please enter a venue');
      return false;
    }
    if (!homeTeamName.trim()) {
      Alert.alert('Validation Error', 'Please enter home team name');
      return false;
    }
    if (!awayTeamName.trim()) {
      Alert.alert('Validation Error', 'Please enter away team name');
      return false;
    }
    if (halfTime <= 0) {
      Alert.alert('Validation Error', 'Please set a half time timer duration (must be greater than 0 minutes)');
      return false;
    }
    return true;
  };

  // Submit handler
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      // Prepare form data
      const formData = {
        competition,
        venue,
        matchDate: (() => {
          const combinedDateTime = new Date(matchDate);
          combinedDateTime.setHours(matchTime.hours, matchTime.minutes, 0, 0);
          return combinedDateTime.toISOString();
        })(),
        officialRole,
        homeTeamName,
        homeTeamShortName,
        homeKitColors,
        awayTeamName,
        awayTeamShortName,
        awayKitColors,
        teamSize,
        maxSubstitute,
        numberOfPeriods,
        periodLengths,
        halfTime,
        extraTime,
        extraTimeLengths,
        SubstituteOpportunities,
        SubstituteOpportunitiesAllowance,
        extraTimeSubOpportunities,
        extraTimeSubOpportunitiesAllowance,
        extraTimeAdditionalSub,
        extraTimeAdditionalSubAllowance,
        rollOnRollOff,
        penalties,
        misconductCode,
        temporaryDismissals,
        temporaryDismissalsTime,
        injuryTimeAllowance,
        injuryTime,
        matchOfficialsList,
        fees,
        expenses,
        mileage,
        travelTime,
        noteContent,
        selectedTemplate: selectedTemplate,
        newTemplateName: newTemplateName
      };

      // Convert to API format
      const apiData = MatchService.convertFormDataToApiFormat(formData);

      let response;
      if (editMode && matchId) {
        // Update existing match
        response = await MatchService.updateMatch(matchId, apiData);
      } else {
        // Create new match (including duplicate)
        response = await MatchService.createMatch(apiData);
      }

      if (response.success) {
        // Save template if "Create New" was selected
        if (selectedTemplate === CREATE_NEW_TEMPLATE && newTemplateName.trim()) {
          try {
            await saveTemplate();
            // Update selectedTemplate to the new template name for future reference
            setSelectedTemplate(newTemplateName);
          } catch (templateError) {
            console.error('Template save error:', templateError);
            // Don't fail the match creation if template save fails
          }
        } else if (selectedTemplate && selectedTemplate !== CREATE_NEW_TEMPLATE) {
          // Update existing template with current settings
          try {
            await updateExistingTemplate();
          } catch (templateError) {
            console.error('Template update error:', templateError);
            // Don't fail the match creation if template updating fails
          }
        }

        const successMessage = editMode
          ? 'Match updated successfully'
          : duplicateMode
            ? 'Match duplicated successfully'
            : 'Match created successfully';

        Alert.alert(
          'Success!',
          successMessage,
          [
            {
              text: 'OK',
              onPress: () => navigation.goBack()
            }
          ]
        );
      } else {
        const errorMessage = editMode
          ? 'Failed to update match'
          : duplicateMode
            ? 'Failed to duplicate match'
            : 'Failed to create match';
        Alert.alert('Error', response.error || errorMessage);
      }
    } catch (error: any) {
      console.error('Submit error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <ScrollView contentContainerStyle={styles.scrollContentContainer} showsVerticalScrollIndicator={false} keyboardShouldPersistTaps="handled">
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>
            {editMode ? 'Edit Match' : duplicateMode ? 'Duplicate Match' : 'Create Match'}
          </Text>
          <FormFieldRow styles={styles} theme={theme} label="Competition" placeholder="Competition" isEditable={true} value={competition} onChangeText={setCompetition} autoCapitalize="words" />
          <FormFieldRow styles={styles} theme={theme} label="Venue" placeholder="Venue" isEditable={true} value={venue} onChangeText={setVenue} autoCapitalize="words" />
          <FormFieldRow styles={styles} theme={theme} label="Select date" value={formatDateTimeDisplay()} onPress={openDateTimePicker} showArrow={true} />
          <DropdownRow styles={styles} theme={theme} label="Official role" value={officialRole} onValueChange={setOfficialRole} options={['Referee', 'Ar 1', 'Ar2 2', '4th Official']} />
        </View>
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Match Format</Text>
          <View style={styles.subSectionWrapper}>
            <Text style={styles.subSectionTitle}>Home Team</Text>
            <FormFieldRow styles={styles} theme={theme} label="Team Name" placeholder="Home team" isEditable={true} value={homeTeamName} onChangeText={setHomeTeamName} autoCapitalize="words" />
            <FormFieldRow styles={styles} theme={theme} label="Short name" placeholder="HOM" isEditable={true} value={homeTeamShortName} onChangeText={setHomeTeamShortName} autoCapitalize="characters" />
            <TeamColourSelector styles={styles} teamLabel="Team Colour" kitColors={homeKitColors} onPress={() => openColorPicker('home')} />
          </View>
          <View style={styles.subSectionWrapper}>
            <Text style={styles.subSectionTitle}>Away Team</Text>
            <FormFieldRow styles={styles} theme={theme} label="Team Name" placeholder="Away team" isEditable={true} value={awayTeamName} onChangeText={setAwayTeamName} autoCapitalize="words" />
            <FormFieldRow styles={styles} theme={theme} label="Short name" placeholder="AWY" isEditable={true} value={awayTeamShortName} onChangeText={setAwayTeamShortName} autoCapitalize="characters" />
            <TeamColourSelector styles={styles} teamLabel="Team Colour" kitColors={awayKitColors} onPress={() => openColorPicker('away')} />
          </View>
        </View>
        <View style={styles.sectionContainer}>
          <FormFieldRow styles={styles} theme={theme} label="Select Template" onPress={() => toggleSection(setIsTemplateSectionExpanded, isTemplateSectionExpanded)} isAccordionHeader={true} arrowIcon={isTemplateSectionExpanded ? <ChevronUpIcon color={theme.text} /> : <ChevronDownIcon color={theme.text} />} />
          {isTemplateSectionExpanded && (
            <View style={styles.accordionContentContainer}>
              <View style={styles.templateInputContainer}>
                <TouchableOpacity
                  style={styles.inputField}
                  onPress={() => setTemplatePickerVisible(!isTemplatePickerVisible)}
                  activeOpacity={0.7}
                >
                  <Text style={styles.inputLabel}>Template</Text>
                  <View style={styles.inputValueContainer}>
                    <Text style={[styles.inputValueText, !selectedTemplate && styles.inputPlaceholderTextStatic]}>
                      {loadingTemplates ? 'Loading...' : selectedTemplate === CREATE_NEW_TEMPLATE ? 'Create New' : selectedTemplate || 'Select or Create'}
                    </Text>
                    <ChevronDownIcon color={theme.text} />
                  </View>
                </TouchableOpacity>
                {isTemplatePickerVisible && (
                  <View style={styles.templateDropdown}>
                    <TouchableOpacity 
                      style={[styles.dropdownItem, selectedTemplate === CREATE_NEW_TEMPLATE && styles.dropdownItemSelected]} 
                      onPress={() => { 
                        setSelectedTemplate(CREATE_NEW_TEMPLATE); 
                        setNewTemplateName(''); 
                        setTemplatePickerVisible(false); 
                      }}
                    >
                      <Text style={[styles.dropdownItemText, selectedTemplate === CREATE_NEW_TEMPLATE && styles.dropdownItemTextSelected]}>
                        + Create New
                      </Text>
                      {selectedTemplate === CREATE_NEW_TEMPLATE && (
                        <Text style={styles.dropdownCheckmark}>✓</Text>
                      )}
                    </TouchableOpacity>
                    {loadingTemplates ? (
                      <View style={styles.dropdownItem}>
                        <ActivityIndicator size="small" color={theme.primary} />
                        <Text style={styles.dropdownItemText}>Loading templates...</Text>
                      </View>
                    ) : (
                      (() => {
                        console.log('📝 Rendering template dropdown. Available templates:', availableTemplates.length);
                        console.log('📋 Template list:', availableTemplates.map(t => ({ name: t.name, id: t.id })));
                        if (availableTemplates.length === 0) {
                          console.log('⚠️ No templates available to render');
                          return (
                            <View style={styles.dropdownItem}>
                              <Text style={styles.dropdownItemText}>No templates available</Text>
                            </View>
                          );
                        }
                        return availableTemplates.map((template) => {
                          console.log('🔄 Rendering template:', template.name);
                          return (
                            <TouchableOpacity 
                              key={template.id} 
                              style={[styles.dropdownItem, selectedTemplate === template.name && styles.dropdownItemSelected]} 
                              onPress={() => { 
                                console.log('👆 Template clicked:', template.name);
                                applyTemplate(template.name); 
                              }}
                            >
                              <View style={styles.dropdownItemContent}>
                                <Text style={[styles.dropdownItemText, selectedTemplate === template.name && styles.dropdownItemTextSelected]}>
                                  {template.name}
                                </Text>
                                {template.userId !== 'system' && (
                                  <Text style={styles.customTemplateIndicator}>Custom</Text>
                                )}
                              </View>
                              {selectedTemplate === template.name && (
                                <Text style={styles.dropdownCheckmark}>✓</Text>
                              )}
                            </TouchableOpacity>
                          );
                        });
                      })()
                    )}
                  </View>
                )}
              </View>

              {selectedTemplate === CREATE_NEW_TEMPLATE && (<FormFieldRow styles={styles} theme={theme} label="New Template Name" placeholder="Enter template name" isEditable={true} value={newTemplateName} onChangeText={setNewTemplateName} autoCapitalize="words" />)}
              <FormFieldRow styles={styles} theme={theme} label="Team size" isEditable={true} value={String(teamSize)} onChangeText={(val: string) => setTeamSize(Number(val) || 0)} keyboardType="number-pad" />
              <PickerLikeRow styles={styles} theme={theme} label="Roll on Roll off" value={rollOnRollOff} onValueChange={setRollOnRollOff} />
              <FormFieldRow styles={styles} theme={theme} label="Max Substitute" isEditable={true} value={String(maxSubstitute)} onChangeText={(val: string) => setMaxSubstitute(Number(val) || 0)} keyboardType="number-pad" />
              <TouchableOpacity style={styles.inputField} activeOpacity={0.7} onPress={() => setSubstituteOpportunities(SubstituteOpportunities === 'Yes' ? 'No' : 'Yes')}><Text style={styles.longLabel}>Substitute opportunities</Text><View style={styles.pickerValueContainer}><Text style={styles.inputValueText}>{SubstituteOpportunities}</Text><ChevronDownIcon color={theme.text} /></View></TouchableOpacity>
              {SubstituteOpportunities === 'Yes' && (<NumericInputRow styles={styles} theme={theme} label="Substitute opportunities allowance" value={SubstituteOpportunitiesAllowance} onValueChange={(val) => setSubstituteOpportunitiesAllowance(val)} />)}
              <NumericInputRow styles={styles} theme={theme} label="Number of periods" value={numberOfPeriods} onValueChange={(newValue) => { if (newValue >= 1) setNumberOfPeriods(newValue); }} />
              {periodLengths.map((length, index) => (<NumericInputRow key={`period-${index}`} styles={styles} theme={theme} label={`Period ${index + 1} Length`} value={length} onValueChange={(newValue) => { const newLengths = [...periodLengths]; newLengths[index] = newValue >= 0 ? newValue : 0; setPeriodLengths(newLengths); }} />))}
              <NumericInputRow styles={styles} theme={theme} label="Half time timer (minutes)" value={halfTime} onValueChange={(newValue) => setHalfTime(newValue >= 0 ? newValue : 0)} />
              <PickerLikeRow styles={styles} theme={theme} label="Injury time allowance" value={injuryTimeAllowance} onValueChange={setInjuryTimeAllowance} />
              {injuryTimeAllowance === 'Yes' && (
                <View>
                  <NumericInputRow styles={styles} theme={theme} label="Subs (Seconds)" value={injuryTime.subs} onValueChange={(newValue) => setInjuryTime(prev => ({ ...prev, subs: Math.max(0, newValue) }))} />
                  <NumericInputRow styles={styles} theme={theme} label="Sanctions (Seconds)" value={injuryTime.sanctions} onValueChange={(newValue) => setInjuryTime(prev => ({ ...prev, sanctions: Math.max(0, newValue) }))} />
                  <NumericInputRow styles={styles} theme={theme} label="Goals (Seconds)" value={injuryTime.goals} onValueChange={(newValue) => setInjuryTime(prev => ({ ...prev, goals: Math.max(0, newValue) }))} />
                </View>
              )}
              <PickerLikeRow styles={styles} theme={theme} label="Extra Time" value={extraTime} onValueChange={setExtraTime} />
              {extraTime === 'Yes' && (
                <View>
                  <NumericInputRow styles={styles} theme={theme} label="ET Period 1 Length" value={extraTimeLengths[0]} onValueChange={(newValue) => setExtraTimeLengths([newValue >= 0 ? newValue : 0, extraTimeLengths[1]])} />
                  <NumericInputRow styles={styles} theme={theme} label="ET Period 2 Length" value={extraTimeLengths[1]} onValueChange={(newValue) => setExtraTimeLengths([extraTimeLengths[0], newValue >= 0 ? newValue : 0])} />
                </View>
              )}
              <TouchableOpacity style={styles.inputField} activeOpacity={0.7} onPress={() => setExtraTimeSubOpportunities(extraTimeSubOpportunities === 'Yes' ? 'No' : 'Yes')}><Text style={styles.longLabel}>Extra time additional sub opportunities</Text><View style={styles.pickerValueContainer}><Text style={styles.inputValueText}>{extraTimeSubOpportunities}</Text><ChevronDownIcon color={theme.text} /></View></TouchableOpacity>
              {extraTimeSubOpportunities === 'Yes' && (<NumericInputRow styles={styles} theme={theme} label="Extra time sub opportunities allowance" value={extraTimeSubOpportunitiesAllowance} onValueChange={(val) => setExtraTimeSubOpportunitiesAllowance(val)} />)}
              <TouchableOpacity style={styles.inputField} activeOpacity={0.7} onPress={() => setExtraTimeAdditionalSub(extraTimeAdditionalSub === 'Yes' ? 'No' : 'Yes')}><Text style={styles.longLabel}>Extra Time additional Sub</Text><View style={styles.pickerValueContainer}><Text style={styles.inputValueText}>{extraTimeAdditionalSub}</Text><ChevronDownIcon color={theme.text} /></View></TouchableOpacity>
              {extraTimeAdditionalSub === 'Yes' && (<NumericInputRow styles={styles} theme={theme} label="Extra Time additional Sub allowance" value={extraTimeAdditionalSubAllowance} onValueChange={(val) => setExtraTimeAdditionalSubAllowance(val)} />)}
              <PickerLikeRow styles={styles} theme={theme} label="Penalties" value={penalties} onValueChange={setPenalties} />
              <DropdownRow styles={styles} theme={theme} label="Misconduct code" value={misconductCode} onValueChange={setMisconductCode} options={misconductSets} />
              <PickerLikeRow styles={styles} theme={theme} label="Temporary dismissals" value={temporaryDismissals} onValueChange={setTemporaryDismissals} />
              {temporaryDismissals === 'Yes' && (<NumericInputRow styles={styles} theme={theme} label="Temporary dismissals time" value={temporaryDismissalsTime} onValueChange={(val) => setTemporaryDismissalsTime(val)} />)}
            </View>
          )}
          <View style={styles.matchOfficialsHeaderContainer}>
            <FormFieldRow styles={styles} theme={theme} label="Match officials" onPress={() => toggleSection(setIsMatchOfficialsExpanded, isMatchOfficialsExpanded)} isAccordionHeader={true} arrowIcon={isMatchOfficialsExpanded ? <ChevronUpIcon color={theme.text} /> : <ChevronDownIcon color={theme.text} />} />
            {isMatchOfficialsExpanded && (<TouchableOpacity onPress={handleAddOfficial} style={styles.addOfficialButton}><UserPlusIcon color={theme.primary} /></TouchableOpacity>)}
          </View>
          {isMatchOfficialsExpanded && (<View style={styles.accordionContentContainer}>{matchOfficialsList.map((official) => (<View key={official.id} style={styles.officialRow}><TextInput style={styles.officialRoleInput} value={official.role} onChangeText={(text) => handleOfficialChange(official.id, 'role', text)} placeholder="Role" placeholderTextColor="#A0A0A0" /><TextInput style={styles.officialNameInput} value={official.name} onChangeText={(text) => handleOfficialChange(official.id, 'name', text)} placeholder="Name or '-----'" placeholderTextColor="#A0A0A0" /><TouchableOpacity onPress={() => handleRemoveOfficial(official.id)} style={styles.removeOfficialButton}><CloseCircleIcon color={theme.text} /></TouchableOpacity></View>))}</View>)}
          <FormFieldRow styles={styles} theme={theme} label="Earnings" onPress={() => toggleSection(setIsEarningsExpanded, isEarningsExpanded)} isAccordionHeader={true} arrowIcon={isEarningsExpanded ? <ChevronUpIcon color={theme.text} /> : <ChevronDownIcon color={theme.text} />} />
          {isEarningsExpanded && (<View style={styles.accordionContentContainer}><FormFieldRow styles={styles} theme={theme} label="Fees" placeholder="Fees" isEditable={true} value={fees} onChangeText={setFees} /><FormFieldRow styles={styles} theme={theme} label="Expenses" placeholder="Expenses" isEditable={true} value={expenses} onChangeText={setExpenses} /><FormFieldRow styles={styles} theme={theme} label="Mileage" placeholder="Mileage" isEditable={true} value={mileage} onChangeText={setMileage} /><FormFieldRow styles={styles} theme={theme} label="Travel Time" placeholder="Travel Time" isEditable={true} value={travelTime} onChangeText={setTravelTime} /></View>)}
          <FormFieldRow styles={styles} theme={theme} label="Note" onPress={() => toggleSection(setIsNoteExpanded, isNoteExpanded)} isAccordionHeader={true} arrowIcon={isNoteExpanded ? <ChevronUpIcon color={theme.text} /> : <ChevronDownIcon color={theme.text} />} />
          {isNoteExpanded && (<View style={[styles.accordionContentContainer, styles.noteInputContainer]}><TextInput style={styles.noteTextInput} value={noteContent} onChangeText={setNoteContent} placeholder="Enter your note here..." placeholderTextColor="#A0A0A0" multiline={true} numberOfLines={4} /></View>)}
        </View>

        {/* Submit Button */}
        <View style={styles.submitButtonContainer}>
          <TouchableOpacity
            style={[styles.submitButton, isSubmitting && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <View style={styles.submitButtonContent}>
                <ActivityIndicator size="small" color="#FFFFFF" style={{ marginRight: 8 }} />
                <Text style={styles.submitButtonText}>
                  {editMode ? 'Updating Match...' : duplicateMode ? 'Duplicating Match...' : 'Creating Match...'}
                </Text>
              </View>
            ) : (
              <Text style={styles.submitButtonText}>
                {editMode ? 'Update Match' : duplicateMode ? 'Duplicate Match' : 'Create Match'}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
      <KitColorPickerModal visible={isModalVisible} onClose={() => setIsModalVisible(false)} onSave={handleSaveKitColors} initialColors={getInitialColorsForModal()} styles={{ theme }} modalStyles={modalStyles} />
      <DateTimePickerModal visible={isDateTimePickerVisible} onClose={() => setIsDateTimePickerVisible(false)} onSave={handleSaveDateTime} initialDate={matchDate} initialTime={matchTime} styles={{ theme }} modalStyles={modalStyles} />
    </>
  );
};

// --- Main Screen Component ---
const CreateMatchScreen = ({ route }: { route: any }) => {
  const { theme } = useTheme();
  const editMode = route?.params?.editMode || false;
  const duplicateMode = route?.params?.duplicateMode || false;
  const matchData = route?.params?.matchData || null;
  const matchId = route?.params?.matchId || null;

  const getTitle = () => {
    if (editMode) return "Edit Match";
    if (duplicateMode) return "Duplicate Match";
    return "Create Match";
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.card }}>
      <Header title={getTitle()} showBackButton={true} />
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <MatchForm
          editMode={editMode}
          duplicateMode={duplicateMode}
          initialMatchData={matchData}
          matchId={matchId}
        />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

// --- DYNAMIC STYLESHEETS ---
const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
  scrollContentContainer: { padding: 15, paddingBottom: 30, backgroundColor: theme.background },
  sectionContainer: {
    backgroundColor: theme.card,
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingTop: 12,
    paddingBottom: 7,
    marginBottom: 15,
    shadowColor: isDarkMode ? 'transparent' : "#000",
    shadowOffset: { width: 0, height: 1, },
    shadowOpacity: 0.10,
    shadowRadius: 2.00,
    elevation: 2,
    borderWidth: isDarkMode ? 1 : 0,
    borderColor: theme.border,
  },
  sectionTitle: { fontSize: 16, fontWeight: 'bold', color: theme.text, marginBottom: 10, paddingLeft: 3 },
  subSectionWrapper: { backgroundColor: `${theme.text}08`, borderRadius: 10, paddingHorizontal: 10, paddingTop: 10, paddingBottom: 5, marginBottom: 12 },
  subSectionTitle: { fontSize: 14, fontWeight: '600', color: theme.text, marginBottom: 8, paddingLeft: 3 },
  inputField: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', backgroundColor: 'transparent', borderRadius: 8, borderWidth: 1, borderColor: theme.border1, paddingHorizontal: 12, minHeight: 50, marginBottom: 10, },
  accordionHeaderField: {},
  inputLabel: { fontSize: 14, color: theme.text, fontWeight: '400', marginRight: 10, flexShrink: 1 },
  longLabel: { flex: 1, marginRight: 10, fontSize: 14, color: theme.text, fontWeight: '400', flexWrap: 'wrap' },
  pickerValueContainer: { flexDirection: 'row', alignItems: 'center', },
  inputValueContainer: { flex: 1, flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-end' },
  textInput: { flex: 1, fontSize: 14, color: theme.text, opacity: 0.8, textAlign: 'right', paddingVertical: Platform.OS === 'ios' ? 14 : 10, paddingHorizontal: 0 },
  inputValueText: { fontSize: 14, color: theme.text, opacity: 0.8, textAlign: 'right', },
  inputPlaceholderTextStatic: { fontSize: 14, color: '#A0A0A0', textAlign: 'right' },
  kitIconWrapper: { padding: 5, },
  accordionContentContainer: { paddingTop: 10, },
  templateInputContainer: { position: 'relative', zIndex: 10, },
  templateDropdown: { position: 'absolute', top: 52, left: 0, right: 0, backgroundColor: theme.card, borderColor: theme.border, borderWidth: 1, borderRadius: 8, shadowColor: "#000", shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.15, shadowRadius: 3.84, elevation: 5, },

  // Dropdown styles
  dropdownContainer: {
    backgroundColor: theme.card,
    borderColor: theme.border,
    borderWidth: 1,
    borderRadius: 8,
    marginTop: -10,
    marginBottom: 10,
    shadowColor: isDarkMode ? 'transparent' : "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: isDarkMode ? 0 : 0.15,
    shadowRadius: 3.84,
    elevation: isDarkMode ? 0 : 5,
    zIndex: 1000,
  },
  dropdownItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 14,
    borderBottomWidth: 1,
    borderBottomColor: theme.border,
  },
  dropdownItemLast: {
    borderBottomWidth: 0,
  },
  dropdownItemText: {
    fontSize: 14,
    color: theme.text,
    opacity: 0.8,
    flex: 1,
  },
  dropdownItemTextSelected: {
    color: theme.primary,
    fontWeight: '500',
    opacity: 1,
  },
  dropdownCheckmark: {
    fontSize: 16,
    color: theme.primary,
    fontWeight: 'bold',
  },
  dropdownItemSelected: {
    backgroundColor: theme.primary + '10', // 10% opacity
  },
  dropdownItemContent: {
    flex: 1,
    flexDirection: 'column',
  },
  numericInputControls: { flexDirection: 'row', alignItems: 'center' },
  numericButton: { padding: 8, marginHorizontal: 5, borderWidth: 1, borderColor: theme.border, borderRadius: 15, width: 30, height: 30, justifyContent: 'center', alignItems: 'center', },
  numericValueText: { fontSize: 14, color: theme.text, opacity: 0.8, minWidth: 30, textAlign: 'center', marginHorizontal: 5 },
  noteInputContainer: {},
  noteTextInput: { backgroundColor: `${theme.text}08`, borderColor: theme.border, borderWidth: 1, borderRadius: 8, padding: 12, fontSize: 14, color: theme.text, textAlignVertical: 'top', minHeight: 100, marginBottom: 10, },
  matchOfficialsHeaderContainer: { position: 'relative' },
  addOfficialButton: { position: 'absolute', right: 40, top: 0, bottom: 0, justifyContent: 'center', alignItems: 'center', paddingHorizontal: 5, zIndex: 1, },
  officialRow: { flexDirection: 'row', alignItems: 'center', backgroundColor: 'transparent', borderRadius: 8, borderWidth: 1, borderColor: theme.border, paddingHorizontal: 10, minHeight: 50, marginBottom: 8 },
  officialRoleInput: { flex: 0.8, fontSize: 14, color: theme.text, paddingVertical: Platform.OS === 'ios' ? 14 : 10, marginRight: 5 },
  officialNameInput: { flex: 1, fontSize: 14, color: theme.text, opacity: 0.8, textAlign: 'right', paddingVertical: Platform.OS === 'ios' ? 14 : 10, marginRight: 5 },
  removeOfficialButton: { padding: 8 },

  // Submit button styles
  submitButtonContainer: { paddingHorizontal: 15, paddingVertical: 20, paddingBottom: 30 },
  submitButton: {
    backgroundColor: theme.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: isDarkMode ? 'transparent' : '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: isDarkMode ? 0 : 0.1,
    shadowRadius: 4,
    elevation: isDarkMode ? 0 : 3,
  },
  submitButtonDisabled: { backgroundColor: theme.border, opacity: 0.6 },
  submitButtonContent: { flexDirection: 'row', alignItems: 'center' },
  submitButtonText: { fontSize: 16, fontWeight: '600', color: '#FFFFFF' },

  // Template styles
  customTemplateIndicator: {
    fontSize: 12,
    color: theme.primary,
    fontWeight: '500',
    opacity: 0.8,
  },
});

const getModalStyles = (theme: any) => StyleSheet.create({
  modalOverlay: { flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0, 0, 0, 0.6)' },
  modalContent: { width: '90%', backgroundColor: theme.card, borderRadius: 15, padding: 20, shadowColor: "#000", shadowOffset: { width: 0, height: 2 }, shadowOpacity: 0.25, shadowRadius: 4, elevation: 5, borderWidth: 1, borderColor: theme.border },
  modalTitle: { fontSize: 20, fontWeight: 'bold', color: theme.text, marginBottom: 20, textAlign: 'center' },
  pickerContainer: { flexDirection: 'row', marginBottom: 20, },
  previewContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 10, },
  palettesContainer: { flex: 1.5, paddingLeft: 15 },
  paletteTitle: { fontSize: 16, fontWeight: '600', color: theme.text, marginBottom: 8 },
  colorGrid: { flexDirection: 'row', flexWrap: 'wrap', marginBottom: 15 },
  colorSwatch: { width: 30, height: 30, borderRadius: 15, margin: 4, },
  buttonContainer: { flexDirection: 'row', justifyContent: 'space-between', paddingTop: 10, borderTopWidth: 1, borderTopColor: theme.border },
  button: { flex: 1, paddingVertical: 12, borderRadius: 8, alignItems: 'center', marginHorizontal: 5 },
  cancelButton: { backgroundColor: theme.border },
  cancelButtonText: { color: theme.text, fontSize: 16, fontWeight: 'bold' },
  saveButton: { backgroundColor: theme.primary },
  saveButtonText: { color: '#FFFFFF', fontSize: 16, fontWeight: 'bold' },

  // DateTime picker modal styles
  dateTimeModalContent: {
    width: '95%',
    maxHeight: '85%',
    backgroundColor: theme.card,
    borderRadius: 15,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    borderWidth: 1,
    borderColor: theme.border
  },

  // Preview section
  previewSection: {
    backgroundColor: `${theme.primary}15`,
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
    alignItems: 'center'
  },
  previewText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.text
  },

  // Date pickers container
  datePickersContainer: { marginBottom: 20 },
  datePickersRow: { flexDirection: 'row', justifyContent: 'space-between' },

  // Time pickers container
  timePickersContainer: { marginBottom: 20 },
  timePickersRow: { flexDirection: 'row', justifyContent: 'center' },

  // Section titles
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.text,
    marginBottom: 15,
    textAlign: 'center'
  },

  // Picker column styles
  pickerColumn: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 5
  },
  pickerLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.text,
    marginBottom: 10
  },

  // Scrollable picker styles
  scrollablePickerContainer: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.border,
    backgroundColor: theme.background,
    overflow: 'hidden',
    position: 'relative',
    width: '100%'
  },
  pickerOverlay: {
    position: 'absolute',
    top: '40%',
    left: 0,
    right: 0,
    height: 40,
    backgroundColor: `${theme.primary}20`,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: theme.primary,
    zIndex: 1,
    pointerEvents: 'none'
  },
  pickerScrollView: {
    flex: 1
  },
  pickerItem: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 10
  },
  pickerItemSelected: {
    backgroundColor: 'transparent'
  },
  pickerItemText: {
    fontSize: 16,
    color: theme.text,
    opacity: 0.6,
    textAlign: 'center'
  },
  pickerItemTextSelected: {
    fontSize: 18,
    fontWeight: '600',
    opacity: 1,
    color: theme.primary
  }
});

export default CreateMatchScreen;