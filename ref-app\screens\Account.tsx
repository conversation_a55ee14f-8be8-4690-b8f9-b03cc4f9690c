import React, { useState, useEffect } from 'react';
import {
    View, Text, ScrollView, TouchableOpacity, StyleSheet, TextInput, Modal, FlatList, SafeAreaView, Alert, ActivityIndicator
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import Header from '../components/Header';
import { countries, countryFas } from './data'; // Assumes data.ts is in the same folder
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import UserService, { UserProfile } from '../api/userService';

// --- Type Definitions for Component Props ---
interface ReadOnlyRowProps { label: string; value: string; styles: any; }
interface NavigationRowProps { label: string; isDestructive?: boolean; onPress?: () => void; styles: any; theme: any; }
interface SelectionDisplayRowProps { label: string; value: string; onPress: () => void; isDestructive?: boolean; styles: any; theme: any; }
interface InputRowProps {
    label: string;
    value: string;
    onValueChange: (text: string) => void;
    placeholder?: string;
    autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
    styles: any;
}
interface MeasurementRowProps {
    label: string;
    value: string;
    onValueChange: (text: string) => void;
    unit: string;
    onUnitChange: () => void;
    styles: any;
    theme: any;
}
interface SelectionRowProps { label: string; value: string; onPress: () => void; styles: any; theme: any; }
interface SelectionModalProps {
    visible: boolean;
    title: string;
    data: readonly string[];
    onSelect: (item: string) => void;
    onClose: () => void;
    styles: any;
}
interface SectionProps { title: string; children: React.ReactNode; styles: any; }


// --- Reusable Components ---
const Section: React.FC<SectionProps> = ({ title, children, styles }) => (
    <>
        <Text style={styles.sectionHeader}>{title}</Text>
        <View style={styles.sectionBox}>{children}</View>
    </>
);

const ReadOnlyRow: React.FC<ReadOnlyRowProps> = ({ label, value, styles }) => (
    <View style={styles.row}><Text style={styles.rowLabel}>{label}</Text><Text style={styles.rowValue}>{value}</Text></View>
);

const NavigationRow: React.FC<NavigationRowProps> = ({ label, isDestructive = false, onPress = () => { }, styles, theme }) => (
    <TouchableOpacity style={styles.row} activeOpacity={0.7} onPress={onPress}>
        <Text style={[styles.rowLabel, isDestructive && styles.destructiveText]}>{label}</Text>
        <MaterialIcons name="chevron-right" size={22} color={isDestructive ? '#d32f2f' : theme.text} style={{ opacity: isDestructive ? 1 : 0.5 }} />
    </TouchableOpacity>
);

const SelectionDisplayRow: React.FC<SelectionDisplayRowProps> = ({ label, value, onPress, isDestructive = false, styles, theme }) => (
    <TouchableOpacity style={styles.row} activeOpacity={0.7} onPress={onPress}>
        <Text style={[styles.rowLabel, isDestructive && styles.destructiveText]}>{label}</Text>
        <View style={styles.rowRight}><Text style={styles.rowValue}>{value}</Text><MaterialIcons name="chevron-right" size={22} color={isDestructive ? '#d32f2f' : theme.text} style={{ opacity: isDestructive ? 1 : 0.5 }} /></View>
    </TouchableOpacity>
);

const InputRow: React.FC<InputRowProps> = ({ label, value, onValueChange, placeholder, autoCapitalize = 'words', styles }) => (
    <View style={styles.row}>
        <Text style={styles.rowLabel}>{label}</Text>
        <TextInput style={styles.textInput} value={value} onChangeText={onValueChange} placeholder={placeholder || 'Enter text'} placeholderTextColor="#c7c7cd" autoCapitalize={autoCapitalize} />
    </View>
);

const MeasurementRow: React.FC<MeasurementRowProps> = ({ label, value, onValueChange, unit, onUnitChange, styles, theme }) => (
    <View style={styles.row}>
        <Text style={styles.rowLabel}>{label}</Text>
        <View style={styles.rowRight}>
            <TextInput style={styles.measurementInput} value={value} onChangeText={onValueChange} keyboardType="numeric" placeholder="0" placeholderTextColor="#c7c7cd" />
            <TouchableOpacity style={styles.unitButton} onPress={onUnitChange}>
                <Text style={styles.unitText}>{unit}</Text>
                <MaterialIcons name="unfold-more" size={20} color={theme.text} style={{ opacity: 0.7 }} />
            </TouchableOpacity>
        </View>
    </View>
);

const SelectionRow: React.FC<SelectionRowProps> = ({ label, value, onPress, styles, theme }) => (
    <TouchableOpacity style={styles.row} activeOpacity={0.7} onPress={onPress}>
        <Text style={styles.rowLabel}>{label}</Text>
        <View style={styles.rowRight}>
            <Text style={styles.rowValue}>{value}</Text>
            <MaterialIcons name="unfold-more" size={20} color={theme.text} style={{ opacity: 0.7 }} />
        </View>
    </TouchableOpacity>
);

const SelectionModal: React.FC<SelectionModalProps> = ({ visible, title, data, onSelect, onClose, styles }) => (
    <Modal animationType="slide" transparent={false} visible={visible} onRequestClose={onClose}>
        <SafeAreaView style={styles.modalContainer}>
            <View style={styles.modalHeader}><Text style={styles.modalTitle}>{title}</Text><TouchableOpacity onPress={onClose}><Text style={styles.modalCloseButton}>Done</Text></TouchableOpacity></View>
            <FlatList data={data} keyExtractor={(item) => item} renderItem={({ item }) => (
                <TouchableOpacity style={styles.modalItem} onPress={() => onSelect(item)}><Text style={styles.modalItemText}>{item}</Text></TouchableOpacity>
            )} />
        </SafeAreaView>
    </Modal>
);

// --- Main Account Screen Component ---
const Account = () => {
    const { theme, isDarkMode } = useTheme();
    const { user, signOut, deleteAccount } = useAuth();
    const styles = getStyles(theme, isDarkMode);

    // User profile state
    const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);

    // Form state
    const [firstName, setFirstName] = useState('');
    const [lastName, setLastName] = useState('');
    const [username, setUsername] = useState('');
    const [refereeLevel, setRefereeLevel] = useState('');
    const [weight, setWeight] = useState('');
    const [weightUnit, setWeightUnit] = useState('Kg');
    const weightUnits = ['Kg', 'Lb', 'St'];
    const [height, setHeight] = useState('');
    const [heightUnit, setHeightUnit] = useState('cm');
    const heightUnits = ['cm', 'm', 'ft', 'in'];
    const [gender, setGender] = useState('Male');
    const genderOptions = ['Male', 'Female'];
    const [selectedCountry, setSelectedCountry] = useState('United Kingdom');
    const [selectedFa, setSelectedFa] = useState('');

    // Modal states
    const [isCountryModalVisible, setCountryModalVisible] = useState(false);
    const [isFaModalVisible, setFaModalVisible] = useState(false);
    const [isDeleteModalVisible, setDeleteModalVisible] = useState(false);
    const [confirmationText, setConfirmationText] = useState('');
    const [isDeleting, setIsDeleting] = useState(false);

    // Load user profile on component mount
    useEffect(() => {
        loadUserProfile();
    }, []);

    const loadUserProfile = async () => {
        try {
            setLoading(true);
            const response = await UserService.getProfile();

            if (response.success && response.data) {
                const profile = response.data;
                setUserProfile(profile);

                // Parse name into first and last name
                const nameParts = profile.name.split(' ');
                setFirstName(nameParts[0] || '');
                setLastName(nameParts.slice(1).join(' ') || '');

                setUsername(profile.displayName || profile.name || '');

                // Load all profile fields
                setWeight(profile.weight ? profile.weight.toString() : '');
                setHeight(profile.height ? profile.height.toString() : '');

                // Map database enum values back to frontend display values
                const genderMap: { [key: string]: string } = {
                    'MALE': 'Male',
                    'FEMALE': 'Female',
                    'OTHER': 'Other',
                    'PREFER_NOT_TO_SAY': 'Prefer not to say'
                };
                setGender(profile.gender ? genderMap[profile.gender] || 'Male' : 'Male');

                setSelectedCountry(profile.country || 'United Kingdom');
                setSelectedFa(profile.countryFA || '');
                setRefereeLevel(profile.refereeLevel || '');
                setUsername(profile.username || profile.displayName || profile.name || '');
            } else {
                Alert.alert('Error', response.error || 'Failed to load profile');
            }
        } catch (error) {
            console.error('Error loading profile:', error);
            Alert.alert('Error', 'Failed to load profile');
        } finally {
            setLoading(false);
        }
    };

    const saveProfile = async () => {
        try {
            setSaving(true);

            // Combine first and last name
            const fullName = `${firstName.trim()} ${lastName.trim()}`.trim();

            // Update profile info with all fields
            const profileResponse = await UserService.updateProfile({
                name: fullName,
                displayName: username.trim(),
                username: username.trim(),
                weight: weight.trim(),
                height: height.trim(),
                gender: gender,
                country: selectedCountry,
                footballAssociation: selectedFa,
                refereeLevel: refereeLevel.trim(),
            });

            if (!profileResponse.success) {
                throw new Error(profileResponse.error || 'Failed to update profile');
            }



            // Reload profile to get updated data
            await loadUserProfile();
            Alert.alert('Success', 'Profile updated successfully');
        } catch (error: any) {
            console.error('Error saving profile:', error);
            Alert.alert('Error', error.message || 'Failed to save profile');
        } finally {
            setSaving(false);
        }
    };

    const handleSignOut = async () => {
        Alert.alert(
            'Sign Out',
            'Are you sure you want to sign out?',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Sign Out',
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            await signOut();
                        } catch (error) {
                            console.error('Sign out error:', error);
                        }
                    },
                },
            ]
        );
    };

    const handleDeleteAccount = () => {
        setDeleteModalVisible(true);
        setConfirmationText('');
    };

    const confirmDeleteAccount = async () => {
        if (confirmationText.toLowerCase() !== 'confirm') {
            Alert.alert('Error', 'Please type "confirm" to delete your account');
            return;
        }

        try {
            setIsDeleting(true);

            // First delete from our database
            const dbDeleteResponse = await UserService.deleteAccount();
            if (!dbDeleteResponse.success) {
                throw new Error(dbDeleteResponse.error || 'Failed to delete account from database');
            }

            // Then delete from Firebase Auth
            const authDeleteResponse = await deleteAccount();
            if (!authDeleteResponse.success) {
                // If requires recent login, prompt user to sign in again
                if (authDeleteResponse.error === 'auth/requires-recent-login') {
                    Alert.alert(
                        'Re-authentication Required',
                        'For security reasons, please sign out and sign back in, then try deleting your account again.',
                        [
                            { text: 'Cancel', style: 'cancel' },
                            {
                                text: 'Sign Out',
                                onPress: async () => {
                                    try {
                                        await signOut();
                                    } catch (error) {
                                        console.error('Sign out error:', error);
                                    }
                                }
                            }
                        ]
                    );
                    return;
                }
                throw new Error(authDeleteResponse.error || 'Failed to delete Firebase account');
            }

            setDeleteModalVisible(false);
            Alert.alert('Account Deleted', 'Your account has been permanently deleted.');
        } catch (error: any) {
            console.error('Delete account error:', error);
            Alert.alert('Error', error.message || 'Failed to delete account. Please try again.');
        } finally {
            setIsDeleting(false);
        }
    };

    const cycleUnit = (currentUnit: string, units: string[], setUnit: React.Dispatch<React.SetStateAction<string>>) => {
        const currentIndex = units.indexOf(currentUnit);
        const nextIndex = (currentIndex + 1) % units.length;
        setUnit(units[nextIndex]);
    };

    const handleSelectCountry = (country: string) => {
        setSelectedCountry(country);
        setSelectedFa('');
        setCountryModalVisible(false);
    };

    const handleSelectFa = (fa: string) => {
        setSelectedFa(fa);
        setFaModalVisible(false);
    };



    const availableFas = countryFas[selectedCountry];
    const securityItems = [{ label: '2FA' }, { label: 'Subscription' }, { label: 'Biometry' }, { label: 'Change password' }];

    // Show loading screen while fetching user data
    if (loading) {
        return (
            <View style={styles.container}>
                <Header title="Account" showBackButton={true} />
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={theme.primary} />
                    <Text style={styles.loadingText}>Loading profile...</Text>
                </View>
            </View>
        );
    }

    return (
        <View style={styles.container}>
            <Header
                title="Account"
                showBackButton={true}
                rightComponent={
                    <TouchableOpacity
                        onPress={saveProfile}
                        disabled={saving}
                        style={styles.saveButton}
                    >
                        {saving ? (
                            <ActivityIndicator size="small" color={theme.primary} />
                        ) : (
                            <Text style={styles.saveButtonText}>Save</Text>
                        )}
                    </TouchableOpacity>
                }
            />
            <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
                <View style={styles.avatarSection}>
                    <View style={styles.avatarCircle}><MaterialIcons name="photo-camera" size={40} color={theme.text} style={{ opacity: 0.5 }} /></View>
                    <Text style={styles.addPicText}>Add Picture</Text>
                </View>

                <Section title="Personal Info" styles={styles}>
                    <InputRow label="First Name" value={firstName} onValueChange={setFirstName} placeholder="Your first name" styles={styles} />
                    <InputRow label="Last Name" value={lastName} onValueChange={setLastName} placeholder="Your last name" styles={styles} />
                    <MeasurementRow label="Weight" value={weight} onValueChange={setWeight} unit={weightUnit} onUnitChange={() => cycleUnit(weightUnit, weightUnits, setWeightUnit)} styles={styles} theme={theme} />
                    <MeasurementRow label="Height" value={height} onValueChange={setHeight} unit={heightUnit} onUnitChange={() => cycleUnit(heightUnit, heightUnits, setHeightUnit)} styles={styles} theme={theme} />
                    <SelectionRow label="Gender" value={gender} onPress={() => cycleUnit(gender, genderOptions, setGender)} styles={styles} theme={theme} />
                    <InputRow label="Username" value={username} onValueChange={setUsername} autoCapitalize="none" placeholder="Your username" styles={styles} />
                    <NavigationRow label="Change Email address" styles={styles} theme={theme} />
                </Section>

                <Section title="Referee Details" styles={styles}>
                    <SelectionDisplayRow label="Country" value={selectedCountry || 'Select'} onPress={() => setCountryModalVisible(true)} styles={styles} theme={theme} />
                    {availableFas && (<SelectionDisplayRow label="Country FA" value={selectedFa || 'Select'} onPress={() => setFaModalVisible(true)} styles={styles} theme={theme} />)}
                    <InputRow label="Referee Level" value={refereeLevel} onValueChange={setRefereeLevel} placeholder="e.g. Level 4, Regional, FIFA" styles={styles} />
                </Section>

                <Section title="Security" styles={styles}>
                    {securityItems.map(item => <NavigationRow key={item.label} label={item.label} styles={styles} theme={theme} />)}
                    <ReadOnlyRow label="App Version" value="V 1.2.0" styles={styles} />
                </Section>

                <View style={[styles.sectionBox, { marginTop: 20 }]}>
                    <NavigationRow label="Sign Out" isDestructive={true} onPress={handleSignOut} styles={styles} theme={theme} />
                    <NavigationRow label="Delete Account" isDestructive={true} onPress={handleDeleteAccount} styles={styles} theme={theme} />
                </View>
            </ScrollView>

            <SelectionModal visible={isCountryModalVisible} title="Select a Country" data={countries} onSelect={handleSelectCountry} onClose={() => setCountryModalVisible(false)} styles={styles} />
            {availableFas && (<SelectionModal visible={isFaModalVisible} title="Select a Country FA" data={availableFas} onSelect={handleSelectFa} onClose={() => setFaModalVisible(false)} styles={styles} />)}

            {/* Delete Account Confirmation Modal */}
            <Modal
                animationType="slide"
                transparent={true}
                visible={isDeleteModalVisible}
                onRequestClose={() => setDeleteModalVisible(false)}
            >
                <View style={styles.deleteModalOverlay}>
                    <View style={styles.deleteModalContainer}>
                        <Text style={styles.deleteModalTitle}>Delete Account</Text>
                        <Text style={styles.deleteModalMessage}>
                            This action cannot be undone. All your data will be permanently deleted from our servers.
                        </Text>
                        <Text style={styles.deleteModalInstruction}>
                            Type "confirm" to delete your account:
                        </Text>
                        <TextInput
                            style={styles.deleteModalInput}
                            value={confirmationText}
                            onChangeText={setConfirmationText}
                            placeholder="Type confirm here"
                            placeholderTextColor="#c7c7cd"
                            autoCapitalize="none"
                        />
                        <View style={styles.deleteModalButtons}>
                            <TouchableOpacity
                                style={[styles.deleteModalButton, styles.cancelButton]}
                                onPress={() => setDeleteModalVisible(false)}
                                disabled={isDeleting}
                            >
                                <Text style={styles.cancelButtonText}>Cancel</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={[styles.deleteModalButton, styles.deleteButton]}
                                onPress={confirmDeleteAccount}
                                disabled={isDeleting || confirmationText.toLowerCase() !== 'confirm'}
                            >
                                {isDeleting ? (
                                    <ActivityIndicator size="small" color="#fff" />
                                ) : (
                                    <Text style={styles.deleteButtonText}>Delete Account</Text>
                                )}
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </Modal>
        </View>
    );
};

// --- DYNAMIC STYLES ---
const getStyles = (theme: any, isDarkMode: boolean) => StyleSheet.create({
    container: { flex: 1, backgroundColor: theme.background },
    scrollContent: { paddingHorizontal: 16, paddingBottom: 32 },
    avatarSection: { alignItems: 'center', marginTop: 8, marginBottom: 18 },
    avatarCircle: { width: 100, height: 100, borderRadius: 50, backgroundColor: theme.border, alignItems: 'center', justifyContent: 'center', marginBottom: 8 },
    addPicText: { fontWeight: '600', fontSize: 15, color: theme.text },
    sectionHeader: { fontWeight: '700', fontSize: 15, color: theme.text, marginTop: 10, marginBottom: 12, marginLeft: 4 },
    sectionBox: {
        backgroundColor: theme.card,
        borderRadius: 12,
        overflow: 'hidden',
        marginBottom: 18,
        borderWidth: 1,
        borderColor: theme.border1,
        elevation: isDarkMode ? 0 : 2
    },
    row: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingVertical: 14, paddingHorizontal: 16, backgroundColor: 'transparent', borderBottomWidth: 1, borderBottomColor: theme.border1 },
    rowLabel: { fontSize: 15, color: theme.text, fontWeight: '500' },
    rowRight: { flexDirection: 'row', alignItems: 'center' },
    rowValue: { fontSize: 15, color: theme.text, opacity: 0.7, fontWeight: '500', marginRight: 8 },
    destructiveText: { color: '#d32f2f' },
    textInput: { flex: 1, fontSize: 15, color: theme.text, fontWeight: '500', textAlign: 'right' },
    measurementInput: { fontSize: 15, color: theme.text, fontWeight: '500', textAlign: 'right', marginRight: 10, minWidth: 50 },
    unitButton: { flexDirection: 'row', alignItems: 'center', paddingVertical: 4, paddingHorizontal: 8, borderRadius: 6, backgroundColor: theme.border },
    unitText: { fontSize: 15, color: theme.text, fontWeight: '500', marginRight: 4 },
    modalContainer: { flex: 1, backgroundColor: theme.background },
    modalHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingHorizontal: 16, paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: theme.border },
    modalTitle: { fontSize: 20, fontWeight: '700', color: theme.text },
    modalCloseButton: { fontSize: 17, color: '#068B47', fontWeight: '600' }, // Standard iOS blue
    modalItem: { paddingVertical: 16, paddingHorizontal: 20, borderBottomWidth: 1, borderBottomColor: theme.border },
    modalItemText: { fontSize: 16, color: theme.text },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 60,
    },
    loadingText: {
        marginTop: 16,
        fontSize: 16,
        color: theme.text,
        opacity: 0.7,
    },
    saveButton: {
        padding: 4,
    },
    saveButtonText: {
        fontSize: 16,
        fontWeight: '600',
        color: theme.primary,
    },
    deleteModalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 20,
    },
    deleteModalContainer: {
        backgroundColor: theme.card,
        borderRadius: 12,
        padding: 24,
        width: '100%',
        maxWidth: 400,
        borderWidth: 1,
        borderColor: theme.border1,
    },
    deleteModalTitle: {
        fontSize: 20,
        fontWeight: '700',
        color: '#d32f2f',
        textAlign: 'center',
        marginBottom: 16,
    },
    deleteModalMessage: {
        fontSize: 16,
        color: theme.text,
        textAlign: 'center',
        marginBottom: 16,
        lineHeight: 22,
    },
    deleteModalInstruction: {
        fontSize: 14,
        color: theme.text,
        marginBottom: 12,
        fontWeight: '500',
    },
    deleteModalInput: {
        borderWidth: 1,
        borderColor: theme.border1,
        borderRadius: 8,
        padding: 12,
        fontSize: 16,
        color: theme.text,
        backgroundColor: theme.background,
        marginBottom: 20,
    },
    deleteModalButtons: {
        flexDirection: 'row',
        gap: 12,
    },
    deleteModalButton: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: 44,
    },
    cancelButton: {
        backgroundColor: theme.border,
        borderWidth: 1,
        borderColor: theme.border1,
    },
    cancelButtonText: {
        fontSize: 16,
        fontWeight: '600',
        color: theme.text,
    },
    deleteButton: {
        backgroundColor: '#d32f2f',
    },
    deleteButtonText: {
        fontSize: 16,
        fontWeight: '600',
        color: '#fff',
    },
});

export default Account;