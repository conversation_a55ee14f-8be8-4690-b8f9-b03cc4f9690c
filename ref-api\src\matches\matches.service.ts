import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { CreateMatchDto } from './dto/create-match.dto';
import { UpdateMatchDto } from './dto/update-match.dto';
import { CreateSquadDto } from './dto/create-squad.dto';
import { UpdateSquadDto } from './dto/update-squad.dto';
import {
  Match,
  MatchStatus,
  Squad,
  TeamType,
  PlayerPosition,
} from '@prisma/client';

@Injectable()
export class MatchesService {
  constructor(private prisma: PrismaService) {}

  async create(
    createMatchDto: CreateMatchDto,
    refereeId: string,
  ): Promise<Match> {
    try {
      // If using default referee ID, find the default referee from seed
      let actualRefereeId = refereeId;

      if (refereeId === 'default-referee-id') {
        const defaultReferee = await this.prisma.user.findFirst({
          where: { email: '<EMAIL>' },
        });

        if (defaultReferee) {
          actualRefereeId = defaultReferee.id;
        } else {
          throw new BadRequestException(
            'Default referee not found. Please run database seed.',
          );
        }
      }

      // Validate referee exists
      const referee = await this.prisma.user.findUnique({
        where: { id: actualRefereeId },
      });

      if (!referee) {
        throw new BadRequestException('Referee not found');
      }

      // Create the match summary
      const matchSummary = {
        // Match creation info
        createdAt: new Date().toISOString(),
        matchDate: createMatchDto.matchDate,
        competition: createMatchDto.competition,
        venue: createMatchDto.venue,
        teams: {
          home: {
            name: createMatchDto.homeTeamName,
            shortName: createMatchDto.homeTeamShortName,
          },
          away: {
            name: createMatchDto.awayTeamName,
            shortName: createMatchDto.awayTeamShortName,
          },
        },
        matchOfficials: createMatchDto.matchOfficials || [],
        officialRole: createMatchDto.officialRole || 'Referee',
        // Match end info (will be populated when match ends)
        matchEndInfo: null,
      };

      console.log('Creating match with data:', {
        competition: createMatchDto.competition,
        venue: createMatchDto.venue,
        matchDate: new Date(createMatchDto.matchDate),
        status: MatchStatus.SCHEDULED,
      });

      const match = await this.prisma.match.create({
        data: {
          competition: createMatchDto.competition,
          venue: createMatchDto.venue,
          matchDate: new Date(createMatchDto.matchDate),
          officialRole: createMatchDto.officialRole || 'Referee',

          // Home team
          homeTeamName: createMatchDto.homeTeamName,
          homeTeamShortName: createMatchDto.homeTeamShortName,
          homeKitBase: createMatchDto.homeKitBase || '#FF0000',
          homeKitStripe: createMatchDto.homeKitStripe || '#00FFFF',

          // Away team
          awayTeamName: createMatchDto.awayTeamName,
          awayTeamShortName: createMatchDto.awayTeamShortName,
          awayKitBase: createMatchDto.awayKitBase || '#FFFFFF',
          awayKitStripe: createMatchDto.awayKitStripe || '#0000FF',

          // Match format
          teamSize: createMatchDto.teamSize || 11,
          maxSubstitute: createMatchDto.maxSubstitute || 5,
          numberOfPeriods: createMatchDto.numberOfPeriods || 2,
          periodLengths: createMatchDto.periodLengths || [45, 45],
          halfTime: createMatchDto.halfTime || 15,

          // Extra time
          extraTime: createMatchDto.extraTime || false,
          extraTimeLengths: createMatchDto.extraTimeLengths,

          // Substitutions
          substituteOpportunities:
            createMatchDto.substituteOpportunities || false,
          substituteOpportunitiesAllowance:
            createMatchDto.substituteOpportunitiesAllowance || 1,
          extraTimeSubOpportunities:
            createMatchDto.extraTimeSubOpportunities || false,
          extraTimeSubOpportunitiesAllowance:
            createMatchDto.extraTimeSubOpportunitiesAllowance || 1,
          extraTimeAdditionalSub:
            createMatchDto.extraTimeAdditionalSub || false,
          extraTimeAdditionalSubAllowance:
            createMatchDto.extraTimeAdditionalSubAllowance || 1,
          rollOnRollOff: createMatchDto.rollOnRollOff || false,

          // Other rules
          penalties: createMatchDto.penalties || false,
          misconductCode: createMatchDto.misconductCode || 'England',
          temporaryDismissals: createMatchDto.temporaryDismissals || false,
          temporaryDismissalsTime: createMatchDto.temporaryDismissalsTime || 10,

          // Injury time
          injuryTimeAllowance: createMatchDto.injuryTimeAllowance || false,
          injuryTimeSubs: createMatchDto.injuryTimeSubs || 0,
          injuryTimeSanctions: createMatchDto.injuryTimeSanctions || 0,
          injuryTimeGoals: createMatchDto.injuryTimeGoals || 0,

          // Officials and other data
          matchOfficials: createMatchDto.matchOfficials || [],
          fees: createMatchDto.fees,
          expenses: createMatchDto.expenses,
          mileage: createMatchDto.mileage,
          travelTime: createMatchDto.travelTime,
          notes: createMatchDto.notes,
          templateName: createMatchDto.templateName,
          summary: matchSummary,

          // Relations
          refereeId: actualRefereeId,
          status: MatchStatus.SCHEDULED,
        },
        include: {
          referee: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
            },
          },
        },
      });

      return match;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(`Failed to create match: ${error.message}`);
    }
  }

  async findAll(refereeId?: string): Promise<Match[]> {
    const where = refereeId ? { refereeId } : {};

    return this.prisma.match.findMany({
      where,
      include: {
        referee: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
      orderBy: {
        matchDate: 'asc',
      },
    });
  }

  async findUpcoming(refereeId?: string): Promise<Match[]> {
    const now = new Date();
    const where = {
      OR: [
        // Future matches that are scheduled
        {
          matchDate: {
            gte: now,
          },
          status: MatchStatus.SCHEDULED,
        },
        // Recent matches (within last 2 hours) that are still scheduled
        {
          matchDate: {
            gte: new Date(now.getTime() - 2 * 60 * 60 * 1000), // 2 hours ago
          },
          status: MatchStatus.SCHEDULED,
        },
      ],
      ...(refereeId && { refereeId }),
    };

    console.log(
      'Finding upcoming matches with query:',
      JSON.stringify(where, null, 2),
    );
    console.log('Current time:', now.toISOString());

    const matches = await this.prisma.match.findMany({
      where,
      include: {
        referee: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
      orderBy: {
        matchDate: 'asc',
      },
    });

    console.log(`Found ${matches.length} upcoming matches`);
    return matches;
  }

  async findPrevious(refereeId?: string): Promise<Match[]> {
    const now = new Date();
    const where = {
      // Simply get all matches with COMPLETED or CANCELLED status
      status: {
        in: [MatchStatus.COMPLETED, MatchStatus.CANCELLED],
      },
      ...(refereeId && { refereeId }),
    };

    console.log(
      'Finding previous matches with query:',
      JSON.stringify(where, null, 2),
    );

    const matches = await this.prisma.match.findMany({
      where,
      include: {
        referee: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
      orderBy: {
        matchDate: 'desc',
      },
    });

    console.log(`Found ${matches.length} previous matches`);
    matches.forEach((match) => {
      console.log(
        `Previous match: ${match.competition} - Status: ${match.status} - Date: ${match.matchDate}`,
      );
    });
    return matches;
  }

  async findOne(id: string): Promise<Match> {
    const match = await this.prisma.match.findUnique({
      where: { id },
      include: {
        referee: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
        assessments: true,
      },
    });

    if (!match) {
      throw new NotFoundException(`Match with ID ${id} not found`);
    }

    // Debug log to check what's being returned
    console.log('🔍 Backend Match Data:', {
      id: match.id,
      extraTime: match.extraTime,
      extraTimeLengths: match.extraTimeLengths,
      templateName: match.templateName,
      misconductCode: match.misconductCode,
    });

    return match;
  }

  async update(id: string, updateMatchDto: UpdateMatchDto): Promise<Match> {
    // Check if match exists
    await this.findOne(id);

    const updateData: any = {};

    // Only update fields that are provided
    Object.keys(updateMatchDto).forEach((key) => {
      if (updateMatchDto[key] !== undefined) {
        if (key === 'matchDate') {
          updateData[key] = new Date(updateMatchDto[key]);
        } else {
          updateData[key] = updateMatchDto[key];
        }
      }
    });

    return this.prisma.match.update({
      where: { id },
      data: updateData,
      include: {
        referee: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });
  }

  async remove(id: string): Promise<Match> {
    // Check if match exists
    await this.findOne(id);

    return this.prisma.match.delete({
      where: { id },
      include: {
        referee: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });
  }

  async updateStatus(id: string, status: MatchStatus): Promise<Match> {
    console.log(`Updating match ${id} status to ${status}`);

    // Check if match exists
    await this.findOne(id);

    const updatedMatch = await this.prisma.match.update({
      where: { id },
      data: { status },
      include: {
        referee: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });

    console.log(
      `Match ${id} status updated successfully to ${updatedMatch.status}`,
    );
    return updatedMatch;
  }

  // Squad methods
  async createSquad(
    matchId: string,
    createSquadDto: CreateSquadDto,
  ): Promise<Squad> {
    // Check if match exists
    await this.findOne(matchId);

    // Check if squad already exists for this match and team type
    const existingSquad = await this.prisma.squad.findUnique({
      where: {
        matchId_teamType: {
          matchId,
          teamType: createSquadDto.teamType as TeamType,
        },
      },
    });

    if (existingSquad) {
      // Update existing squad instead of creating new one
      return this.updateSquad(matchId, existingSquad.id, createSquadDto);
    }

    // Create new squad with players and officials
    const squad = await this.prisma.squad.create({
      data: {
        matchId,
        teamType: createSquadDto.teamType as TeamType,
        players: {
          create: createSquadDto.players.map((player) => ({
            number: player.number,
            name: player.name,
            position: player.position as PlayerPosition,
            isCaptain: player.isCaptain,
            isGoalkeeper: player.isGoalkeeper,
          })),
        },
        officials: {
          create: createSquadDto.officials.map((official) => ({
            role: official.role,
            name: official.name,
          })),
        },
      },
      include: {
        players: true,
        officials: true,
      },
    });

    return squad;
  }

  async getSquad(matchId: string, teamType: string): Promise<Squad> {
    // Check if match exists
    await this.findOne(matchId);

    const squad = await this.prisma.squad.findUnique({
      where: {
        matchId_teamType: {
          matchId,
          teamType: teamType as TeamType,
        },
      },
      include: {
        players: {
          orderBy: [
            { position: 'asc' }, // STARTER first, then SUBSTITUTE
            { number: 'asc' },
          ],
        },
        officials: true,
      },
    });

    if (!squad) {
      throw new NotFoundException(
        `Squad not found for match ${matchId} and team ${teamType}`,
      );
    }

    return squad;
  }

  async updateSquad(
    matchId: string,
    squadId: string,
    updateSquadDto: UpdateSquadDto,
  ): Promise<Squad> {
    // Check if match exists
    await this.findOne(matchId);

    // Check if squad exists
    const existingSquad = await this.prisma.squad.findUnique({
      where: { id: squadId },
      include: { players: true, officials: true },
    });

    if (!existingSquad) {
      throw new NotFoundException(`Squad with ID ${squadId} not found`);
    }

    // Delete existing players and officials, then create new ones
    await this.prisma.squadPlayer.deleteMany({
      where: { squadId },
    });

    await this.prisma.squadOfficial.deleteMany({
      where: { squadId },
    });

    // Update squad with new data
    const updatedSquad = await this.prisma.squad.update({
      where: { id: squadId },
      data: {
        teamType: updateSquadDto.teamType as TeamType,
        players: {
          create:
            updateSquadDto.players?.map((player) => ({
              number: player.number,
              name: player.name,
              position: player.position as PlayerPosition,
              isCaptain: player.isCaptain,
              isGoalkeeper: player.isGoalkeeper,
            })) || [],
        },
        officials: {
          create:
            updateSquadDto.officials?.map((official) => ({
              role: official.role,
              name: official.name,
            })) || [],
        },
      },
      include: {
        players: {
          orderBy: [{ position: 'asc' }, { number: 'asc' }],
        },
        officials: true,
      },
    });

    return updatedSquad;
  }

  async removeSquad(matchId: string, squadId: string): Promise<void> {
    // Check if match exists
    await this.findOne(matchId);

    // Check if squad exists
    const squad = await this.prisma.squad.findUnique({
      where: { id: squadId },
    });

    if (!squad) {
      throw new NotFoundException(`Squad with ID ${squadId} not found`);
    }

    // Delete squad (cascade will handle players and officials)
    await this.prisma.squad.delete({
      where: { id: squadId },
    });
  }

  // Squad officials methods
  async addSquadOfficial(
    matchId: string,
    teamType: string,
    officialData: { role: string; name: string },
  ): Promise<any> {
    const squad = await this.getSquad(matchId, teamType);

    if (!squad) {
      throw new NotFoundException(
        `Squad not found for match ${matchId} and team ${teamType}`,
      );
    }

    const official = await this.prisma.squadOfficial.create({
      data: {
        squadId: squad.id,
        ...officialData,
      },
    });

    return official;
  }

  async updateSquadOfficial(
    squadId: string,
    officialId: string,
    updates: { name?: string; role?: string },
  ): Promise<any> {
    const official = await this.prisma.squadOfficial.update({
      where: { id: officialId, squadId: squadId },
      data: updates,
    });

    if (!official) {
      throw new NotFoundException(
        `Official with ID ${officialId} not found in squad ${squadId}`,
      );
    }

    return official;
  }

  async removeSquadOfficial(squadId: string, officialId: string): Promise<void> {
    // Check if squad exists
    await this.prisma.squad.findUnique({
      where: { id: squadId },
    });

    await this.prisma.squadOfficial.delete({
      where: {
        id: officialId,
        squadId: squadId,
      },
    });
  }

  // Match logs methods
  async saveMatchLogs(matchId: string, logs: any[]): Promise<any[]> {
    // Check if match exists
    const match = await this.findOne(matchId);

    // Delete existing logs for this match
    await this.prisma.matchLog.deleteMany({
      where: { matchId },
    });

    // Save new logs
    const savedLogs: any[] = [];
    for (const log of logs) {
      const savedLog = await this.prisma.matchLog.create({
        data: {
          type: log.type,
          timestamp: log.timestamp,
          realTime: new Date(log.realTime),
          data: log.data,
          matchId: matchId,
        },
      });
      savedLogs.push(savedLog);
    }

    // Calculate match statistics from logs and update summary
    const matchStats = this.calculateMatchStats(logs);
    
    // Update match summary with end info
    const currentSummary = match.summary as any || {};
    const updatedSummary = {
      ...currentSummary,
      matchEndInfo: {
        endedAt: new Date().toISOString(),
        ...matchStats,
      },
    };

    await this.prisma.match.update({
      where: { id: matchId },
      data: {
        summary: updatedSummary,
      },
    });

    return savedLogs;
  }

  async getMatchLogs(matchId: string): Promise<any[]> {
    // Check if match exists
    await this.findOne(matchId);

    return this.prisma.matchLog.findMany({
      where: { matchId },
      orderBy: { createdAt: 'asc' },
    });
  }

  // Helper method to calculate match statistics from logs
  private calculateMatchStats(logs: any[]): any {
    console.log('📋 Calculating match stats from logs:', logs.length);
    
    const stats = {
      goals: {
        home: 0,
        away: 0,
        total: 0,
      },
      cards: {
        yellow: {
          home: 0,
          away: 0,
          total: 0,
        },
        red: {
          home: 0,
          away: 0,
          total: 0,
        },
        total: 0,
      },
      isAbandoned: false,
      hadExtraTime: false,
    };

    // Log all card-related events for debugging
    const cardLogs = logs.filter(log => log.type?.includes('card') || log.type === 'yellow_card' || log.type === 'red_card');
    console.log('🟨 Card logs found:', cardLogs);

    logs.forEach((log, index) => {
      console.log(`Processing log ${index + 1}/${logs.length}: ${log.type}`, log.data);
      
      switch (log.type) {
        case 'goal':
          if (log.data?.team === 'home') {
            stats.goals.home++;
          } else if (log.data?.team === 'away') {
            stats.goals.away++;
          }
          stats.goals.total++;
          break;

        // Handle various card types
        case 'yellow_card':
          console.log('🟨 Processing yellow card:', log);
          if (log.data?.team === 'home') {
            stats.cards.yellow.home++;
          } else if (log.data?.team === 'away') {
            stats.cards.yellow.away++;
          }
          stats.cards.yellow.total++;
          stats.cards.total++;
          console.log('🟨 Yellow card stats after:', stats.cards.yellow);
          break;

        case 'red_card':
          console.log('🟥 Processing red card:', log);
          if (log.data?.team === 'home') {
            stats.cards.red.home++;
          } else if (log.data?.team === 'away') {
            stats.cards.red.away++;
          }
          stats.cards.red.total++;
          stats.cards.total++;
          console.log('🟥 Red card stats after:', stats.cards.red);
          break;

        case 'card':
          // Generic card type - check data for cardType
          const cardType = log.data?.cardType;
          console.log('🃏 Processing generic card:', log, 'Card type:', cardType);
          
          if (cardType === 'yellow') {
            if (log.data?.team === 'home') {
              stats.cards.yellow.home++;
            } else if (log.data?.team === 'away') {
              stats.cards.yellow.away++;
            }
            stats.cards.yellow.total++;
            stats.cards.total++;
            console.log('🟨 Yellow card counted! Stats after:', stats.cards.yellow);
          } else if (cardType === 'red') {
            if (log.data?.team === 'home') {
              stats.cards.red.home++;
            } else if (log.data?.team === 'away') {
              stats.cards.red.away++;
            }
            stats.cards.red.total++;
            stats.cards.total++;
            console.log('🟥 Red card counted! Stats after:', stats.cards.red);
          } else {
            console.log('⚠️ Unknown card type:', cardType, 'Full log:', log);
          }
          break;

        case 'match_abandoned':
          stats.isAbandoned = true;
          break;

        case 'extra_time_start':
        case 'extra_time':
          stats.hadExtraTime = true;
          break;

        default:
          // Handle other log types if needed
          break;
      }
    });

    console.log('🎯 Final calculated stats:', JSON.stringify(stats, null, 2));
    return stats;
  }

  // Get unique filter options from match summaries
  async getFilterOptions(refereeId?: string): Promise<any> {
    const where = refereeId ? { refereeId } : {};
    console.log('🔍 Getting filter options for refereeId:', refereeId);

    const matches = await this.prisma.match.findMany({
      where,
      select: {
        summary: true,
        matchOfficials: true,
      },
    });

    console.log('📋 Found matches for filter options:', matches.length);
    console.log('🔎 Sample match summaries:', matches.slice(0, 2).map(m => m.summary));

    const competitions = new Set<string>();
    const venues = new Set<string>();
    const teams = new Set<string>();
    const matchOfficials = new Set<string>();
    const roles = new Set<string>();

    matches.forEach((match) => {
      const summary = match.summary as any;
      if (summary) {
        // Extract from creation info
        if (summary.competition) competitions.add(summary.competition);
        if (summary.venue) venues.add(summary.venue);
        if (summary.officialRole) roles.add(summary.officialRole);
        
        if (summary.teams) {
          if (summary.teams.home?.name) teams.add(summary.teams.home.name);
          if (summary.teams.away?.name) teams.add(summary.teams.away.name);
        }
        
        if (summary.matchOfficials && Array.isArray(summary.matchOfficials)) {
          summary.matchOfficials.forEach((official: any) => {
            if (official.name) matchOfficials.add(official.name);
            if (official.role) roles.add(official.role);
          });
        }
      }

      // Also check the main matchOfficials field as fallback
      if (match.matchOfficials && Array.isArray(match.matchOfficials)) {
        (match.matchOfficials as any[]).forEach((official: any) => {
          if (official.name) matchOfficials.add(official.name);
          if (official.role) roles.add(official.role);
        });
      }
    });

    const result = {
      competitions: Array.from(competitions).sort(),
      venues: Array.from(venues).sort(),
      teams: Array.from(teams).sort(),
      matchOfficials: Array.from(matchOfficials).sort(),
      roles: Array.from(roles).sort(),
    };

    console.log('🎆 Filter options result:', result);
    return result;
  }

  // Get filtered matches based on summary data
  async getFilteredMatches(refereeId: string, filters: any): Promise<any[]> {
    const where: any = { refereeId };
    
    console.log('🔍 Starting getFilteredMatches with filters:', JSON.stringify(filters, null, 2));

    // Base query to get matches with summary data
    let matches = await this.prisma.match.findMany({
      where,
      include: {
        referee: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
      orderBy: {
        matchDate: 'desc',
      },
    });

    console.log(`📋 Found ${matches.length} total matches before filtering`);

    // Apply filters based on summary data
    // Only apply filter if criteria is defined (undefined means no restriction)
    
    if (filters.competitions && filters.competitions.length > 0) {
      matches = matches.filter(match => {
        const summary = match.summary as any;
        return summary?.competition && filters.competitions.includes(summary.competition);
      });
    }

    if (filters.venues && filters.venues.length > 0) {
      matches = matches.filter(match => {
        const summary = match.summary as any;
        return summary?.venue && filters.venues.includes(summary.venue);
      });
    }

    if (filters.teams && filters.teams.length > 0) {
      const beforeCount = matches.length;
      matches = matches.filter(match => {
        const summary = match.summary as any;
        if (!summary?.teams) return false;
        const homeTeam = summary.teams.home?.name;
        const awayTeam = summary.teams.away?.name;
        const includeMatch = filters.teams.some((team: string) => team === homeTeam || team === awayTeam);
        if (includeMatch) {
          console.log(`🏀 Team filter included: ${homeTeam} vs ${awayTeam}`);
        }
        return includeMatch;
      });
      console.log(`🏀 Team filter: ${beforeCount} -> ${matches.length} matches`);
    }

    if (filters.matchOfficials && filters.matchOfficials.length > 0) {
      matches = matches.filter(match => {
        const summary = match.summary as any;
        if (!summary?.matchOfficials) return false;
        return summary.matchOfficials.some((official: any) => 
          filters.matchOfficials.includes(official.name)
        );
      });
    }

    if (filters.roles && filters.roles.length > 0) {
      matches = matches.filter(match => {
        const summary = match.summary as any;
        const officialRole = summary?.officialRole;
        if (officialRole && filters.roles.includes(officialRole)) return true;
        
        // Also check match officials roles
        if (summary?.matchOfficials) {
          return summary.matchOfficials.some((official: any) => 
            filters.roles.includes(official.role)
          );
        }
        return false;
      });
    }

    // Filter by match statistics (from matchEndInfo)
    // If specific teams are selected, filter by team-specific stats
    // Otherwise, use total match stats
    
    if (filters.minYellowCards && filters.minYellowCards > 0) {
      matches = matches.filter(match => {
        const summary = match.summary as any;
        const matchEndInfo = summary?.matchEndInfo;
        
        // If teams filter is specified and has exactly one team, filter by that team's stats
        if (filters.teams && filters.teams.length === 1) {
          const selectedTeam = filters.teams[0];
          const homeTeamName = summary?.teams?.home?.name;
          const awayTeamName = summary?.teams?.away?.name;
          
          let teamYellowCards = 0;
          if (selectedTeam === homeTeamName) {
            teamYellowCards = matchEndInfo?.cards?.yellow?.home || 0;
          } else if (selectedTeam === awayTeamName) {
            teamYellowCards = matchEndInfo?.cards?.yellow?.away || 0;
          } else {
            // Selected team not found in this match, exclude it
            return false;
          }
          
          console.log(`🟨 Yellow card filter - Team: ${selectedTeam}, Cards: ${teamYellowCards}, Required: ${filters.minYellowCards}, Match: ${homeTeamName} vs ${awayTeamName}`);
          return teamYellowCards >= filters.minYellowCards;
        }
        
        // Default: use total match stats
        const yellowCards = matchEndInfo?.cards?.yellow?.total || 0;
        return yellowCards >= filters.minYellowCards;
      });
    }

    if (filters.minRedCards && filters.minRedCards > 0) {
      const beforeCount = matches.length;
      matches = matches.filter(match => {
        const summary = match.summary as any;
        const matchEndInfo = summary?.matchEndInfo;
        
        // If teams filter is specified and has exactly one team, filter by that team's stats
        if (filters.teams && filters.teams.length === 1) {
          const selectedTeam = filters.teams[0];
          const homeTeamName = summary?.teams?.home?.name;
          const awayTeamName = summary?.teams?.away?.name;
          
          let teamRedCards = 0;
          if (selectedTeam === homeTeamName) {
            teamRedCards = matchEndInfo?.cards?.red?.home || 0;
          } else if (selectedTeam === awayTeamName) {
            teamRedCards = matchEndInfo?.cards?.red?.away || 0;
          } else {
            // Selected team not found in this match, exclude it
            console.log(`❌ Red card filter - Team ${selectedTeam} not found in match: ${homeTeamName} vs ${awayTeamName}`);
            return false;
          }
          
          const meetsRequirement = teamRedCards >= filters.minRedCards;
          console.log(`🔍 Red card filter - Team: ${selectedTeam}, Cards: ${teamRedCards}, Required: ${filters.minRedCards}, Match: ${homeTeamName} vs ${awayTeamName}, Meets requirement: ${meetsRequirement}`);
          return meetsRequirement;
        }
        
        // Default: use total match stats
        const redCards = matchEndInfo?.cards?.red?.total || 0;
        const meetsRequirement = redCards >= filters.minRedCards;
        console.log(`🔍 Red card filter (total) - Cards: ${redCards}, Required: ${filters.minRedCards}, Meets requirement: ${meetsRequirement}`);
        return meetsRequirement;
      });
      console.log(`🟥 Red card filter: ${beforeCount} -> ${matches.length} matches`);
    }

    if (filters.minGoals && filters.minGoals > 0) {
      matches = matches.filter(match => {
        const summary = match.summary as any;
        const matchEndInfo = summary?.matchEndInfo;
        
        // If teams filter is specified and has exactly one team, filter by that team's stats
        if (filters.teams && filters.teams.length === 1) {
          const selectedTeam = filters.teams[0];
          const homeTeamName = summary?.teams?.home?.name;
          const awayTeamName = summary?.teams?.away?.name;
          
          let teamGoals = 0;
          if (selectedTeam === homeTeamName) {
            teamGoals = matchEndInfo?.goals?.home || 0;
          } else if (selectedTeam === awayTeamName) {
            teamGoals = matchEndInfo?.goals?.away || 0;
          } else {
            // Selected team not found in this match, exclude it
            return false;
          }
          
          console.log(`⚽ Goals filter - Team: ${selectedTeam}, Goals: ${teamGoals}, Required: ${filters.minGoals}, Match: ${homeTeamName} vs ${awayTeamName}`);
          return teamGoals >= filters.minGoals;
        }
        
        // Default: use total match stats
        const goals = matchEndInfo?.goals?.total || 0;
        return goals >= filters.minGoals;
      });
    }

    if (filters.hadExtraTime === true) {
      matches = matches.filter(match => {
        const summary = match.summary as any;
        return summary?.matchEndInfo?.hadExtraTime === true;
      });
    }

    if (filters.wasAbandoned === true) {
      matches = matches.filter(match => {
        const summary = match.summary as any;
        return summary?.matchEndInfo?.isAbandoned === true;
      });
    }

    // Filter by date range if provided
    if (filters.dateFrom) {
      const fromDate = new Date(filters.dateFrom);
      matches = matches.filter(match => new Date(match.matchDate) >= fromDate);
    }

    if (filters.dateTo) {
      const toDate = new Date(filters.dateTo);
      matches = matches.filter(match => new Date(match.matchDate) <= toDate);
    }

    return matches;
  }

  /**
   * Check if a user is a participant in a match
   */
  async isUserParticipant(matchId: string, userId: string): Promise<boolean> {
    try {
      const participant = await this.prisma.matchParticipant.findUnique({
        where: {
          matchId_userId: {
            matchId,
            userId
          }
        }
      });

      return participant !== null;
    } catch (error) {
      console.error('Error checking user participation:', error);
      return false;
    }
  }
}
