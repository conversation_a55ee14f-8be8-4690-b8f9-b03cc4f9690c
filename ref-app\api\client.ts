import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { API_CONFIG, ApiResponse } from './config';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: API_CONFIG.BASE_URL,
      timeout: API_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        const hasAuth = !!config.headers.Authorization;
        console.log(`🚀 ${config.method?.toUpperCase()} ${config.url} ${hasAuth ? '🔑' : '❌'}`);
        
        // Only log missing auth for protected endpoints
        if (!hasAuth && this.isProtectedEndpoint(config.url || '')) {
          console.warn(`⚠️ Missing auth for protected endpoint: ${config.url}`);
        }
        return config;
      },
      (error) => {
        console.error('❌ API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        console.log(`✅ ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        // Don't log squad not found errors as they're expected for new matches
        const isSquadNotFound = error.response?.status === 404 && 
                               error.response?.data?.error?.includes('Squad not found');
        
        if (!isSquadNotFound) {
          const status = error.response?.status || 'Network';
          const url = error.config?.url || 'unknown';
          console.error(`❌ ${status} ${url}:`, error.response?.data || error.message);
        }
        return Promise.reject(error);
      }
    );
  }

  // Generic GET method
  async get<T>(url: string, params?: any): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.get<ApiResponse<T>>(url, { params });
      return response.data;
    } catch (error: any) {
      return this.handleError(error);
    }
  }

  // Generic POST method
  async post<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.post<ApiResponse<T>>(url, data);
      return response.data;
    } catch (error: any) {
      return this.handleError(error);
    }
  }

  // Generic PUT method
  async put<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.put<ApiResponse<T>>(url, data);
      return response.data;
    } catch (error: any) {
      return this.handleError(error);
    }
  }

  // Generic PATCH method
  async patch<T>(url: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.patch<ApiResponse<T>>(url, data);
      return response.data;
    } catch (error: any) {
      return this.handleError(error);
    }
  }

  // Generic DELETE method
  async delete<T>(url: string): Promise<ApiResponse<T>> {
    try {
      const response = await this.client.delete<ApiResponse<T>>(url);
      return response.data;
    } catch (error: any) {
      return this.handleError(error);
    }
  }

  // Error handler
  private handleError(error: any): ApiResponse {
    if (error.response) {
      // Server responded with error status
      const errorData = error.response.data;
      
      // Special handling for squad not found - this is expected for new matches
      if (error.response.status === 404 && 
          errorData?.error?.includes('Squad not found')) {
        return {
          success: false,
          error: 'Squad not found',
          message: 'No squad data available yet',
          isExpectedError: true // Flag to indicate this is an expected error
        };
      }
      
      return {
        success: false,
        error: errorData?.error || errorData?.message || 'Server error',
        message: errorData?.message || 'Request failed'
      };
    } else if (error.request) {
      // Request was made but no response received
      return {
        success: false,
        error: 'Network error',
        message: 'Unable to connect to server. Please check your internet connection.'
      };
    } else {
      // Something else happened
      return {
        success: false,
        error: error.message || 'Unknown error',
        message: 'An unexpected error occurred'
      };
    }
  }

  // Set authorization token
  setAuthToken(token: string) {
    this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    console.log('🔑 Auth token set in API client');
  }

  // Remove authorization token
  removeAuthToken() {
    delete this.client.defaults.headers.common['Authorization'];
    console.log('🔑 Auth token removed from API client');
  }

  // Check if auth token is set
  hasAuthToken(): boolean {
    return !!this.client.defaults.headers.common['Authorization'];
  }

  // Check if endpoint requires authentication
  private isProtectedEndpoint(url: string): boolean {
    const publicEndpoints = ['/health', '/auth/login'];
    return !publicEndpoints.some(endpoint => url.includes(endpoint));
  }

  // Health check
  async healthCheck(): Promise<ApiResponse> {
    return this.get('/health');
  }
}

// Export singleton instance
export const apiClient = new ApiClient();
export default apiClient;
