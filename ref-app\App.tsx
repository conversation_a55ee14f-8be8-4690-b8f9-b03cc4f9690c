import React, { useEffect, useState, useRef } from 'react';
import { NavigationContainer, DefaultTheme, DarkTheme } from '@react-navigation/native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Settings } from 'react-native-fbsdk-next';
import { DeviceEventEmitter } from 'react-native';

// Import Firebase config to ensure initialization
import './firebaseConfig';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { UserProvider } from './contexts/UserContext';
import AuthNavigator from './navigation/AuthNavigator';
import { ThemeProvider, useTheme } from './contexts/ThemeContext'; // Import useTheme
import { lightTheme, darkTheme } from './themes/colors'; // <-- IMPORTANT: Adjust this path to where your colors.ts is located
import NotificationService from './services/NotificationService';
import InvitationNotificationModal from './components/InvitationNotificationModal';

// This component handles the app structure with providers
const AppWithProviders = () => {
  const { isDarkMode } = useTheme();
  
  return (
    <SafeAreaProvider>
      <AuthProvider>
        <UserProvider>
          <ThemedAppContent isDarkMode={isDarkMode} />
        </UserProvider>
      </AuthProvider>
    </SafeAreaProvider>
  );
};

// This is the component that will use both theme and auth contexts
const ThemedAppContent = ({ isDarkMode }: { isDarkMode: boolean }) => {
  const { user } = useAuth();
  const [currentInvitation, setCurrentInvitation] = useState<any>(null);
  const [showInvitationModal, setShowInvitationModal] = useState(false);
  const navigationRef = useRef<any>(null);

  // Create a custom navigation theme that matches your light theme
  const MyLightTheme = {
    ...DefaultTheme,
    colors: {
      ...DefaultTheme.colors,
      primary: lightTheme.primary,
      background: lightTheme.background, // This is the key fix
      card: lightTheme.card,
      text: lightTheme.text,
      border: lightTheme.border,
    },
  };

  // Create a custom navigation theme that matches your dark theme
  const MyDarkTheme = {
    ...DarkTheme,
    colors: {
      ...DarkTheme.colors,
      primary: darkTheme.primary,
      background: darkTheme.background, // This is the key fix
      card: darkTheme.card,
      text: darkTheme.text,
      border: darkTheme.border,
    },
  };

  useEffect(() => {
    // Initialize Facebook SDK
    Settings.setAppID('651727944558014'); // Replace with your actual Facebook App ID
    Settings.initializeSDK();

    let removeListener: (() => void) | null = null;

    // Setup notification listener when service is ready
    const setupNotificationListener = () => {
      console.log('📱 Setting up notification listener...');
      removeListener = NotificationService.addListener('app', (notification) => {
        console.log('📱 App received notification:', notification);
        console.log('📱 Notification type:', notification.type);
        console.log('📱 Current modal state:', showInvitationModal);

        if (notification.type === 'sync_invite') {
          console.log('📱 Setting invitation modal to show');
          setCurrentInvitation(notification);
          setShowInvitationModal(true);
          console.log('📱 Modal should now be visible');
        } else if (notification.type === 'match_start') {
          console.log('📱 Match started notification received');
          // Navigate to StartMatchScreen with the match data
          if (navigationRef.current && notification.data?.matchData) {
            console.log('📱 Navigating to StartMatch screen');
            navigationRef.current.navigate('StartMatch', {
              matchId: notification.matchId,
              matchData: notification.data.matchData,
              autoStart: true,
              kickoffTeam: notification.data.kickoffTeam
            });
          }
        } else if (notification.type === 'action_broadcast') {
          console.log('📱 Action broadcast notification received');
          console.log('📱 Action data:', notification.data);

          // Don't show notifications to the referee who sent the action
          if (notification.fromUserId === user?.uid) {
            console.log('📱 Ignoring own action broadcast');
            return;
          }

          // Check if this is a response notification (should be ignored)
          if (notification.data?.response || notification.data?.actionId === 'unknown') {
            console.log('📱 Ignoring action response notification');
            return;
          }

          // Don't show approval dialogs for period_end - these should be automatic
          if (notification.data?.actionType === 'period_end') {
            console.log('📱 Period end notification - handling automatically');

            // Emit event to automatically handle period end in the match screen
            DeviceEventEmitter.emit('autoPeriodEnd', {
              actionData: notification.data.actionData,
              matchId: notification.matchId
            });
            return;
          }

          // Only show approval dialog for actual match actions that need approval
          const approvableActions = ['goal', 'card', 'substitution', 'penalty_shot'];
          if (notification.data?.actionType && approvableActions.includes(notification.data.actionType) && notification.data?.actionData) {
            console.log('📱 Showing action approval dialog for:', notification.data.actionType);
            NotificationService.handleActionBroadcast(notification);
          } else {
            console.log('📱 Non-approvable action or invalid data, ignoring:', notification.data?.actionType);
          }
        } else if (notification.type === 'sync_update') {
          console.log('📱 Sync update notification received');
          // Emit sync update event for real-time updates
          DeviceEventEmitter.emit('syncUpdate', notification.data);
        }
      });
      console.log('📱 Notification listener registered');
    };

    // Listen for notification service ready event
    const handleNotificationServiceReady = () => {
      console.log('📱 Notification service is ready, setting up listener');
      setupNotificationListener();
    };

    const subscription = DeviceEventEmitter.addListener('notificationServiceReady', handleNotificationServiceReady);

    // Also try to setup immediately in case service is already ready
    if (NotificationService.isConnected()) {
      setupNotificationListener();
    }

    return () => {
      subscription.remove();
      if (removeListener) {
        removeListener();
      }
    };
  }, []);

  const handleAcceptInvitation = (matchId: string) => {
    console.log('✅ Invitation accepted for match:', matchId);
    // The match has been added to the user's account
    // Force a refresh of the dashboard/matches
    setTimeout(() => {
      // This will trigger a re-render and refresh of match data
      console.log('🔄 Refreshing dashboard data...');
      // You might want to emit a custom event or use a global state refresh here
    }, 1000);
  };

  const handleDeclineInvitation = (invitationId: string) => {
    console.log('❌ Invitation declined:', invitationId);
    // Handle invitation decline if needed
  };

  return (
    <>
      {/* Pass the correct theme to the NavigationContainer */}
      <NavigationContainer ref={navigationRef} theme={isDarkMode ? MyDarkTheme : MyLightTheme}>
        <AuthNavigator />
      </NavigationContainer>

      {/* Global Invitation Modal */}
      <InvitationNotificationModal
        visible={showInvitationModal}
        invitation={currentInvitation}
        onClose={() => {
          setShowInvitationModal(false);
          setCurrentInvitation(null);
        }}
        onAccept={handleAcceptInvitation}
        onDecline={handleDeclineInvitation}
      />
    </>
  );
};


// The main App component now wraps everything in the ThemeProvider
export default function App() {
  return (
    <ThemeProvider>
      <ThemedApp />
    </ThemeProvider>
  );
}