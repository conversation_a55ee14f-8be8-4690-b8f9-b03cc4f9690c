import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { CreateEvaluationQuestionsDto } from './dto/create-evaluation-questions.dto';
import { CreateMatchEvaluationDto } from './dto/create-match-evaluation.dto';
import { EvaluationQuestion, MatchEvaluation } from '@prisma/client';

@Injectable()
export class EvaluationService {
  constructor(private prisma: PrismaService) {}

  /**
   * Get evaluation questions for a user
   */
  async getEvaluationQuestions(userId: string): Promise<EvaluationQuestion[]> {
    console.log(`📚 Fetching evaluation questions for userId: ${userId}`);

    const questions = await this.prisma.evaluationQuestion.findMany({
      where: { userId },
      orderBy: { id: 'asc' }
    });

    console.log(`✅ Found ${questions.length} questions for user ${userId}`);
    return questions;
  }

  /**
   * Create or update evaluation questions for a user
   */
  async createEvaluationQuestions(dto: CreateEvaluationQuestionsDto): Promise<EvaluationQuestion[]> {
    const { userId, questions } = dto;
    
    console.log(`💾 Saving evaluation questions for userId: ${userId}`, { questionCount: questions.length });

    // Delete existing questions for this user
    await this.prisma.evaluationQuestion.deleteMany({
      where: { userId }
    });

    console.log(`🗑️ Deleted existing questions for user ${userId}`);

    // Create new questions
    const createdQuestions: EvaluationQuestion[] = [];
    for (const question of questions) {
      const created = await this.prisma.evaluationQuestion.create({
        data: {
          id: question.id,
          userId: userId,
          text: question.text
        }
      });
      createdQuestions.push(created);
    }

    console.log(`✅ Created ${createdQuestions.length} questions for user ${userId}`);
    return createdQuestions;
  }

  /**
   * Create match evaluation with answers
   */
  async createMatchEvaluation(dto: CreateMatchEvaluationDto): Promise<MatchEvaluation> {
    const { matchId, userId, answers } = dto;
    
    console.log(`💾 Saving match evaluation for matchId: ${matchId}, userId: ${userId}`, { answerCount: answers.length });

    // Check if evaluation already exists for this match
    const existingEvaluation = await this.prisma.matchEvaluation.findFirst({
      where: { matchId }
    });

    if (existingEvaluation) {
      console.log(`⚠️ Evaluation already exists for match ${matchId}, updating...`);
      
      // Delete existing answers
      await this.prisma.evaluationAnswer.deleteMany({
        where: { evaluationId: existingEvaluation.id }
      });

      // Update evaluation and create new answers
      const updatedEvaluation = await this.prisma.matchEvaluation.update({
        where: { id: existingEvaluation.id },
        data: {
          userId,
          completedAt: new Date(),
          answers: {
            create: answers.map(answer => ({
              questionId: answer.questionId,
              questionText: answer.question,
              rating: answer.rating,
              label: answer.label,
              emoji: answer.emoji
            }))
          }
        },
        include: { answers: true }
      });

      console.log(`✅ Updated evaluation for match ${matchId}`);
      return updatedEvaluation;
    } else {
      // Create new evaluation
      const evaluation = await this.prisma.matchEvaluation.create({
        data: {
          matchId,
          userId,
          completedAt: new Date(),
          answers: {
            create: answers.map(answer => ({
              questionId: answer.questionId,
              questionText: answer.question,
              rating: answer.rating,
              label: answer.label,
              emoji: answer.emoji
            }))
          }
        },
        include: { answers: true }
      });

      console.log(`✅ Created evaluation for match ${matchId}`);
      return evaluation;
    }
  }

  /**
   * Get match evaluation by matchId
   */
  async getMatchEvaluation(matchId: string): Promise<MatchEvaluation | null> {
    console.log(`📖 Fetching match evaluation for matchId: ${matchId}`);

    const evaluation = await this.prisma.matchEvaluation.findFirst({
      where: { matchId },
      include: { answers: true }
    });

    if (evaluation) {
      console.log(`✅ Found evaluation for match ${matchId} with ${evaluation.answers.length} answers`);
    } else {
      console.log(`❌ No evaluation found for match ${matchId}`);
    }

    return evaluation;
  }

  /**
   * Delete match evaluation (optional cleanup)
   */
  async deleteMatchEvaluation(matchId: string): Promise<boolean> {
    console.log(`🗑️ Deleting evaluation for matchId: ${matchId}`);

    const evaluation = await this.prisma.matchEvaluation.findFirst({
      where: { matchId }
    });

    if (!evaluation) {
      console.log(`❌ No evaluation found to delete for match ${matchId}`);
      return false;
    }

    await this.prisma.matchEvaluation.delete({
      where: { id: evaluation.id }
    });

    console.log(`✅ Deleted evaluation for match ${matchId}`);
    return true;
  }
}