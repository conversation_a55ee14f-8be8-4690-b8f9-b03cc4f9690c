const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function cleanupDatabase() {
  try {
    console.log('🧹 Starting database cleanup...');
    
    // Delete all assessments first (due to foreign key constraints)
    const deletedAssessments = await prisma.assessment.deleteMany({});
    console.log(`🗑️ Deleted ${deletedAssessments.count} assessments`);
    
    // Delete all squad players and officials
    const deletedSquadPlayers = await prisma.squadPlayer.deleteMany({});
    console.log(`🗑️ Deleted ${deletedSquadPlayers.count} squad players`);
    
    const deletedSquadOfficials = await prisma.squadOfficial.deleteMany({});
    console.log(`🗑️ Deleted ${deletedSquadOfficials.count} squad officials`);
    
    // Delete all squads
    const deletedSquads = await prisma.squad.deleteMany({});
    console.log(`🗑️ Deleted ${deletedSquads.count} squads`);
    
    // Delete all matches
    const deletedMatches = await prisma.match.deleteMany({});
    console.log(`🗑️ Deleted ${deletedMatches.count} matches`);
    
    // Delete all users
    const deletedUsers = await prisma.user.deleteMany({});
    console.log(`🗑️ Deleted ${deletedUsers.count} users`);
    
    console.log('✅ Database cleanup completed successfully!');
    
    // Show current state
    const userCount = await prisma.user.count();
    const matchCount = await prisma.match.count();
    
    console.log(`📊 Current state: ${userCount} users, ${matchCount} matches`);
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanupDatabase();