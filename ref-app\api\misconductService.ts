import { apiClient } from './client';
import { API_CONFIG, ApiResponse } from './config';

// Misconduct interfaces
export interface MisconductCode {
  id: string;
  code: string;
  description: string;
  subcategories?: SubCategory[];
}

export interface SubCategory {
  id: string;
  code: string;
  description: string;
}

export interface MisconductSet {
  id: string;
  name: string;
  playersYellowCodes: MisconductCode[];
  playersRedCodes: MisconductCode[];
  officialsYellowCodes: MisconductCode[];
  officialsRedCodes: MisconductCode[];
  userId?: string;
  createdAt?: string;
  updatedAt?: string;
}

export class MisconductService {
  // Get all misconduct sets for a user
  static async getMisconductSets(userId?: string): Promise<ApiResponse<MisconductSet[]>> {
    const endpoint = userId ? `/misconduct-sets/${userId}` : '/misconduct-sets';
    return apiClient.get<MisconductSet[]>(endpoint);
  }

  // Create a new misconduct set
  static async createMisconductSet(misconductSet: Omit<MisconductSet, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<MisconductSet>> {
    return apiClient.post<MisconductSet>('/misconduct-sets', misconductSet);
  }

  // Update an existing misconduct set
  static async updateMisconductSet(id: string, updates: Partial<MisconductSet>): Promise<ApiResponse<MisconductSet>> {
    return apiClient.put<MisconductSet>(`/misconduct-sets/${id}`, updates);
  }

  // Delete a misconduct set
  static async deleteMisconductSet(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/misconduct-sets/${id}`);
  }

  // Get hardcoded Wales misconduct set
  static getWalesMisconductSet(): MisconductSet {
    return {
      id: 'wales-default',
      name: 'Wales',
      playersYellowCodes: [
        {
          id: 'c1',
          code: 'C1',
          description: 'Unsporting Behaviour',
          subcategories: [
            { id: 'c1-aa', code: 'AA', description: 'Adopting an aggressive attitude' },
            { id: 'c1-di', code: 'DI', description: 'Simulation' },
            { id: 'c1-dp', code: 'DP', description: 'Dangerous Play' },
            { id: 'c1-ft', code: 'FT', description: 'Foul Tackle' },
            { id: 'c1-gc', code: 'GC', description: 'Goal Celebration' },
            { id: 'c1-hb', code: 'HB', description: 'Handball' },
            { id: 'c1-rp', code: 'RP', description: 'Reckless Play' },
            { id: 'c1-sp', code: 'SP', description: 'Pushing or Pulling an opponent' },
            { id: 'c1-tr', code: 'TR', description: 'Tripping' },
            { id: 'c1-ub', code: 'UB', description: 'Unspecified Behaviour' }
          ]
        },
        {
          id: 'c2',
          code: 'C2',
          description: 'Shows dissent by word or action(T)',
        },
        {
          id: 'c3',
          code: 'C3',
          description: 'Persistently infringing the Laws of the Game',
        },
        {
          id: 'c4',
          code: 'C4',
          description: 'Delays restart',
        },
        {
          id: 'c5',
          code: 'C5',
          description: 'Fails to retreat required distance at a restart',
        },
        {
          id: 'c6',
          code: 'C6',
          description: 'Enters the field of play without permission',
        },
        {
          id: 'c7',
          code: 'C7',
          description: 'Leaves the field of play without permission',
        }
      ],
      playersRedCodes: [
        {
          id: 's1',
          code: 'S1',
          description: 'Serious foul play',
        },
        {
          id: 's2',
          code: 'S2',
          description: 'Violent conduct',
        },
        {
          id: 's3',
          code: 'S3',
          description: 'Spitting at an opponent or any other person',
        },
        {
          id: 's4',
          code: 'S4',
          description: 'Denying a goal-scoring opportunity to opponents by deliberate handball',
        },
        {
          id: 's5',
          code: 'S5',
          description: 'Denying a goal-scoring opportunity to opponents by an offense punishable by a free kick or penalty',
        },
        {
          id: 's6',
          code: 'S6',
          description: 'Using offensive, insulting, or abusive language/gestures',
        },
        {
          id: 's7',
          code: 'S7',
          description: 'Receiving a second caution',
        }
      ],
      officialsYellowCodes: [
        {
          id: 't1',
          code: 'T1',
          description: 'Yellow',
        }
      ],
      officialsRedCodes: [
        {
          id: 't2',
          code: 'T2',
          description: 'Red',
        }
      ]
    };
  }

  // Get hardcoded England with FA Codes misconduct set
  static getEnglandFAMisconductSet(): MisconductSet {
    return {
      id: 'england-fa-default',
      name: 'England with FA Codes',
      playersYellowCodes: [
        {
          id: 'c1-eng',
          code: 'C1',
          description: 'Unsporting Behaviour',
          subcategories: [
            { id: 'c1-aa-eng', code: 'AA', description: 'Adopting an aggressive attitude' },
            { id: 'c1-di-eng', code: 'DI', description: 'Simulation' },
            { id: 'c1-dp-eng', code: 'DP', description: 'Dangerous Play' },
            { id: 'c1-ft-eng', code: 'FT', description: 'Foul Tackle' },
            { id: 'c1-gc-eng', code: 'GC', description: 'Goal Celebration' },
            { id: 'c1-hb-eng', code: 'HB', description: 'Handball' },
            { id: 'c1-rp-eng', code: 'RP', description: 'Reckless Play' },
            { id: 'c1-sp-eng', code: 'SP', description: 'Pushing or Pulling an opponent' },
            { id: 'c1-tr-eng', code: 'TR', description: 'Tripping' },
            { id: 'c1-ub-eng', code: 'UB', description: 'Unspecified Behaviour' }
          ]
        },
        {
          id: 'c2-eng',
          code: 'C2',
          description: 'Shows dissent by word or action(T)',
        },
        {
          id: 'c3-eng',
          code: 'C3',
          description: 'Persistent infringement',
        },
        {
          id: 'c4-eng',
          code: 'C4',
          description: 'Delays the restart of play',
        },
        {
          id: 'c5-eng',
          code: 'C5',
          description: 'Fails to retreat required distance',
        },
        {
          id: 'c6-eng',
          code: 'C6',
          description: 'Enters the FOP without permission',
        },
        {
          id: 'c7-eng',
          code: 'C7',
          description: 'Leaves the FOP without permission',
        }
      ],
      playersRedCodes: [
        {
          id: 's1-eng',
          code: 'S1',
          description: 'Serious foul play',
        },
        {
          id: 's2-eng',
          code: 'S2',
          description: 'Violent conduct',
          subcategories: [
            { id: 's2a-eng', code: 'S2A', description: 'Head to head contact' },
            { id: 's2b-eng', code: 'S2B', description: 'Elbowing' },
            { id: 's2c-eng', code: 'S2C', description: 'Kicking' },
            { id: 's2d-eng', code: 'S2D', description: 'Stamping' },
            { id: 's2e-eng', code: 'S2E', description: 'Striking' },
            { id: 's2f-eng', code: 'S2F', description: 'Biting' },
            { id: 's2g-eng', code: 'S2G', description: 'Other' }
          ]
        },
        {
          id: 's3-eng',
          code: 'S3',
          description: 'Spitting',
        },
        {
          id: 's4-eng',
          code: 'S4',
          description: 'Denies goal by hand',
        },
        {
          id: 's5-eng',
          code: 'S5',
          description: 'DOGSO',
        },
        {
          id: 's6-eng',
          code: 'S6',
          description: 'OFFINABUSS',
        },
        {
          id: 's7-eng',
          code: 'S7',
          description: 'Second Caution',
        }
      ],
      officialsYellowCodes: [
        {
          id: 't1-eng',
          code: 'T1',
          description: 'Clearly/persistently not respecting the confines of their team\'s technical area',
        },
        {
          id: 't2-eng',
          code: 'T2',
          description: 'Delaying the restart of play by their team',
        },
        {
          id: 't3-eng',
          code: 'T3',
          description: 'Deliberately entering the technical area of the opposing team (non-confrontational)',
        },
        {
          id: 't4-eng',
          code: 'T4',
          description: 'Dissent by word or action',
        },
        {
          id: 't5-eng',
          code: 'T5',
          description: 'Entering the referee review area (RRA)',
        },
        {
          id: 't6-eng',
          code: 'T6',
          description: 'Excessively showing the TV signal for a VAR \'review\'',
        },
        {
          id: 't7-eng',
          code: 'T7',
          description: 'Excessively/persistently gesturing for a red or yellow card',
        },
        {
          id: 't8-eng',
          code: 'T8',
          description: 'Gestures which show a clear lack of respect for the match officials(s) e.g. sarcastic clapping',
        },
        {
          id: 't9-eng',
          code: 'T9',
          description: 'Gesturing or acting in a provocative or inflammatory manner',
        },
        {
          id: 't10-eng',
          code: 'T10',
          description: 'Persistent unacceptable behaviour (including repeated warning offences)',
        },
        {
          id: 't11-eng',
          code: 'T11',
          description: 'Showing a lack of respect for the game',
        },
        {
          id: 't12-eng',
          code: 'T12',
          description: 'Throwing/kicking drinks bottles or other objects',
        },
        {
          id: 't13-eng',
          code: 'T13',
          description: 'Other',
        }
      ],
      officialsRedCodes: [
        {
          id: 't14-eng',
          code: 'T14',
          description: 'Delaying the restart of play by the opposing team',
        },
        {
          id: 't15-eng',
          code: 'T15',
          description: 'Deliberately leaving the technical area to show dissent or remonstrate with a Match Official',
        },
        {
          id: 't16-eng',
          code: 'T16',
          description: 'Deliberately leaving the technical to act in a provocative or inflammatory manner',
        },
        {
          id: 't17-eng',
          code: 'T17',
          description: 'Deliberately leaving the technical to enter the opposing technical area in an aggressive or confrontational manner',
        },
        {
          id: 't18-eng',
          code: 'T18',
          description: 'Deliberately throwing/kicking an object onto the field of play',
        },
        {
          id: 't19-eng',
          code: 'T19',
          description: 'Entering the field of play to confront a match official (including half time and full-time)',
        },
        {
          id: 't20-eng',
          code: 'T20',
          description: 'Entering the field of play to interfere with play, an opposing player or match official',
        },
        {
          id: 't21-eng',
          code: 'T21',
          description: 'Entering the video operation room (VOR)',
        },
        {
          id: 't22-eng',
          code: 'T22',
          description: 'Physical or aggressive behaviour (including biting and spitting)',
        },
        {
          id: 't23-eng',
          code: 'T23',
          description: 'Receiving a second caution in the same match',
        },
        {
          id: 't24-eng',
          code: 'T24',
          description: 'Using offensive, insulting or abusive language and/or gestures',
        },
        {
          id: 't25-eng',
          code: 'T25',
          description: 'Using unauthorised electronic or communication equipment and/or behaving in an appropriate manner as the result of using such equipment',
        },
        {
          id: 't26-eng',
          code: 'T26',
          description: 'Violent Conduct',
        },
        {
          id: 't27-eng',
          code: 'T27',
          description: 'Other',
        }
      ]
    };
  }

  // Get all available misconduct sets (hardcoded + custom)
  static async getAllMisconductSets(userId?: string): Promise<MisconductSet[]> {
    const hardcodedSets = [
      this.getWalesMisconductSet(),
      this.getEnglandFAMisconductSet()
    ];

    if (!userId) {
      return hardcodedSets;
    }

    try {
      const response = await this.getMisconductSets(userId);
      
      // Handle both ApiResponse format and direct data format
      let userData;
      if (response.success !== undefined) {
        // ApiResponse format
        if (response.success && response.data) {
          userData = response.data;
        } else {
          console.error('Failed to load custom misconduct sets:', response.error);
          return hardcodedSets;
        }
      } else {
        // Direct data format (response is the data itself)
        userData = response;
      }

      const userSets = Array.isArray(userData) ? userData : [];
      return [...hardcodedSets, ...userSets];
    } catch (error) {
      console.error('Error loading custom misconduct sets:', error);
    }

    return hardcodedSets;
  }
}

export default MisconductService;