import { Is<PERSON>rray, IsNotEmpty, IsNumber, IsString, <PERSON>idateNested, <PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';

export class EvaluationAnswerDto {
  @IsNumber()
  @IsNotEmpty()
  questionId: number;

  @IsString()
  @IsNotEmpty()
  question: string;

  @IsNumber()
  @Min(1)
  @Max(5)
  rating: number;

  @IsString()
  @IsNotEmpty()
  label: string;

  @IsNumber()
  @Min(1)
  @Max(5)
  emoji: number;
}

export class CreateMatchEvaluationDto {
  @IsString()
  @IsNotEmpty()
  matchId: string;

  @IsString()
  @IsNotEmpty()
  userId: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EvaluationAnswerDto)
  answers: EvaluationAnswerDto[];
}