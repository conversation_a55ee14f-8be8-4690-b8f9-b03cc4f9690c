import { Module } from '@nestjs/common';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { PrismaService } from '../prisma.service';
import { FirebaseService } from '../auth/firebase.service';

@Module({
  controllers: [UsersController],
  providers: [UsersService, PrismaService, FirebaseService],
  exports: [UsersService],
})
export class UsersModule {}