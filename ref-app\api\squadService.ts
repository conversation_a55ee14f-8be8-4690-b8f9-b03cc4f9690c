import { apiClient } from './client';
import { API_CONFIG, ApiResponse } from './config';

// Squad data interfaces
export interface SquadPlayer {
  id?: string;
  number: number;
  name: string;
  isCaptain: boolean;
  isGoalkeeper: boolean;
  position: 'STARTER' | 'SUBSTITUTE';
}

export interface SquadOfficial {
  id?: string;
  role: string;
  name: string;
}

export interface SquadData {
  id?: string;
  matchId: string;
  teamType: 'HOME' | 'AWAY';
  players: SquadPlayer[];
  officials: SquadOfficial[];
  createdAt?: string;
  updatedAt?: string;
}

export interface SquadResponse extends SquadData {
  id: string;
  createdAt: string;
  updatedAt: string;
}

export class SquadService {
  // Save squad data (create or update)
  static async saveSquad(squadData: SquadData): Promise<ApiResponse<SquadResponse>> {
    if (squadData.id) {
      // Update existing squad
      const { id, ...updateData } = squadData;
      return apiClient.put<SquadResponse>(`${API_CONFIG.ENDPOINTS.MATCHES.BASE}/${squadData.matchId}/squad/${squadData.id}`, updateData);
    } else {
      // Create new squad
      return apiClient.post<SquadResponse>(`${API_CONFIG.ENDPOINTS.MATCHES.BASE}/${squadData.matchId}/squad`, squadData);
    }
  }

  // Get squad data by match ID and team type
  static async getSquadByMatch(matchId: string, teamType: 'home' | 'away'): Promise<ApiResponse<SquadResponse>> {
    const upperTeamType = teamType.toUpperCase();
    return apiClient.get<SquadResponse>(`${API_CONFIG.ENDPOINTS.MATCHES.BASE}/${matchId}/squad/${upperTeamType}`);
  }

  // Delete squad data
  static async deleteSquad(matchId: string, squadId: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`${API_CONFIG.ENDPOINTS.MATCHES.BASE}/${matchId}/squad/${squadId}`);
  }

  // Add squad official
  static async addSquadOfficial(matchId: string, teamType: 'home' | 'away', official: { role: string; name: string }): Promise<ApiResponse<SquadOfficial>> {
    const upperTeamType = teamType.toUpperCase();
    return apiClient.post<SquadOfficial>(`${API_CONFIG.ENDPOINTS.MATCHES.BASE}/${matchId}/squad/${upperTeamType}/officials`, official);
  }

  // Update squad official
  static async updateSquadOfficial(squadId: string, officialId: string, updates: { name?: string; role?: string }): Promise<ApiResponse<SquadOfficial>> {
    return apiClient.patch<SquadOfficial>(`${API_CONFIG.ENDPOINTS.MATCHES.BASE}/squads/${squadId}/officials/${officialId}`, updates);
  }

  // Delete squad official
  static async deleteSquadOfficial(squadId: string, officialId: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`${API_CONFIG.ENDPOINTS.MATCHES.BASE}/squads/${squadId}/officials/${officialId}`);
  }

  // Convert squad screen data to API format
  static convertToApiFormat(
    matchId: string,
    teamType: 'home' | 'away',
    starters: any[],
    substitutes: any[],
    officials: any[],
    existingSquadId?: string
  ): SquadData {
    const players: SquadPlayer[] = [
      ...starters.map(player => ({
        number: player.number,
        name: player.name,
        isCaptain: player.isCaptain,
        isGoalkeeper: player.isGoalkeeper,
        position: 'STARTER' as const
      })),
      ...substitutes.map(player => ({
        number: player.number,
        name: player.name,
        isCaptain: false,
        isGoalkeeper: false,
        position: 'SUBSTITUTE' as const
      }))
    ];

    const squadOfficials: SquadOfficial[] = officials.map(official => ({
      role: official.role,
      name: official.name
    }));

    return {
      id: existingSquadId,
      matchId,
      teamType: teamType.toUpperCase() as 'HOME' | 'AWAY',
      players,
      officials: squadOfficials
    };
  }

  // Convert API response to squad screen format
  static convertFromApiFormat(squadResponse: SquadResponse): {
    starters: any[];
    substitutes: any[];
    officials: any[];
  } {
    const starters = squadResponse.players
      .filter(player => player.position === 'STARTER')
      .map(player => ({
        id: player.id,
        number: player.number,
        name: player.name,
        isCaptain: player.isCaptain,
        isGoalkeeper: player.isGoalkeeper,
        isBeingSwapped: false,
        isCaptainSelectable: false
      }));

    const substitutes = squadResponse.players
      .filter(player => player.position === 'SUBSTITUTE')
      .map(player => ({
        id: player.id,
        number: player.number,
        name: player.name,
        isCaptain: false,
        isGoalkeeper: false,
        isBeingSwapped: false
      }));

    const officials = squadResponse.officials.map(official => ({
      id: official.id,
      role: official.role,
      name: official.name,
      squadId: squadResponse.id,
      isEditing: false,
      tempRole: official.role,
      tempName: official.name
    }));

    return { starters, substitutes, officials };
  }
}

export default SquadService;