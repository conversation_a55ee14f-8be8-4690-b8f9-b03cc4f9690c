import { Module } from '@nestjs/common';
import { MatchesService } from './matches.service';
import { MatchesController } from './matches.controller';
import { PrismaService } from '../prisma.service';
import { FirebaseService } from '../auth/firebase.service';
import { UsersService } from '../users/users.service';

@Module({
  controllers: [MatchesController],
  providers: [MatchesService, PrismaService, FirebaseService, UsersService],
  exports: [MatchesService]
})
export class MatchesModule {}
