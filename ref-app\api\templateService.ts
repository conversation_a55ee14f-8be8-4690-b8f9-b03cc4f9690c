import { apiClient } from './client';
import { ApiResponse } from './config';

// Template interfaces
export interface MatchTemplate {
  id: string;
  name: string;
  userId: string;
  
  // Match format settings
  teamSize: number;
  maxSubstitute: number;
  numberOfPeriods: number;
  periodLengths: number[];
  halfTime: number;
  
  // Extra time settings
  extraTime: boolean;
  extraTimeLengths?: [number, number];
  
  // Substitution rules
  substituteOpportunities: boolean;
  substituteOpportunitiesAllowance: number;
  extraTimeSubOpportunities: boolean;
  extraTimeSubOpportunitiesAllowance: number;
  extraTimeAdditionalSub: boolean;
  extraTimeAdditionalSubAllowance: number;
  rollOnRollOff: boolean;
  
  // Other rules
  penalties: boolean;
  misconductCode: string;
  temporaryDismissals: boolean;
  temporaryDismissalsTime: number;
  
  // Injury time settings
  injuryTimeAllowance: boolean;
  injuryTimeSubs: number;
  injuryTimeSanctions: number;
  injuryTimeGoals: number;
  
  // Timestamps
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateTemplateRequest {
  name: string;
  userId: string;
  teamSize: number;
  maxSubstitute: number;
  numberOfPeriods: number;
  periodLengths: number[];
  halfTime: number;
  extraTime: boolean;
  extraTimeLengths?: [number, number];
  substituteOpportunities: boolean;
  substituteOpportunitiesAllowance: number;
  extraTimeSubOpportunities: boolean;
  extraTimeSubOpportunitiesAllowance: number;
  extraTimeAdditionalSub: boolean;
  extraTimeAdditionalSubAllowance: number;
  rollOnRollOff: boolean;
  penalties: boolean;
  misconductCode: string;
  temporaryDismissals: boolean;
  temporaryDismissalsTime: number;
  injuryTimeAllowance: boolean;
  injuryTimeSubs: number;
  injuryTimeSanctions: number;
  injuryTimeGoals: number;
}

export class TemplateService {
  // Get all templates for a user
  static async getTemplates(userId: string): Promise<ApiResponse<MatchTemplate[]>> {
    return apiClient.get<MatchTemplate[]>(`/templates/${userId}`);
  }

  // Create a new template
  static async createTemplate(templateData: CreateTemplateRequest): Promise<ApiResponse<MatchTemplate>> {
    return apiClient.post<MatchTemplate>('/templates', templateData);
  }

  // Update an existing template
  static async updateTemplate(id: string, updates: Partial<CreateTemplateRequest>): Promise<ApiResponse<MatchTemplate>> {
    return apiClient.patch<MatchTemplate>(`/templates/${id}`, updates);
  }



  // Delete a template
  static async deleteTemplate(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/templates/${id}`);
  }

  // Get all available templates (including hardcoded ones)
  static async getAllTemplates(userId?: string): Promise<MatchTemplate[]> {
    console.log('📦 TemplateService.getAllTemplates called with userId:', userId);
    
    // Hardcoded default templates
    const defaultTemplates: MatchTemplate[] = [
      {
        id: 'default-league',
        name: 'League Standard',
        userId: 'system',
        teamSize: 11,
        maxSubstitute: 5,
        numberOfPeriods: 2,
        periodLengths: [45, 45],
        halfTime: 15,
        extraTime: false,
        substituteOpportunities: true,
        substituteOpportunitiesAllowance: 3,
        extraTimeSubOpportunities: false,
        extraTimeSubOpportunitiesAllowance: 1,
        extraTimeAdditionalSub: false,
        extraTimeAdditionalSubAllowance: 1,
        rollOnRollOff: false,
        penalties: false,
        misconductCode: 'Wales',
        temporaryDismissals: false,
        temporaryDismissalsTime: 10,
        injuryTimeAllowance: true,
        injuryTimeSubs: 30,
        injuryTimeSanctions: 30,
        injuryTimeGoals: 30,
      },
      {
        id: 'default-cup',
        name: 'Cup Final Rules',
        userId: 'system',
        teamSize: 11,
        maxSubstitute: 7,
        numberOfPeriods: 2,
        periodLengths: [45, 45],
        halfTime: 15,
        extraTime: true,
        extraTimeLengths: [15, 15],
        substituteOpportunities: true,
        substituteOpportunitiesAllowance: 5,
        extraTimeSubOpportunities: true,
        extraTimeSubOpportunitiesAllowance: 1,
        extraTimeAdditionalSub: false,
        extraTimeAdditionalSubAllowance: 1,
        rollOnRollOff: false,
        penalties: true,
        misconductCode: 'Wales',
        temporaryDismissals: false,
        temporaryDismissalsTime: 10,
        injuryTimeAllowance: true,
        injuryTimeSubs: 30,
        injuryTimeSanctions: 30,
        injuryTimeGoals: 30,
      },
      {
        id: 'default-youth',
        name: 'Youth League U12',
        userId: 'system',
        teamSize: 9,
        maxSubstitute: 3,
        numberOfPeriods: 2,
        periodLengths: [30, 30],
        halfTime: 10,
        extraTime: false,
        substituteOpportunities: true,
        substituteOpportunitiesAllowance: 5,
        extraTimeSubOpportunities: false,
        extraTimeSubOpportunitiesAllowance: 1,
        extraTimeAdditionalSub: false,
        extraTimeAdditionalSubAllowance: 1,
        rollOnRollOff: false,
        penalties: false,
        misconductCode: 'Wales',
        temporaryDismissals: false,
        temporaryDismissalsTime: 10,
        injuryTimeAllowance: false,
        injuryTimeSubs: 0,
        injuryTimeSanctions: 0,
        injuryTimeGoals: 0,
      }
    ];

    console.log('📋 Default templates loaded:', defaultTemplates.length);

    if (!userId) {
      console.log('⚠️ No userId provided, returning only default templates');
      return defaultTemplates;
    }

    try {
      console.log('📡 Making API call to get user templates for userId:', userId);
      const response = await this.getTemplates(userId);
      console.log('📡 API response received:', response);
      
      // Handle both ApiResponse format and direct data format
      let userData;
      if (response.success !== undefined) {
        // ApiResponse format
        console.log('📦 ApiResponse format detected, success:', response.success);
        if (response.success && response.data) {
          userData = response.data;
          console.log('✅ User data extracted from ApiResponse:', userData);
        } else {
          console.error('❌ Failed to load templates from API:', response.error);
          return defaultTemplates;
        }
      } else {
        // Direct data format (response is the data itself)
        console.log('📦 Direct data format detected');
        userData = response;
      }

      const userTemplates = Array.isArray(userData) ? userData : [];
      console.log('📋 User templates extracted:', userTemplates.length, 'templates');
      
      const allTemplates = [...defaultTemplates, ...userTemplates];
      console.log('✅ Total templates to return:', allTemplates.length);
      return allTemplates;
    } catch (error) {
      console.error('❌ Error loading templates:', error);
      console.log('🔄 Returning default templates due to error');
    }

    return defaultTemplates;
  }

  // Convert form data to template format
  static convertFormDataToTemplate(formData: any, templateName: string, userId: string): CreateTemplateRequest {
    return {
      name: templateName,
      userId: userId,
      teamSize: formData.teamSize || 11,
      maxSubstitute: formData.maxSubstitute || 5,
      numberOfPeriods: formData.numberOfPeriods || 2,
      periodLengths: formData.periodLengths || [45, 45],
      halfTime: formData.halfTime || 15,
      extraTime: formData.extraTime === 'Yes',
      extraTimeLengths: formData.extraTime === 'Yes' ? formData.extraTimeLengths : undefined,
      substituteOpportunities: formData.SubstituteOpportunities === 'Yes',
      substituteOpportunitiesAllowance: formData.SubstituteOpportunitiesAllowance || 1,
      extraTimeSubOpportunities: formData.extraTimeSubOpportunities === 'Yes',
      extraTimeSubOpportunitiesAllowance: formData.extraTimeSubOpportunitiesAllowance || 1,
      extraTimeAdditionalSub: formData.extraTimeAdditionalSub === 'Yes',
      extraTimeAdditionalSubAllowance: formData.extraTimeAdditionalSubAllowance || 1,
      rollOnRollOff: formData.rollOnRollOff === 'Yes',
      penalties: formData.penalties === 'Yes',
      misconductCode: formData.misconductCode || 'Wales',
      temporaryDismissals: formData.temporaryDismissals === 'Yes',
      temporaryDismissalsTime: formData.temporaryDismissalsTime || 10,
      injuryTimeAllowance: formData.injuryTimeAllowance === 'Yes',
      injuryTimeSubs: formData.injuryTime?.subs || 0,
      injuryTimeSanctions: formData.injuryTime?.sanctions || 0,
      injuryTimeGoals: formData.injuryTime?.goals || 0,
    };
  }

  // Apply template to form data
  static applyTemplateToForm(template: MatchTemplate): any {
    return {
      teamSize: template.teamSize,
      maxSubstitute: template.maxSubstitute,
      numberOfPeriods: template.numberOfPeriods,
      periodLengths: template.periodLengths,
      halfTime: template.halfTime,
      extraTime: template.extraTime ? 'Yes' : 'No',
      extraTimeLengths: template.extraTimeLengths || [15, 15],
      SubstituteOpportunities: template.substituteOpportunities ? 'Yes' : 'No',
      SubstituteOpportunitiesAllowance: template.substituteOpportunitiesAllowance,
      extraTimeSubOpportunities: template.extraTimeSubOpportunities ? 'Yes' : 'No',
      extraTimeSubOpportunitiesAllowance: template.extraTimeSubOpportunitiesAllowance,
      extraTimeAdditionalSub: template.extraTimeAdditionalSub ? 'Yes' : 'No',
      extraTimeAdditionalSubAllowance: template.extraTimeAdditionalSubAllowance,
      rollOnRollOff: template.rollOnRollOff ? 'Yes' : 'No',
      penalties: template.penalties ? 'Yes' : 'No',
      misconductCode: template.misconductCode,
      temporaryDismissals: template.temporaryDismissals ? 'Yes' : 'No',
      temporaryDismissalsTime: template.temporaryDismissalsTime,
      injuryTimeAllowance: template.injuryTimeAllowance ? 'Yes' : 'No',
      injuryTime: {
        subs: template.injuryTimeSubs,
        sanctions: template.injuryTimeSanctions,
        goals: template.injuryTimeGoals,
      },
    };
  }
}

export default TemplateService;