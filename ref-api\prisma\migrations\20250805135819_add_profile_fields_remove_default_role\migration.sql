-- CreateEnum
CREATE TYPE "Gender" AS ENUM ('MALE', 'FEMALE', 'OTHER', 'PREFER_NOT_TO_SAY');

-- CreateEnum
CREATE TYPE "RefereeLevel" AS ENUM ('LEVEL_1', 'LEVEL_2', 'LEVEL_3', 'LEVEL_4', 'LEVEL_5', 'LEVEL_6', 'LEVEL_7', 'LEVEL_8', 'LEVEL_9', 'NATIONAL', 'FIFA');

-- AlterTable
ALTER TABLE "matches" ALTER COLUMN "officialRole" DROP DEFAULT;

-- AlterTable
ALTER TABLE "users" ADD COLUMN     "country" TEXT,
ADD COLUMN     "countryFA" TEXT,
ADD COLUMN     "gender" "Gender",
ADD COLUMN     "height" DOUBLE PRECISION,
ADD COLUMN     "refereeLevel" "RefereeLevel",
ADD COLUMN     "username" TEXT,
ADD COLUMN     "weight" DOUBLE PRECISION;
